import 'package:broker_settings/src/domain/enums/broker_settings_enum.dart';
import 'package:broker_settings/src/domain/model/broker_settings_response.dart';
import 'package:broker_settings/src/domain/repository/broker_settings_repository.dart';
import 'package:prelude/prelude.dart';
import 'package:user_account/user_account.dart';

class GetBrokerCurrenciesUseCase {
  final BrokerSettingsRepository repository;
  final GetBrokerIdUseCase getBrokerIdUseCase;

  const GetBrokerCurrenciesUseCase({
    required this.repository,
    required this.getBrokerIdUseCase,
  });
  TaskEither<Exception, List<AccountCurrency>?> call({
    BrokerSettingsType brokerSettingsType = BrokerSettingsType.initialAccount,
  }) {
    final brokerId = getBrokerIdUseCase();
    if (brokerId == null) {
      return TaskEither.left(Exception('Broker ID not found'));
    }
    return repository
        .getBrokerSettingsData(
          brokerId: brokerId,
          brokerSettingsType: brokerSettingsType,
        )
        .map((response) => response.data.allCurrencies);
  }
}
