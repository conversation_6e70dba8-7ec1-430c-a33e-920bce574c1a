import 'package:api_client/api_client.dart';
import 'package:clock/clock.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:equiti_secure_storage/equiti_secure_storage.dart';
import 'package:feature_flags/feature_flags.dart';
import 'package:flutter/widgets.dart';
import 'package:injectable/injectable.dart';
import 'package:locale_manager/locale_manager.dart';
import 'package:login/login.dart';
import 'package:login/src/di/di_container.dart';
import 'package:monitoring/monitoring.dart';
import 'package:theme_manager/theme_manager.dart';

import '../mocks/auth_service_mock.dart';
import '../mocks/common_flags_mock.dart';
import '../mocks/equiti_secure_mock.dart';
import '../mocks/locale_manager_mock.dart';
import '../mocks/login_navigation_mock.dart';
import '../mocks/theme_manager_mock.dart';
import '../mocks/token_manager_mock.dart';

Future<void> setupDi() async {
  final gh = GetItHelper(diContainer);
  gh.lazySingleton(() => MockApiInterceptor());
  gh.lazySingleton(
    () =>
        DioBuilder()
            .setBaseUrl('http://equiti-platform.com/')
            .addInterceptor(gh<PrettyDioLogger>())
            .addInterceptor(gh<MockApiInterceptor>())
            .withNativeAdapter()
            .withReporter(),
  );

  gh.lazySingleton<ApiClientBase>(
    instanceName: "mobileBffApiClient",
    () => DioApiClient(gh<DioBuilder>().build()),
  );

  gh.lazySingleton<AuthService>(() => AuthServiceMock());

  gh.lazySingleton<ApiClientBase>(() => DioApiClient(gh<DioBuilder>().build()));
  gh.lazySingleton<ThemeManager>(() => ThemeManagerMock());

  gh.lazySingleton<SecureStorage>(() => EquitiSecureMock());
  await MonitoringPackageModule().init(gh);
  gh.lazySingleton(() => GlobalKey<NavigatorState>());
  gh.lazySingleton(() => Clock());
  gh.lazySingleton<LoginNavigation>(() => LoginNavigationMock());
  gh.lazySingleton<TokenManager>(() => TokenManagerMock());
  gh.lazySingleton<LocaleManager>(() => LocaleManagerMock());
  gh.lazySingleton<CommonFlags>(() => CommonFlagsMock());

  await LoginPackageModule().init(gh);
  await DuploPackageModule().init(gh);
  await EquitiAnalyticsPackageModule().init(gh);
}
