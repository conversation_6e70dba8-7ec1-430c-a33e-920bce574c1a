import 'package:equiti_secure_storage/equiti_secure_storage.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:prelude/prelude.dart';

class LogoutRepository {
  final SecureStorage secureStorage;
  final AuthService Function() authService;

  const LogoutRepository({
    required this.secureStorage,
    required this.authService,
  });

  TaskEither<Exception, void> logout() {
    return authService().logout().flatMap((r) {
      return TaskEither.tryCatch(() async {
        await secureStorage.clear();
        print("SecureStorage clear");
        print("OKTA logout");
      }, (error, stackTrace) => Exception(error.toString()));
    });
  }
}
