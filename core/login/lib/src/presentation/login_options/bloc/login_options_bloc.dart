import 'dart:async';
import 'dart:io';

import 'package:api_client/api_client.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:login/src/data/api/check_email_response.dart';
import 'package:login/src/domain/error_code.dart';
import 'package:login/src/domain/usecase/check_email_usecase.dart';
import 'package:login/src/domain/usecase/login_with_okta_usecase.dart';
import 'package:login/src/domain/usecase/login_with_uae_pass_usecase.dart';
import 'package:login/src/navigation/login_navigation.dart';
import 'package:prelude/prelude.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:validator/validator.dart';

part 'login_options_bloc.freezed.dart';
part 'login_options_event.dart';
part 'login_options_state.dart';

class LoginOptionsBloc extends Bloc<LoginOptionsEvent, LoginOptionsState> {
  final LoginNavigation _loginNavigation;
  final LoginWithOktaUsecase _loginWithOktaUsecase;
  final CheckEmailUseCase _checkEmailUseCase;
  final LoginWithUaePassUsecase _loginWithUaePassUsecase;
  final AnalyticsService _analyticsService;

  LoginOptionsBloc(
    this._loginNavigation,
    this._loginWithOktaUsecase,
    this._checkEmailUseCase,
    this._loginWithUaePassUsecase,
    this._analyticsService,
  ) : super(const LoginOptionsState()) {
    on<_NavigateToLogin>(_navigateToLogin);
    on<_NavigateToSignup>(_navigateToSignup);
    on<_NavigateToOldAppStore>(_navigateToOldAppStore);
    on<_ValidateEmail>(_validateEmail);
    on<_LoginWithUaePass>(_loginWithUaePass);
    on<_BottomSheetClosed>(_bottomSheetClosed);
  }

  FutureOr<void> _navigateToLogin(
    _NavigateToLogin event,
    Emitter<LoginOptionsState> emit,
  ) async {
    final result =
        await TaskEither<Exception, void>.Do(($) async {
          // Set process state to loading
          if (!isClosed) {
            emit(
              state.copyWith(processState: LoginOptionsProcessState.loading()),
            );
          }

          _analyticsService.sendEvent(
            eventType: OnboardingAnalyticsEvent.checkEmailStart.eventType.name,
            eventName: OnboardingAnalyticsEvent.checkEmailStart.eventName,
            metadata: {'email': event.email},
          );

          // Check if email exists
          final checkEmailData = await $(
            _checkEmailUseCase(email: event.email),
          );

          _analyticsService.sendEvent(
            eventType:
                OnboardingAnalyticsEvent.checkEmailSuccess.eventType.name,
            eventName: OnboardingAnalyticsEvent.checkEmailSuccess.eventName,
            metadata: {
              'email': event.email,
              'migrationStatus': checkEmailData.migration_status?.name,
            },
          );

          if (!checkEmailData.isExist && !isClosed) {
            // Set process state to email does not exist
            emit(
              state.copyWith(
                processState: LoginOptionsProcessState.emailDoesNotExist(),
              ),
            );
            return;
          }

          if (checkEmailData.isExist &&
              checkEmailData.migration_status == MigrationStatus.notMigrated &&
              !isClosed) {
            emit(
              state.copyWith(
                processState: LoginOptionsProcessState.emailRelatedToOldApp(),
              ),
            );
            return;
          }

          // Login with Okta if email exists
          await $(_loginWithOktaUsecase(event.email));

          if (!isClosed) {
            // Set process state to initial
            emit(
              state.copyWith(processState: LoginOptionsProcessState.initial()),
            );
          }

          _analyticsService.sendEvent(
            eventType: OnboardingAnalyticsEvent.loginSuccess.eventType.name,
            eventName: OnboardingAnalyticsEvent.loginSuccess.eventName,
            metadata: {'channel': 'okta'},
          );
          // ignore: avoid-missing-enum-constant-in-map
          _analyticsService.setGlobalAttributes({
            AnalyticsGlobalAttributes.hashEmail: event.email.hash(),
            AnalyticsGlobalAttributes.email: event.email,
          });
          // Navigate to progress tracker
          _loginNavigation.goToProgressTracker();
        }).run();

    result.fold(
      (Exception exception) {
        // todo (aakash): Handle user_cancelled error properly
        print('Operation failed: $exception');
        _analyticsService.sendEvent(
          eventType: OnboardingAnalyticsEvent.checkEmailError.eventType.name,
          eventName: OnboardingAnalyticsEvent.checkEmailError.eventName,
          metadata: {'email': event.email},
        );
        _analyticsService.sendEvent(
          eventType: OnboardingAnalyticsEvent.loginOktaError.eventType.name,
          eventName: OnboardingAnalyticsEvent.loginOktaError.eventName,
        );
        if (!isClosed) {
          emit(
            state.copyWith(processState: LoginOptionsProcessState.initial()),
          );
        }
      },
      (_) => <String, dynamic>{}, // Success case is handled within the Do block
    );
  }

  FutureOr<void> _navigateToOldAppStore(
    _NavigateToOldAppStore event,
    Emitter<LoginOptionsState> emit,
  ) {
    String storeUrl =
        "https://apps.apple.com/us/app/equiti-trader/id6443657013";
    if (Platform.isAndroid) {
      storeUrl =
          "https://play.google.com/store/apps/details?id=com.equiti.trader.app";
    }

    final uri = Uri.parse(storeUrl);
    launchUrl(uri, mode: LaunchMode.externalApplication);
  }

  FutureOr<void> _navigateToSignup(
    _NavigateToSignup event,
    Emitter<LoginOptionsState> emit,
  ) {
    _loginNavigation.goToCountrySelectorPage();
  }

  FutureOr<void> _loginWithUaePass(
    _LoginWithUaePass event,
    Emitter<LoginOptionsState> emit,
  ) async {
    if (!isClosed) {
      emit(state.copyWith(processState: LoginOptionsProcessState.loading()));
    }
    final result = await _loginWithUaePassUsecase().run();
    result.fold(
      (exception) {
        _analyticsService.sendEvent(
          eventType: OnboardingAnalyticsEvent.loginUaePassError.eventType.name,
          eventName: OnboardingAnalyticsEvent.loginUaePassError.eventName,
        );
        // Handle UAE Pass exceptions
        if (exception is UaePassException) {
          switch (exception.code) {
            case UaePassExceptionCode.cancelled_by_user:
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: LoginOptionsProcessState.userCancelled(),
                  ),
                );
              }
              break;
            case UaePassExceptionCode.unknown:
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: LoginOptionsProcessState.error(
                      exception.message,
                    ),
                  ),
                );
              }
              break;
          }
        }
        // Handle token exchange api exceptions
        if (exception is ClientException) {
          final errorCode = exception.mobileBffBaseError?.errorCode;
          switch (errorCode) {
            case ErrorCode.USER_SIGNED_UP_WITH_DIFFERENT_CHANNEL:
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState:
                        LoginOptionsProcessState.userSignedUpWithDifferentChannel(),
                  ),
                );
              }
              break;
            case ErrorCode.USER_NOT_SIGNED_UP_WITH_UAE_PASS:
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState:
                        LoginOptionsProcessState.userNotSignedUpWithUaePass(),
                  ),
                );
              }
              break;
            default:
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: LoginOptionsProcessState.error(
                      exception.message,
                    ),
                  ),
                );
              }
          }
        }
      },
      (authResult) {
        _analyticsService.sendEvent(
          eventType: OnboardingAnalyticsEvent.loginSuccess.eventType.name,
          eventName: OnboardingAnalyticsEvent.loginSuccess.eventName,
          metadata: {'channel': 'uaepass'},
        );
        _loginNavigation.goToProgressTracker();
      },
    );
  }

  FutureOr<void> _validateEmail(
    _ValidateEmail event,
    Emitter<LoginOptionsState> emit,
  ) {
    if (!isClosed) {
      InputValidator validator = InputValidator();
      final isEmailValid =
          validator.validate(event.email).email().run().isEmpty;
      emit(state.copyWith(isButtonEnabled: isEmailValid));
    }
  }

  FutureOr<void> _bottomSheetClosed(
    _BottomSheetClosed event,
    Emitter<LoginOptionsState> emit,
  ) {
    if (!isClosed) {
      emit(state.copyWith(processState: LoginOptionsProcessState.initial()));
    }
  }
}
