{"@@locale": "en", "@@prefix_key": "payments", "payments_funding": "Funding", "@payments_funding": {}, "payments_fee": "Fee", "@payments_fee": {}, "payments_more": "more", "@payments_more": {}, "payments_deposit": "<PERSON><PERSON><PERSON><PERSON>", "payments_depositAmount": " <PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "@payments_depositAmount": {}, "payments_amount": "Amount", "@payments_amount": {}, "payments_transactionLimit": "Transaction limit: ", "@payments_transactionLimit": {}, "payments_transferAmount": "Transfer Amount", "@payments_transferAmount": {}, "payments_addAmount": "Add Amount", "@payments_addAmount": {}, "payments_convertedAmount": "Converted Amount", "@payments_convertedAmount": {}, "payments_toBeDeposited": "To be Deposited", "@payments_toBeDeposited": {}, "payments_withdrawalAmount": "<PERSON><PERSON><PERSON> Amount", "@payments_withdrawalAmount": {}, "payments_selectCurrency": "Select Currency", "@payments_selectCurrency": {}, "payments_accounts": "Accounts", "payments_wallets": "Wallets", "payments_withdraw_policy_first_text": "<PERSON><PERSON><PERSON> follows a strict return to source policy", "payments_withdraw_policy_second_text": "In all instances where applicable, all monies paid out by <PERSON><PERSON><PERSON> will be paid back to the source from where they originated from.", "payments_deposit_with": "Deposit with", "payments_deposit_to_account": "Deposit to account", "payments_deposit_select_trading_account": "Select a trading account or a wallet to deposit your money", "payments_withdraw": "<PERSON><PERSON><PERSON>", "payments_withdraw_with": "Withdraw with", "payments_withdraw_from_account": "<PERSON><PERSON><PERSON> from Account", "payments_withdraw_select_trading_account": "Select a trading account or a wallet to withdraw your money from", "payments_depositBankTransfer": "Deposit with bank transfer", "payments_selectBank": "Select Bank", "payments_allCurrencies": "All Currencies", "payments_swift_bic_code": "SWIFT/ BIC Code", "payments_accountName": "Account Name", "payments_accountAddress": "Account Address", "payments_reference": "Reference", "payments_purposeOfPayment": "Purpose of Payment", "payments_accountInformation": "Account Information", "payments_ensureNameAndAccountUse": "Please ensure you use your full name and <PERSON><PERSON>ti account number as a reference.", "payments_iban": "IBAN", "payments_accountNumber": "Account Number", "payments_depositedViaBankTransfer": "I've deposited via bank transfer", "payments_copyAll": "Copy all", "payments_saveAsPDF": "Save as PDF", "payments_withdrawal_request_submitted": "<PERSON><PERSON>wal request submitted", "@payments_withdrawal_request_submitted": {}, "payments_withdrawal_successful": "<PERSON><PERSON><PERSON> successful", "payments_withdrawal_successful_description": "Your withdrawal has been approved and the funds are on their way. Please allow some time for them to reach your account.", "payments_withdrawal_rejected": "<PERSON><PERSON><PERSON> rejected", "payments_withdrawal_rejected_description": "There was an issue with your withdrawal request and it was not approved. Please review your details and try again.", "payments_withdrawal_error": "<PERSON><PERSON><PERSON> error", "payments_withdrawal_error_description": "Something went wrong and your withdrawal could not be completed. Please try again later or contact support if the issue persists.", "payments_tryAgain": "Try again", "payments_withdrawal_request_submitted_description": "Thank you for your withdrawal request, we are now processing your transaction and will let you know when it has been approved.", "@payments_withdrawal_request_submitted_description": {}, "payments_continue": "Continue", "@payments_continue": {}, "payments_withdraw_to_your_bank_account": "Withdraw to your bank account", "@payments_withdraw_to_your_bank_account": {}, "payments_submit_form_description": "Upon submitting this form, you will be requested to upload a copy of your bank statement. You will only need to do this once for every bank account you register.", "@payments_submit_form_description": {}, "payments_bank_information": "Bank information", "@payments_bank_information": {}, "payments_account_information": "Account information", "@payments_account_information": {}, "payments_location_of_bank": "Location of bank", "@payments_location_of_bank": {}, "payments_name_of_account_holder": "Name of account holder", "@payments_name_of_account_holder": {}, "payments_bank_name": "Bank name", "@payments_bank_name": {}, "payments_branch_name": "Branch name", "@payments_branch_name": {}, "payments_swift_bic": "SWIFT BIC", "@payments_swift_bic": {}, "payments_account_number_iban": "Account number / IBAN", "@payments_account_number_iban": {}, "payments_account_nickname": "Account nickname", "@payments_account_nickname": {}, "payments_account_nickname_description": "The name that will appear on the withdrawal page next time you want to withdraw funds. You can store up to 3 bank accounts.", "@payments_account_nickname_description": {}, "payments_transfer_type": "Transfer type", "@payments_transfer_type": {}, "payments_request_withdrawal": "Request withdrawal", "@payments_request_withdrawal": {}, "payments_withdrawal_details": "Withdrawal details", "@payments_withdrawal_details": {}, "payments_withdraw_to_your_bank_Account": "Withdraw to your bank account", "@payments_withdraw_to_your_bank_Account": {}, "payments_saved_bank_accounts": "Saved Bank Accounts", "@payments_saved_bank_accounts": {}, "payments_add_new_bank_account": "Add new account", "@payments_add_new_bank_account": {}, "payments_choose_transfer_type": "Choose transfer type", "payments_bank_details": "Bank Details", "payments_no_accounts_found": "No accounts found", "payments_no_accounts_found_description": "Looks like you don’t have any saved bank accounts, add a new one.", "payments_no_wallets_found": "No wallets found", "payments_add_new_account": "Add new account", "payments_verifyWithdrawalMethod": "Verifying your withdrawal method", "payments_checkingPreviousDepositMethod": "We’re checking if you’ve previously deposited using this method.", "payments_sorry": "Sorry!", "payments_didNotDepositedUsingThisMethod": "You did not deposit funds using this method", "payments_changeWithdrawalMethod": "Change withdrawal method", "payments_paymentMethodAccounts": "{paymentMethod} Accounts", "payments_selectAccountToWithdrawTo": "Select the {paymentMethod} account you would like to withdraw your funds to.", "payments_withdraw_fees_idle": "Add an amount to calculate withdrawal fees (if applicable)", "@payments_withdraw_fees_idle": {"description": "Message shown when user needs to enter amount or select transfer type to calculate fees"}, "payments_withdraw_fees_loading": "Calculating withdrawal fees...", "@payments_withdraw_fees_loading": {"description": "Loading message while calculating withdrawal fees"}, "payments_withdraw_fees_zero": "No fees will be deducted for this withdrawal.", "@payments_withdraw_fees_zero": {"description": "Message when no fees will be charged for the withdrawal"}, "payments_withdraw_fees_non_zero_bank1": "<PERSON><PERSON><PERSON> will incur a ", "payments_withdraw_fees_non_zero_bank2": " transfer fee.", "payments_withdraw_fees_non_zero": "{formattedFeesAndAmount} will be deducted (includes {formattedFees} {currency} fee). You'll receive {formattedAmount} {currency}.", "@payments_withdraw_fees_non_zero": {"description": "Message showing withdrawal fees and total amount", "placeholders": {"currency": {"type": "String", "description": "The currency code"}, "formattedFees": {"type": "String", "description": "The fee on amount"}, "formattedAmount": {"type": "String", "description": "The amount needs to be withdrawn"}}}, "payments_expandable_info_transfer_type": "Choose a transfer type to calculate withdrawal fees (if applicable)", "@payments_expandable_info_transfer_type": {"description": "Title for expandable info view asking user to choose transfer type"}, "payments_locationOfBank": "Location of bank", "payments_nameOfAccountHolder": "Name of account holder", "payments_bankName": "Bank name", "payments_branchName": "Branch name", "payments_swiftBic": "SWIFT BIC", "payments_accountNumberIban": "Account number / IBAN", "payments_goBack": "Go back", "payments_maximumAccountsSaved": "Maximum accounts saved", "@payments_maximumAccountsSaved": {"description": "Title shown when user has reached maximum saved accounts limit"}, "payments_accountLimitDescription": "You can only keep 3 bank accounts at a time. Delete one of your existing accounts to add a new one.", "@payments_accountLimitDescription": {"description": "Description explaining account limit and how to add new accounts"}, "payments_manageAccounts": "Manage accounts", "@payments_manageAccounts": {"description": "Button text to navigate to account management screen"}, "payments_cancel": "Cancel", "@payments_cancel": {"description": "Button text to cancel the current action"}, "payments_delete_bank_account_title": "Delete bank account?", "@payments_delete_bank_account_title": {"description": "Title for delete bank account confirmation dialog"}, "payments_delete_bank_account_description": "Removing this account will permanently erase its details and any scheduled transfers linked to it. You can add the account again later if needed.", "@payments_delete_bank_account_description": {"description": "Description explaining consequences of deleting bank account"}, "payments_account_nickname_label": "Account Nickname", "@payments_account_nickname_label": {"description": "Label for account nickname field"}, "payments_account_number_iban_label": "Account number / IBAN", "@payments_account_number_iban_label": {"description": "Label for account number and IBAN field"}, "payments_delete_account_button": "Delete account", "@payments_delete_account_button": {"description": "Button text to confirm account deletion"}, "payments_something_went_wrong": "Something went wrong", "@payments_something_went_wrong": {"description": "Generic error title when an operation fails"}, "payments_could_not_delete_account": "We could not delete your account.", "@payments_could_not_delete_account": {"description": "Error message when account deletion fails"}, "payments_dismiss": "<PERSON><PERSON><PERSON>", "@payments_dismiss": {"description": "<PERSON><PERSON> text to dismiss error dialog"}, "payments_fee_idle_text": "Choose a transfer type to calculate withdrawal fees (if applicable)", "payments_fee_loading_text": "Calculating withdrawal fees..", "payments_fee_zero_fees_text": "No fees will be deducted for this withdrawal.", "payments_fee_non_zero_fees_text": "Withdrawal will incur a {formattedFees} {currency} transfer fee.", "payments_submit_documents_button": "Submit documents", "payments_bank_statement_required_title": "Bank Statement required", "payments_upload_doc_description": "We need you to send us supporting documents to ensure the withdrawal is made in accordance with regulations.", "payments_upload_doc_file_formats_note": "JPG, PDF, TIFF, PNG (max. 10MB)", "payments_upload_doc_recent_statement_note": "All statements should be dated within the last 3 months.", "payments_upload_doc_tap_to_upload": "Tap to upload", "payments_upload_doc_uploading": "Uploading...", "payments_upload_doc_success": "Upload successful", "payments_upload_doc_failure": "Upload failed", "payments_file_size_limit_exceeded": "File Size Limit Exceeded", "@payments_file_size_limit_exceeded": {"description": "Title for error message when uploaded files exceed size limit"}, "payments_file_size_limit_exceeded_description": "The following files exceed the 10MB limit and were not uploaded: {rejectedFileNames}. Please select files smaller than 10MB.", "@payments_file_size_limit_exceeded_description": {"description": "Description for error message when uploaded files exceed size limit", "placeholders": {"rejectedFileNames": {"type": "String", "description": "List of rejected file names with their sizes"}}}, "payments_max_files_reached": "Max files reached", "@payments_max_files_reached": {"description": "Title for error message when maximum number of files is reached"}, "payments_max_files_reached_description": "You can only upload up to 5 files.", "@payments_max_files_reached_description": {"description": "Description for error message when maximum number of files is reached"}, "payments_leave_confirmation_title": "Are you sure you want to leave?", "@payments_leave_confirmation_title": {"description": "Title asking user to confirm if they want to leave the payment process"}, "payments_leave_confirmation_message": "You're in the middle of a transaction. If you exit now, your progress may be lost and the process might not complete successfully.", "@payments_leave_confirmation_message": {"description": "Warning message about leaving during a transaction"}, "payments_stay_and_finish": "Stay and finish", "@payments_stay_and_finish": {"description": "Button text to continue with the payment process"}, "payments_leave_anyway": "Leave anyway", "@payments_leave_anyway": {"description": "<PERSON><PERSON> text to leave the payment process despite the warning"}, "payments_amount_validation_minimum": "Minimum amount is {minAmount} {currency}", "@payments_amount_validation_minimum": {"description": "Error message when entered amount is below minimum limit", "placeholders": {"minAmount": {"type": "String", "description": "The minimum amount formatted as currency"}, "currency": {"type": "String", "description": "The currency code"}}}, "payments_amount_validation_maximum": "Maximum amount is {maxAmount} {currency}", "@payments_amount_validation_maximum": {"description": "Error message when entered amount is above maximum limit", "placeholders": {"maxAmount": {"type": "String", "description": "The maximum amount formatted as currency"}, "currency": {"type": "String", "description": "The currency code"}}}, "payments_amount_validation_exceeded_limit": "You have exceeded the allowed limit", "@payments_amount_validation_exceeded_limit": {"description": "Generic error message for amount validation failures"}, "payments_transactionStillProcessing_title": "Transaction is still processing", "payments_transactionStillProcessing_description": "Please check your Funding Activity for confirmation your deposit has been successful.", "payments_transactionSuccess_title": "Transaction successful", "payments_transactionSuccess_description": "The amount has been updated in your account or wallet.", "payments_makeAnotherDeposit": "Make another deposit", "payments_transactionSubmitted_title": "Deposit request submitted", "payments_transactionSubmitted_description": "Your deposit request is pending verification. Once reviewed and confirmed you will receive the notification by email, and the funds will be put onto you designated account.", "payments_transactionDeclined_title": "Transaction declined", "payments_transactionDeclined_description": "Something went wrong with your payment. You can try again or switch to another payment method.", "payments_transactionDeclined_blocked_title": "Transaction blocked", "@payments_transactionDeclined_blocked_title": {"description": "Title for transaction declined due to blocked card/account"}, "payments_transactionDeclined_blocked_description": "Your request was unsuccessful. Please try again or change your deposit method.", "@payments_transactionDeclined_blocked_description": {"description": "Description for transaction declined due to blocked card/account"}, "payments_transactionDeclined_insufficientFunds_title": "Insufficient funds", "@payments_transactionDeclined_insufficientFunds_title": {"description": "Title for transaction declined due to insufficient funds"}, "payments_transactionDeclined_insufficientFunds_description": "There are insufficient funds in your account. Please top up, change the amount or try a new card or payment method.", "@payments_transactionDeclined_insufficientFunds_description": {"description": "Description for transaction declined due to insufficient funds"}, "payments_transactionDeclined_timedOut_title": "Transaction failed", "@payments_transactionDeclined_timedOut_title": {"description": "Title for transaction declined due to timeout"}, "payments_transactionDeclined_timedOut_description": "Something went wrong with your payment. You can try again or switch to another payment method.", "@payments_transactionDeclined_timedOut_description": {"description": "Description for transaction declined due to timeout"}, "payments_transactionDeclined_expiredCard_title": "Your card has expired", "@payments_transactionDeclined_expiredCard_title": {"description": "Title for transaction declined due to expired card"}, "payments_transactionDeclined_expiredCard_description": "Please add a new card to deposit, or change your deposit method.", "@payments_transactionDeclined_expiredCard_description": {"description": "Description for transaction declined due to expired card"}, "payments_changePaymentMethod": "Change deposit method", "payments_transactionError_title": "Something went wrong", "payments_transactionError_description": "We couldn't complete the transaction.", "payments_tryAnotherMethod": "Try another deposit method", "payments_deposit_error_minimum_amount": "Please enter an amount above the minimum deposit", "@payments_deposit_error_minimum_amount": {"description": "Error message when deposit amount is below minimum"}, "payments_deposit_error_validation": "Please enter a valid deposit amount", "@payments_deposit_error_validation": {"description": "Error message for deposit validation errors"}, "payments_deposit_error_unknown": "An unknown error occurred", "@payments_deposit_error_unknown": {"description": "Error message for unknown deposit errors"}, "payments_insufficient_funds_title": "Insufficient Funds", "@payments_insufficient_funds_title": {"description": "Title for insufficient funds error message"}, "payments_insufficient_funds_with_fees": "Insufficient funds. Available balance: {availableBalance} {currency}, Required: {requiredAmount} {currency} (including {fees} {currency} fee)", "@payments_insufficient_funds_with_fees": {"description": "Error message when withdrawal amount plus fees exceeds account balance", "placeholders": {"fees": {"type": "String", "description": "The fee amount formatted as currency"}, "currency": {"type": "String", "description": "The currency code"}}}, "payments_insufficient_funds_without_fees": "Insufficient funds. Available balance: {availableBalance} {currency}, Required: {requiredAmount} {currency}", "@payments_insufficient_funds_without_fees": {"description": "Error message when withdrawal amount exceeds account balance (no fees)", "placeholders": {"availableBalance": {"type": "String", "description": "The available account balance formatted as currency"}, "requiredAmount": {"type": "String", "description": "The required withdrawal amount formatted as currency"}, "currency": {"type": "String", "description": "The currency code"}}}, "payments_source_account_title": "Source account or wallet", "payments_destination_account_title": "Destination account or wallet", "payments_destination_account_subtext": "Select the account where you'd like to receive the funds.", "payments_withdraw_to_bank_card": "Withdraw to your bank card", "@payments_withdraw_to_bank_card": {"description": "Title for the withdraw to bank card page"}, "payments_my_cards": "My cards", "@payments_my_cards": {"description": "Section title for user's saved cards"}, "payments_transfer": "Transfer", "@payments_transfer": {"description": "Title for transfer funds page"}, "payments_source_account": "Source account", "@payments_source_account": {"description": "Label for source account selection"}, "payments_select_source_account": "Select the account you'd like to use as the source of funds.", "@payments_select_source_account": {"description": "Description text for source account selection"}, "payments_noLocalBankFeesApply": "No local transfer fees. International fees apply.", "payments_downloaded": "Downloaded", "payments_bank_account_deleted": "Bank account deleted", "payments_bank_account_removed_description": "The bank account has been removed. You can add a new account at any time.", "payments_new_bank_account": "Add new bank account", "payments_saved_accounts": "Saved accounts", "payments_transferPending": "Transfer pending", "payments_transferSuccessful": "Transfer successful!", "payments_transferRejected": "Transfer rejected", "payments_transferError": "Transfer error", "payments_transferPendingDescription": "Please check your notifications or Funding Activity for confirmation your transfer has been successful.", "payments_transferRejectedDescription": "We're sorry. After reviewing your request, we're unable to proceed with this transfer.", "payments_transferFromAccount": "Transfer from account", "payments_transferToAccount": "Transfer to account", "payments_currency": "<PERSON><PERSON><PERSON><PERSON>", "payments_account": "Account", "payments_domestic": "Local transfer", "payments_international": "International transfer", "payments_premier_account_min_deposit": "Minimum deposit is {amount} {currency} (or equivalent)", "@payments_premier_account_min_deposit": {"description": "Minimum deposit message for premier accounts", "placeholders": {"amount": {"type": "String", "description": "The minimum amount formatted as currency"}, "currency": {"type": "String", "description": "The currency code"}}}, "payments_premier_account_warning_title": "Deposit {amount} {currency} to unlock Premier", "@payments_premier_account_warning_title": {"description": "Warning title for premier account minimum deposit requirement", "placeholders": {"amount": {"type": "String", "description": "The minimum amount formatted as currency"}, "currency": {"type": "String", "description": "The currency code"}}}, "payments_premier_account_warning_description": "To unlock the benefits of the Premier account, a minimum deposit of {amount} {currency} is required.", "@payments_premier_account_warning_description": {"description": "Warning description for premier account minimum deposit requirement", "placeholders": {"amount": {"type": "String", "description": "The minimum amount formatted as currency"}, "currency": {"type": "String", "description": "The currency code"}}}, "payments_add_new_wallet": "Add new wallet", "payments_minimum_amount": "Minimum amount: {amount} {currency}", "@payments_minimum_amount": {"description": "Minimum amount text with placeholders for amount and currency", "placeholders": {"amount": {"type": "String", "description": "The minimum amount formatted"}, "currency": {"type": "String", "description": "The currency code"}}}}