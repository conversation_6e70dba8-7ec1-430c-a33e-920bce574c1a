// import 'package:equiti_analytics/src/events/analytics_event_type.dart';

import 'package:equiti_analytics/src/events/analytics_event_type.dart';

enum PaymentsAnalyticsEvent {
  depositStart(AnalyticsEventType.Payments, 'depositStart'),
  depositMopSelected(AnalyticsEventType.Payments, 'depositMopSelected'),
  depositAccountSelected(AnalyticsEventType.Payments, 'depositAccountSelected'),
  depositCurrencyChanged(AnalyticsEventType.Payments, 'depositCurrencyChanged'),
  depositSuggestedAmountSelected(
    AnalyticsEventType.Payments,
    'depositSuggestedAmountSelected',
  ),
  depositInitiated(AnalyticsEventType.Payments, 'depositInitiated'),
  depositWebviewLoadStarted(
    AnalyticsEventType.Payments,
    'depositWebviewLoadStarted',
  ),
  depositWebviewLoaded(AnalyticsEventType.Payments, 'depositWebviewLoaded'),
  depositWebviewCallback(AnalyticsEventType.Payments, 'depositWebviewCallback'),
  depositWebviewExitConfirmation(
    AnalyticsEventType.Payments,
    'depositWebviewExitConfirmation',
  ),
  depositSdkLoadStarted(AnalyticsEventType.Payments, 'depositSdkLoadStarted'),
  depositSdkLoaded(AnalyticsEventType.Payments, 'depositSdkLoaded'),
  depositSdkEvent(AnalyticsEventType.Payments, 'depositSdkEvent'),
  depositCompleted(AnalyticsEventType.Payments, 'depositCompleted'),
  depositBankDetailsCopied(
    AnalyticsEventType.Payments,
    'depositBankDetailsCopied',
  ),
  depositBankPdfDownloaded(
    AnalyticsEventType.Payments,
    'depositBankPdfDownloaded',
  ),
  depositNotAvailable(AnalyticsEventType.Payments, 'depositNotAvailable'),

  // transfers
  transferSourceAccountPageLoaded(
    AnalyticsEventType.Payments,
    'transferSourceAccountPageLoaded',
  ),
  transferSourceAccountSelected(
    AnalyticsEventType.Payments,
    'transferSourceAccountSelected',
  ),
  transferDestinationAccountPageLoaded(
    AnalyticsEventType.Payments,
    'transferDestinationAccountPageLoaded',
  ),
  transferDestinationAccountSelected(
    AnalyticsEventType.Payments,
    'transferDestinationAccountSelected',
  ),

  transferInititated(AnalyticsEventType.Payments, 'transferInititated'),
  transferCompleted(AnalyticsEventType.Payments, 'transferCompleted'),

  //withdraw
  withdrawStart(AnalyticsEventType.Payments, 'withdrawStart'),
  withdrawMopSelected(AnalyticsEventType.Payments, 'withdrawMopSelected'),
  withdrawAccountSelected(
    AnalyticsEventType.Payments,
    'withdrawAccountSelected',
  ),
  withdrawCurrencyChanged(
    AnalyticsEventType.Payments,
    'withdrawCurrencyChanged',
  ),
  withdrawSuggestedAmountSelected(
    AnalyticsEventType.Payments,
    'withdrawSuggestedAmountSelected',
  ),
  withdawCardsLoaded(AnalyticsEventType.Payments, 'withdawCardsLoaded'),
  withdrawCardSelected(AnalyticsEventType.Payments, 'withdrawCardSelected'),
  withdrawFeesChanged(AnalyticsEventType.Payments, 'withdrawFeesChanged'),
  withdrawMFAInitiated(AnalyticsEventType.Payments, 'withdrawMFAInitiated'),
  withdrawMFACompleted(AnalyticsEventType.Payments, 'withdrawMFACompleted'),
  withdrawInitiated(AnalyticsEventType.Payments, 'withdrawInitiated'),
  withdrawBanksLoaded(AnalyticsEventType.Payments, 'withdrawBanksLoaded'),
  withdrawBankSelected(AnalyticsEventType.Payments, 'withdrawBankSelected'),
  withdrawBankDeleted(AnalyticsEventType.Payments, 'withdrawBankDeleted'),
  withdrawBankTransferTypesLoaded(
    AnalyticsEventType.Payments,
    'withdrawBankTransferTypesLoaded',
  ),
  withdrawBankTransferTypeSelected(
    AnalyticsEventType.Payments,
    'withdrawBankTransferTypeSelected',
  ),
  withdrawBankFeesChanged(
    AnalyticsEventType.Payments,
    'withdrawBankFeesChanged',
  ),
  withdrawAddNewBankStart(
    AnalyticsEventType.Payments,
    'withdrawAddNewBankStart',
  ),
  withdrawAddNewBankInititate(
    AnalyticsEventType.Payments,
    'withdrawAddNewBankInititate',
  ),
  withdrawAddNewBankComplete(
    AnalyticsEventType.Payments,
    'withdrawAddNewBankComplete',
  ),
  withdrawBankDocUploadPageLoaded(
    AnalyticsEventType.Payments,
    'withdrawBankDocUploadPageLoaded',
  ),
  withdrawBankDocUploadFileSelected(
    AnalyticsEventType.Payments,
    'withdrawBankDocUploadFileSelected',
  ),
  withdrawSkrillAccountsLoaded(
    AnalyticsEventType.Payments,
    'withdrawSkrillAccountsLoaded',
  ),
  withdrawSkrillAccountSelected(
    AnalyticsEventType.Payments,
    'withdrawSkrillAccountSelected',
  ),
  withdrawNetellerAccountsLoaded(
    AnalyticsEventType.Payments,
    'withdrawNetellerAccountsLoaded',
  ),
  withdrawNetellerAccountSelected(
    AnalyticsEventType.Payments,
    'withdrawNetellerAccountSelected',
  ),
  mopNotApplicable(AnalyticsEventType.Payments, 'mopNotApplicable'),
  withdrawCompleted(AnalyticsEventType.Payments, 'withdrawCompleted'),
  withdrawNotAvailable(AnalyticsEventType.Payments, 'withdrawNotAvailable');

  const PaymentsAnalyticsEvent(this.eventType, this.eventName);

  final AnalyticsEventType eventType;
  final String eventName;
}
