enum TradeAnalyticsEvent {
  //accounts
  accountSelected('Trade', 'accountSelected'),
  //Trade and orders
  symbolSelected('Trade', 'symbolSelected'),
  LotSizeChanged('Trade', 'lotSizeChanged'),
  buySelected('Trade', 'buySelected'),
  sellSelected('Trade', 'sellSelected'),
  tpChanged('Trade', 'tpChanged'),
  slChanged('Trade', 'slChanged'),
  placingTrade('Trade', 'placingTrade'),
  placingOrder('Trade', 'placingOrder'),
  tradePlacedResult('Trade', 'tradePlacedResult'),
  orderPlacedResult('Trade', 'orderPlacedResult'),
  // close trades
  startCloseTrade('Trade', 'startCloseTrade'),
  closeTradeResult('Trade', 'closeTradeResult'),
  //portfolio
  positionAdded('Trade', 'positionAdded'),
  positionDeleted('Trade', 'positionDeleted'),
  orderAdded('Trade', 'orderAdded'),
  orderDeleted('Trade', 'orderDeleted'),
  alertAdded('Trade', 'alertAdded'),
  alertDeleted('Trade', 'alertDeleted');

  const TradeAnalyticsEvent(this.eventType, this.eventName);

  final String eventType;
  final String eventName;
}
