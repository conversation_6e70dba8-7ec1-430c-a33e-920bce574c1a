import 'package:duplo/src/assets/assets.gen.dart';
import 'package:flutter/material.dart';

class LanguageModel {
  final String displayCode;
  final String name;
  final Widget flag;
  final String code;

  const LanguageModel({
    required this.displayCode,
    required this.name,
    required this.flag,
    required this.code,
  });
}

extension LanguageModelOptions on LanguageModel {
  static List<LanguageModel> supportedLanguages = [
    LanguageModel(
      displayCode: 'En',
      name: 'English',
      flag: Assets.images.enLanIc.svg(),
      code: 'en',
    ),
    LanguageModel(
      displayCode: 'Ar',
      name: 'العربية',
      flag: Assets.images.arLanIc.svg(height: 16, width: 16),
      code: "ar",
    ),
  ];
  static LanguageModel? getLanguageModelFromCode(String code) {
    return supportedLanguages
        .where(
          (element) => element.displayCode.toLowerCase() == code.toLowerCase(),
        )
        .firstOrNull;
  }

  static LanguageModel get defaultLanguage => supportedLanguages.firstOrNull!;
}

extension LanguageModelString on String {
  LanguageModel toLanguageModel() {
    return LanguageModelOptions.supportedLanguages.firstWhere(
      (element) => element.code.toLowerCase() == this.toLowerCase(),
      orElse: () => LanguageModelOptions.defaultLanguage,
    );
  }
}
