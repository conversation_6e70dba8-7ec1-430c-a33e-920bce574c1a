import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/di/di_container.dart';
import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:theme_manager/theme_manager.dart';

/// A search input field widget that follows the Du<PERSON>lo design system.
///
/// This widget provides a customizable search input field with optional leading/trailing icons,
/// helper text, and disabled state styling.
///
/// Example usage:
/// ```dart
/// DuploSearchInputField(
///   controller: TextEditingController(),
///   hintText: 'Search items...',
///   helperText: 'Enter at least 3 characters',
///   trailingIcon: Icon(Icons.close),
///   autoFocus: true,
/// )
/// ```
///
/// Properties:
/// - [controller]: Required TextEditingController to manage the input text
/// - [hintText]: Placeholder text shown when the field is empty (defaults to 'Search')
/// - [leadingIcon]: Custom widget to show before the input (defaults to search icon)
/// - [trailingIcon]: Optional widget to show after the input
/// - [autoFocus]: Whether the field should be focused when first displayed
/// - [isDisabled]: Whether the field should be disabled/non-editable
/// - [helperText]: Optional text shown below the input field
class DuploSearchInputField extends StatefulWidget {
  const DuploSearchInputField({
    super.key,
    this.hintText,
    required this.controller,
    this.leadingIcon,
    this.trailingIcon,
    this.autoFocus = false,
    this.isDisabled = false,
    this.helperText,
    this.onChanged,
    this.onSubmitted,
    this.onTapOutside,
    this.onTap,
  });

  /// The placeholder text shown when the search field is empty.
  /// Defaults to 'Search'.
  final String? hintText;

  /// Controller for managing the text input state.
  /// Required to handle text changes and input manipulation.
  final TextEditingController controller;

  /// Optional widgets to display before and after the text input.
  /// [leadingIcon] appears at the start (defaults to search icon if null).
  /// [trailingIcon] appears at the end (no default).
  final Widget? leadingIcon, trailingIcon;

  /// Whether the text field should be focused when first displayed.
  /// Defaults to false.
  final bool autoFocus;

  /// Whether the text field is non-editable/disabled.
  /// When true, the field will be styled with disabled colors and prevent input.
  /// Defaults to false.
  final bool isDisabled;

  /// Optional text displayed below the search field.
  /// Can be used to provide additional context or instructions.
  final String? helperText;

  /// Called whenever the text in the search field changes.
  /// Provides the current text value as a parameter.
  final void Function(String value)? onChanged;

  /// Called when the user submits the search field (e.g. pressing enter/done).
  /// Provides the final text value as a parameter.
  final void Function(String value)? onSubmitted;

  /// Called when the user taps outside of the search field.
  /// Provides the tap event details as a parameter.
  final void Function(PointerDownEvent)? onTapOutside;

  /// Called when the user taps on the search field.
  final VoidCallback? onTap;

  @override
  State<DuploSearchInputField> createState() => _DuploSearchInputFieldState();
}

class _DuploSearchInputFieldState extends State<DuploSearchInputField> {
  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final outLineBorder = OutlineInputBorder(
      borderSide: BorderSide(color: theme.border.borderSecondary),
      borderRadius: BorderRadius.circular(6),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: widget.onTap,
          child: Focus(
            child: Builder(
              builder: (builderContext) {
                return TextField(
                  controller: widget.controller,
                  enabled: !widget.isDisabled,
                  autofocus: widget.autoFocus,
                  keyboardAppearance:
                      diContainer<ThemeManager>().isDarkMode
                          ? Brightness.dark
                          : Brightness.light,
                  style: TextStyle(
                    color: theme.text.textPrimary,
                    fontSize: context.duploTextStyles.textMd.fontSize,
                    height: context.duploTextStyles.textMd.lineHeight,
                    fontWeight: FontWeight.w400,
                  ),
                  onChanged: (value) {
                    setState(() {
                      widget.onChanged?.call(value);
                    });
                  },
                  textAlign: TextAlign.justify,
                  onSubmitted: widget.onSubmitted,
                  cursorWidth: 2,
                  inputFormatters: [
                    FilteringTextInputFormatter.deny(
                      RegExp(r'[\u0621-\u064A\u0660-\u0669]'),
                    ),
                  ],
                  cursorColor: theme.text.textPrimary,
                  onTapOutside: (event) {
                    Focus.of(builderContext).unfocus();

                    widget.onTapOutside?.call(event);
                  },
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: theme.background.bgSecondarySubtle,
                    border: outLineBorder,
                    enabledBorder: outLineBorder,
                    focusedBorder: outLineBorder.copyWith(
                      borderSide: BorderSide(color: theme.border.borderBrand),
                    ),
                    disabledBorder: outLineBorder.copyWith(
                      borderSide: BorderSide(
                        color: theme.border.borderDisabled,
                      ),
                    ),
                    hintText:
                        widget.hintText ??
                        EquitiLocalization.of(context).duplo_search,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 14,
                      vertical: 12,
                    ),
                    hintStyle: TextStyle(
                      color: theme.text.textPlaceholder,
                      fontSize: context.duploTextStyles.textMd.fontSize,
                    ),
                    prefixIcon: Padding(
                      padding: EdgeInsets.fromLTRB(
                        Directionality.of(context) == TextDirection.ltr
                            ? 14
                            : 8,
                        14,
                        Directionality.of(context) == TextDirection.ltr
                            ? 8
                            : 14,
                        14,
                      ),
                      child:
                          widget.leadingIcon ??
                          Assets.images.searchMd.svg(
                            height: 20,
                            width: 20,
                            matchTextDirection: true,
                          ),
                    ),
                    suffixIcon:
                        widget.trailingIcon != null
                            ? Padding(
                              padding: const EdgeInsets.only(
                                right: 12,
                                left: 8,
                              ),
                              child: widget.trailingIcon,
                            )
                            : widget.controller.text.isNotEmpty
                            ? Padding(
                              padding: EdgeInsets.fromLTRB(
                                Directionality.of(context) == TextDirection.ltr
                                    ? 14
                                    : 8,
                                12,
                                Directionality.of(context) == TextDirection.ltr
                                    ? 8
                                    : 14,
                                12,
                              ),
                              child: InkWell(
                                onTap: () {
                                  setState(() {
                                    widget.controller.clear();
                                    widget.onChanged?.call(
                                      widget.controller.text,
                                    );
                                  });
                                },
                                child: Assets.images.xCircle.svg(
                                  height: 20,
                                  width: 20,
                                  matchTextDirection: true,
                                ),
                              ),
                            )
                            : null,
                    isCollapsed: true,
                  ),
                );
              },
            ),
          ),
        ),
        if (widget.helperText != null)
          Padding(
            padding: const EdgeInsets.only(top: 6, left: 8),
            child: DuploText(
              text: widget.helperText!,
              style: context.duploTextStyles.textXs,
              color: theme.text.textTertiary,
              fontWeight: DuploFontWeight.medium,
            ),
          ),
      ],
    );
  }
}
