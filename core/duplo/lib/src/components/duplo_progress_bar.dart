import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:flutter/material.dart';

class DuploProgressBar extends StatefulWidget {
  const DuploProgressBar({
    super.key,
    required this.progressValue,
    this.padding,
    this.backgroundColor,
    this.color,
    this.barCount = 1,
    this.currentBarIndex = 1,
  });

  final double progressValue;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final Color? color;
  final int barCount;
  final int currentBarIndex;

  @override
  State<DuploProgressBar> createState() => _DuploProgressBarState();
}

class _DuploProgressBarState extends State<DuploProgressBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  double _previousValue = 0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _updateAnimation();
  }

  @override
  void didUpdateWidget(DuploProgressBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.progressValue != oldWidget.progressValue ||
        widget.currentBarIndex != oldWidget.currentBarIndex) {
      _previousValue = oldWidget.progressValue;
      _updateAnimation();
    }
  }

  void _updateAnimation() {
    _animation = Tween<double>(
      begin: _previousValue,
      end: widget.progressValue,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
    _controller.forward(from: 0);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    return Padding(
      padding: widget.padding ?? const EdgeInsets.only(bottom: 16),
      child: Row(
        children: List.generate(widget.barCount * 2 - 1, (index) {
          if (index.isOdd) {
            return const SizedBox(width: 8);
          }
          final i = index ~/ 2 + 1;
          return Expanded(
            child:
                i == widget.currentBarIndex
                    ? AnimatedBuilder(
                      animation: _animation,
                      builder:
                          (ctx, child) => LinearProgressIndicator(
                            value: _animation.value,
                            minHeight: 4,
                            borderRadius: BorderRadius.circular(999),
                            backgroundColor:
                                widget.backgroundColor ??
                                theme.background.bgQuaternary,
                            color:
                                widget.color ?? theme.foreground.fgBrandPrimary,
                          ),
                    )
                    : LinearProgressIndicator(
                      value: i < widget.currentBarIndex ? 1 : 0,
                      minHeight: 4,
                      borderRadius: BorderRadius.circular(999),
                      backgroundColor:
                          widget.backgroundColor ??
                          theme.background.bgQuaternary,
                      color: widget.color ?? theme.foreground.fgBrandPrimary,
                    ),
          );
        }),
      ),
    );
  }
}
