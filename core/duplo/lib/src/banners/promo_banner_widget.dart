import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/duplo_tap/duplo_tap.dart';
import 'package:duplo/src/theming/duplo_theme_context_extension.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_typography_context_extension.dart';
import 'package:flutter/material.dart';

class PromoBannerWidget extends StatelessWidget {
  final String title;
  final String subtitle;
  final Widget? icon;
  final VoidCallback? onTap;

  const PromoBannerWidget({
    Key? key,
    required this.title,
    required this.subtitle,
    this.icon,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;

    return SizedBox(
      height: 122,
      child: Card(
        color: theme.background.bgSecondary,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: theme.border.borderSecondary, width: 1.0),
        ),
        clipBehavior: Clip.antiAlias,
        child: DuploTap(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 8, 8),
            child: Row(
              children: [
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      DuploText(
                        text: title,
                        style: textStyles.textMd,
                        fontWeight: DuploFontWeight.semiBold,
                        maxLines: 2,
                        color: theme.text.textPrimary,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      DuploText(
                        text: subtitle,
                        style: textStyles.textXs,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        color: theme.text.textSecondary,
                        textAlign: TextAlign.start,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 80,
                  height: 80,
                  child: icon ?? Assets.images.bannerGold.svg(),
                ),
                const SizedBox(width: 8),
                const Icon(Icons.chevron_right, color: Colors.grey, size: 24),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
