import 'package:broker_settings/broker_settings.dart';
import 'package:domain/domain.dart';
import 'package:onboarding/src/data/account_creation_response_model.dart';
import 'package:onboarding/src/data/progress_tracker/progress_tracker_response_model.dart';
import 'package:onboarding/src/data/send_otp_response_model/send_otp_response_model.dart';
import 'package:onboarding/src/domain/model/mobile_otp_verification_args/mobile_otp_verification_args.dart';
import 'package:onboarding/src/navigation/arguments/signup_options_args.dart';

abstract class OnboardingNavigation {
  void goToSignupOptions({required SignupOptionsArgs args});
  void goToLoginOptions({bool replace = false});
  void goToLogin();
  void goToSignup({String? email});
  void navigateToUserRegistration();
  void navigateToUserRegistrationUaePass({IdDetails? idDetails});
  void goToPersonalDetailsIntroPage();
  void navigateToVerifyMobile({required MobileOtpVerificationArgs args});
  void navigateToMobileNumberInput();
  void navigateToOtpInput({
    required MobileOtpVerificationArgs args,
    required SendOtpResponseData sendOtpModel,
  });
  void removeUntilMobileNumberInput();
  void navigateToPhoneNumberVerified({bool replace = false});
  void navigateToCountrySelector();
  void navigateToProgressTracker();
  void setAndNavigateToProgressTracker();
  void navigateToVerifyIdentity({bool replaceRoute = false});
  void navigateToMorphFormBuilder({ProgressTrackerData progressTracker});
  void setAndNavigateToMorphFormBuilder();
  void navigateToCreateAccountIntro({bool replaceRoute = false});
  void navigateToCreateAccountMain({
    required CreateAccountFlow createAccountFlow,
  });
  void navigateToAccountSuccessful({
    required AccountCreationResponseData data,
    required CreateAccountFlow createAccountFlow,
    bool replace = false,
    required AccountCreationPlatform platform,
  });
  void navigateToDepositIntro({bool replace = false});
  void navigateToDepositPaymentOptions({
    required DepositFlowConfig depositFlowConfig,
  });
  void navigateToHub({bool replace = false});
  void goToCitySelection();
  void navigateToSwitchAccounts({bool replace = false});
  void navigateBackToAccountsScreen();
  void logout();
  void popUnitRoute({required String popUntil});
}
