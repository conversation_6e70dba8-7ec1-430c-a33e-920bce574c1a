import 'dart:async';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:login/login.dart';
import 'package:onboarding/src/data/progress_tracker/progress_state.dart';
import 'package:onboarding/src/data/progress_tracker/progress_sub_state.dart';
import 'package:onboarding/src/data/progress_tracker/progress_tracker_action.dart';
import 'package:onboarding/src/data/progress_tracker/progress_tracker_response_model.dart';
import 'package:onboarding/src/domain/exceptions/fetch_progress_exception/fetch_progress_exception.dart';
import 'package:onboarding/src/domain/usecase/progress_tracker_use_case.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:onboarding/src/utils/decider.dart';
import 'package:prelude/prelude.dart';

part 'progress_tracker_bloc.freezed.dart';
part 'progress_tracker_event.dart';
part 'progress_tracker_state.dart';

class ProgressTrackerBloc
    extends Bloc<ProgressTrackerEvent, ProgressTrackerState> {
  final ProgressTrackerUseCase _progressTrackerUseCase;
  final OnboardingNavigation _onboardingNavigation;
  final LogoutUseCase _logoutUseCase;

  ProgressTrackerBloc({
    required ProgressTrackerUseCase progressTrackerUseCase,
    required OnboardingNavigation onboardingNavigation,
    required LogoutUseCase logoutUseCase,
  }) : _progressTrackerUseCase = progressTrackerUseCase,
       _onboardingNavigation = onboardingNavigation,
       _logoutUseCase = logoutUseCase,
       super(Loading()) {
    on<_Started>(_onStarted);
    on<_OnButtonPressed>(_onButtonPressed);
    on<_OnExploreTheAppPressed>(_onExploreTheAppPressed);
    on<_Logout>(_logout);
  }

  FutureOr<void> _onStarted(
    _Started event,
    Emitter<ProgressTrackerState> emit,
  ) async {
    emit(ProgressTrackerState.loading());

    try {
      final response =
          await _progressTrackerUseCase.fetchCurrentProgress().run();

      response.fold(
        (exception) {
          if (exception is FetchProgressException) {
            switch (exception) {
              case FetchProgressUnknownError():
                emit(ProgressTrackerState.error());
            }
          } else {
            log('Error fetching progress: $exception');
            emit(ProgressTrackerState.error());
          }
        },
        (ProgressTrackerData progressTrackerData) {
          log('Fetched progress successfully: $progressTrackerData');

          // If registration not started, navigate to add full name screen
          if (!progressTrackerData.registrationStarted) {
            Decider.handleRegistrationFlow(
              _onboardingNavigation,
              progressTrackerData.authenticationChannel,
              progressTrackerData.idDetails,
            );
            return;
          }
          // If phone number not added, navigate to phone number input screen
          if (progressTrackerData.phoneNumberAdded != null &&
              !progressTrackerData.phoneNumberAdded!) {
            Decider.handlePhoneNumberFlow(_onboardingNavigation);
            return;
          }

          final isPlaceTradeCompleted = progressTrackerData.steps?.any(
            (element) =>
                element.state == ProgressState.placeTrade &&
                element.subState == ProgressSubState.completed,
          );

          if (isPlaceTradeCompleted ?? false) {
            _onboardingNavigation.navigateToHub(replace: true);
            return;
          }

          if (progressTrackerData.currentStepIndex == null) {
            return emit(
              ProgressTrackerState.error(error: "No current step index found"),
            );
          }
          int firstIndexToShow = progressTrackerData.currentStepIndex ?? 0;
          int secondIndexToShow = firstIndexToShow + 1;

          if (progressTrackerData.currentStepIndex ==
              progressTrackerData.steps!.length - 1) {
            firstIndexToShow = progressTrackerData.currentStepIndex! - 1;
            secondIndexToShow = progressTrackerData.currentStepIndex!;
          }

          ProgressTrackerStep firstItem =
              progressTrackerData.steps!.elementAtOrNull(firstIndexToShow)!;
          ProgressTrackerStep secondItem =
              progressTrackerData.steps!.elementAtOrNull((secondIndexToShow))!;

          /// Check if there is any other ProgressIndicatorHeaderInfo before the firstItem
          ProgressTrackerStep? previouslyCompleted;
          if (firstIndexToShow > 0) {
            previouslyCompleted = progressTrackerData.steps!.elementAtOrNull(
              (firstIndexToShow - 1),
            );
          }

          /// Check if there is any other ProgressIndicatorHeaderInfo after the secondItem
          ProgressTrackerStep? nextToCompleted;
          if (secondIndexToShow < progressTrackerData.steps!.length - 1) {
            nextToCompleted = progressTrackerData.steps!.elementAtOrNull(
              (secondIndexToShow + 1),
            );
          }
          emit(
            ProgressTrackerState.success(
              firstItem,
              secondItem,
              previouslyCompleted,
              nextToCompleted,
              progressTrackerData,
            ),
          );
        },
      );
    } on Exception catch (e) {
      emit(ProgressTrackerState.error());
    }
  }

  void _onButtonPressed(
    _OnButtonPressed event,
    Emitter<ProgressTrackerState> emit,
  ) {
    final currentState = state;
    log('Button pressed: ${event.action} ${currentState}');
    if (currentState is Success) {
      final progressTracker = currentState.progressTracker;
      log('Button pressed: ${progressTracker}');
      _onboardingNavigation.navigateToMorphFormBuilder(
        progressTracker: progressTracker,
      );
    }
  }

  FutureOr<void> _onExploreTheAppPressed(
    _OnExploreTheAppPressed event,
    Emitter<ProgressTrackerState> emit,
  ) {
    _onboardingNavigation.navigateToHub();
  }

  void _logout(_Logout event, Emitter<ProgressTrackerState> emit) {
    _logoutUseCase.logout(
      onSuccess: () {
        _onboardingNavigation.logout();
      },
      onFailure: () {
        log("Logout failed");
      },
    );
  }
}
