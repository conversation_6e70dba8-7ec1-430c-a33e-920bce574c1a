// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'progress_tracker_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ProgressTrackerEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProgressTrackerEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProgressTrackerEvent()';
}


}

/// @nodoc
class $ProgressTrackerEventCopyWith<$Res>  {
$ProgressTrackerEventCopyWith(ProgressTrackerEvent _, $Res Function(ProgressTrackerEvent) __);
}


/// @nodoc


class _Started implements ProgressTrackerEvent {
  const _Started();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Started);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProgressTrackerEvent.started()';
}


}




/// @nodoc


class _OnExploreTheAppPressed implements ProgressTrackerEvent {
  const _OnExploreTheAppPressed();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnExploreTheAppPressed);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProgressTrackerEvent.onExploreTheAppPressed()';
}


}




/// @nodoc


class _OnButtonPressed implements ProgressTrackerEvent {
  const _OnButtonPressed(this.action);
  

 final  ProgressTrackerAction action;

/// Create a copy of ProgressTrackerEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnButtonPressedCopyWith<_OnButtonPressed> get copyWith => __$OnButtonPressedCopyWithImpl<_OnButtonPressed>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnButtonPressed&&(identical(other.action, action) || other.action == action));
}


@override
int get hashCode => Object.hash(runtimeType,action);

@override
String toString() {
  return 'ProgressTrackerEvent.onButtonPressed(action: $action)';
}


}

/// @nodoc
abstract mixin class _$OnButtonPressedCopyWith<$Res> implements $ProgressTrackerEventCopyWith<$Res> {
  factory _$OnButtonPressedCopyWith(_OnButtonPressed value, $Res Function(_OnButtonPressed) _then) = __$OnButtonPressedCopyWithImpl;
@useResult
$Res call({
 ProgressTrackerAction action
});




}
/// @nodoc
class __$OnButtonPressedCopyWithImpl<$Res>
    implements _$OnButtonPressedCopyWith<$Res> {
  __$OnButtonPressedCopyWithImpl(this._self, this._then);

  final _OnButtonPressed _self;
  final $Res Function(_OnButtonPressed) _then;

/// Create a copy of ProgressTrackerEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? action = null,}) {
  return _then(_OnButtonPressed(
null == action ? _self.action : action // ignore: cast_nullable_to_non_nullable
as ProgressTrackerAction,
  ));
}


}

/// @nodoc


class _Logout implements ProgressTrackerEvent {
  const _Logout();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Logout);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProgressTrackerEvent.logout()';
}


}




/// @nodoc
mixin _$ProgressTrackerState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProgressTrackerState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProgressTrackerState()';
}


}

/// @nodoc
class $ProgressTrackerStateCopyWith<$Res>  {
$ProgressTrackerStateCopyWith(ProgressTrackerState _, $Res Function(ProgressTrackerState) __);
}


/// @nodoc


class Loading implements ProgressTrackerState {
  const Loading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Loading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProgressTrackerState.loading()';
}


}




/// @nodoc


class Success implements ProgressTrackerState {
  const Success(this.firstItem, this.secondItem, this.previouslyCompleted, this.nextToCompleted, this.progressTracker);
  

 final  ProgressTrackerStep firstItem;
 final  ProgressTrackerStep secondItem;
 final  ProgressTrackerStep? previouslyCompleted;
 final  ProgressTrackerStep? nextToCompleted;
 final  ProgressTrackerData progressTracker;

/// Create a copy of ProgressTrackerState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SuccessCopyWith<Success> get copyWith => _$SuccessCopyWithImpl<Success>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Success&&(identical(other.firstItem, firstItem) || other.firstItem == firstItem)&&(identical(other.secondItem, secondItem) || other.secondItem == secondItem)&&(identical(other.previouslyCompleted, previouslyCompleted) || other.previouslyCompleted == previouslyCompleted)&&(identical(other.nextToCompleted, nextToCompleted) || other.nextToCompleted == nextToCompleted)&&(identical(other.progressTracker, progressTracker) || other.progressTracker == progressTracker));
}


@override
int get hashCode => Object.hash(runtimeType,firstItem,secondItem,previouslyCompleted,nextToCompleted,progressTracker);

@override
String toString() {
  return 'ProgressTrackerState.success(firstItem: $firstItem, secondItem: $secondItem, previouslyCompleted: $previouslyCompleted, nextToCompleted: $nextToCompleted, progressTracker: $progressTracker)';
}


}

/// @nodoc
abstract mixin class $SuccessCopyWith<$Res> implements $ProgressTrackerStateCopyWith<$Res> {
  factory $SuccessCopyWith(Success value, $Res Function(Success) _then) = _$SuccessCopyWithImpl;
@useResult
$Res call({
 ProgressTrackerStep firstItem, ProgressTrackerStep secondItem, ProgressTrackerStep? previouslyCompleted, ProgressTrackerStep? nextToCompleted, ProgressTrackerData progressTracker
});


$ProgressTrackerStepCopyWith<$Res> get firstItem;$ProgressTrackerStepCopyWith<$Res> get secondItem;$ProgressTrackerStepCopyWith<$Res>? get previouslyCompleted;$ProgressTrackerStepCopyWith<$Res>? get nextToCompleted;$ProgressTrackerDataCopyWith<$Res> get progressTracker;

}
/// @nodoc
class _$SuccessCopyWithImpl<$Res>
    implements $SuccessCopyWith<$Res> {
  _$SuccessCopyWithImpl(this._self, this._then);

  final Success _self;
  final $Res Function(Success) _then;

/// Create a copy of ProgressTrackerState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? firstItem = null,Object? secondItem = null,Object? previouslyCompleted = freezed,Object? nextToCompleted = freezed,Object? progressTracker = null,}) {
  return _then(Success(
null == firstItem ? _self.firstItem : firstItem // ignore: cast_nullable_to_non_nullable
as ProgressTrackerStep,null == secondItem ? _self.secondItem : secondItem // ignore: cast_nullable_to_non_nullable
as ProgressTrackerStep,freezed == previouslyCompleted ? _self.previouslyCompleted : previouslyCompleted // ignore: cast_nullable_to_non_nullable
as ProgressTrackerStep?,freezed == nextToCompleted ? _self.nextToCompleted : nextToCompleted // ignore: cast_nullable_to_non_nullable
as ProgressTrackerStep?,null == progressTracker ? _self.progressTracker : progressTracker // ignore: cast_nullable_to_non_nullable
as ProgressTrackerData,
  ));
}

/// Create a copy of ProgressTrackerState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProgressTrackerStepCopyWith<$Res> get firstItem {
  
  return $ProgressTrackerStepCopyWith<$Res>(_self.firstItem, (value) {
    return _then(_self.copyWith(firstItem: value));
  });
}/// Create a copy of ProgressTrackerState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProgressTrackerStepCopyWith<$Res> get secondItem {
  
  return $ProgressTrackerStepCopyWith<$Res>(_self.secondItem, (value) {
    return _then(_self.copyWith(secondItem: value));
  });
}/// Create a copy of ProgressTrackerState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProgressTrackerStepCopyWith<$Res>? get previouslyCompleted {
    if (_self.previouslyCompleted == null) {
    return null;
  }

  return $ProgressTrackerStepCopyWith<$Res>(_self.previouslyCompleted!, (value) {
    return _then(_self.copyWith(previouslyCompleted: value));
  });
}/// Create a copy of ProgressTrackerState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProgressTrackerStepCopyWith<$Res>? get nextToCompleted {
    if (_self.nextToCompleted == null) {
    return null;
  }

  return $ProgressTrackerStepCopyWith<$Res>(_self.nextToCompleted!, (value) {
    return _then(_self.copyWith(nextToCompleted: value));
  });
}/// Create a copy of ProgressTrackerState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProgressTrackerDataCopyWith<$Res> get progressTracker {
  
  return $ProgressTrackerDataCopyWith<$Res>(_self.progressTracker, (value) {
    return _then(_self.copyWith(progressTracker: value));
  });
}
}

/// @nodoc


class Error implements ProgressTrackerState {
  const Error({this.error});
  

 final  String? error;

/// Create a copy of ProgressTrackerState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ErrorCopyWith<Error> get copyWith => _$ErrorCopyWithImpl<Error>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Error&&(identical(other.error, error) || other.error == error));
}


@override
int get hashCode => Object.hash(runtimeType,error);

@override
String toString() {
  return 'ProgressTrackerState.error(error: $error)';
}


}

/// @nodoc
abstract mixin class $ErrorCopyWith<$Res> implements $ProgressTrackerStateCopyWith<$Res> {
  factory $ErrorCopyWith(Error value, $Res Function(Error) _then) = _$ErrorCopyWithImpl;
@useResult
$Res call({
 String? error
});




}
/// @nodoc
class _$ErrorCopyWithImpl<$Res>
    implements $ErrorCopyWith<$Res> {
  _$ErrorCopyWithImpl(this._self, this._then);

  final Error _self;
  final $Res Function(Error) _then;

/// Create a copy of ProgressTrackerState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? error = freezed,}) {
  return _then(Error(
error: freezed == error ? _self.error : error // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
