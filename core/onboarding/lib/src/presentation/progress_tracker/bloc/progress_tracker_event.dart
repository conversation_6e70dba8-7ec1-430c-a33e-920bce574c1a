part of 'progress_tracker_bloc.dart';

@freezed
sealed class ProgressTrackerEvent with _$ProgressTrackerEvent {
  const factory ProgressTrackerEvent.started() = _Started;
  const factory ProgressTrackerEvent.onExploreTheAppPressed() =
      _OnExploreTheAppPressed;
  const factory ProgressTrackerEvent.onButtonPressed(
    ProgressTrackerAction action,
  ) = _OnButtonPressed;
  const factory ProgressTrackerEvent.logout() = _Logout;
}
