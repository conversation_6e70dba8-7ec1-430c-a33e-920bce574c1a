import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:login/src/di/di_container.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/data/progress_tracker/progress_sub_state.dart';
import 'package:onboarding/src/data/progress_tracker/progress_tracker_action.dart';
import 'package:onboarding/src/data/progress_tracker/progress_tracker_response_model.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:onboarding/src/presentation/progress_tracker/bloc/progress_tracker_bloc.dart';
import 'package:onboarding/src/presentation/progress_tracker/widgets/progress_indicator_header_widget.dart';
import 'package:onboarding/src/presentation/progress_tracker/widgets/state_widgets/progress_tracker_state_widget.dart';
import 'package:prelude/prelude.dart';

class ProgressTrackerScreen extends StatefulWidget {
  const ProgressTrackerScreen({super.key});

  @override
  State<ProgressTrackerScreen> createState() => _ProgressTrackerScreenState();
}

class _ProgressTrackerScreenState extends State<ProgressTrackerScreen>
    with PerformanceObserverMixin {
  late BuildContext blocContext;

  Widget _widgetForState({
    required ProgressTrackerStep progressTrackerStep,
    required void Function(ProgressTrackerAction) onButtonPressed,
    required void Function(String) onDiscoverMorePressed,
    VoidCallback? onExploreTheAppPressed,
    required List<DiscoverMoreData> discoverMoreItems,
    ProgressTrackerData? progressTrackerData,
  }) {
    // ignore: avoid-missing-enum-constant-in-map
    diContainer<AnalyticsService>().setGlobalAttributes({
      AnalyticsGlobalAttributes.userId: progressTrackerData?.userId,
      AnalyticsGlobalAttributes.brokerId: progressTrackerData?.brokerId,
    });
    diContainer<AnalyticsService>().sendEvent(
      eventType: OnboardingAnalyticsEvent.progressTrackerNext.eventType.name,
      eventName: OnboardingAnalyticsEvent.progressTrackerNext.eventName,
      metadata: {'currentStep': progressTrackerData?.currentStep?.name},
    );
    final ProgressTrackerAction? action = progressTrackerStep.primaryAction();
    if (action != null) {
      return ProgressTrackerStateWidget(
        stateAction: action,
        onButtonPressed: onButtonPressed,
        onDiscoverMorePressed: onDiscoverMorePressed,
        title: progressTrackerStep.title ?? "",
        subtitle: progressTrackerStep.message ?? "",
        onExploreTheAppPressed: onExploreTheAppPressed,
        discoverMoreItems: discoverMoreItems,
      );
    }

    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);
    return BlocProvider(
      create:
          (BuildContext createContext) =>
              diContainer<ProgressTrackerBloc>()
                ..add(ProgressTrackerEvent.started()),
      child: BlocBuilder<ProgressTrackerBloc, ProgressTrackerState>(
        buildWhen: (previous, current) => previous != current,
        builder: (builderContext, state) {
          blocContext = builderContext;
          return switch (state) {
            Loading() => LoadingView(),
            Error() => Scaffold(
              backgroundColor: theme.background.bgSecondary,
              body: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: DuploSpacing.spacing_xl_16,
                  ),
                  child: EmptyOrErrorStateComponent.error(
                    raiseIssueText: localization.trader_raiseATicket,
                    onRaiseIssue: () {
                      debugPrint('Raise ticket');
                    },
                    svgImage: onboarding.Assets.images.searchError.svg(),
                    title: localization.trader_somethingWentWrong,
                    description: localization.trader_somethingWentWrong,
                    retryButtonText: localization.trader_tryAgain,
                    onTapRetry: () {
                      builderContext.read<ProgressTrackerBloc>().add(
                        ProgressTrackerEvent.started(),
                      );
                    },
                  ),
                ),
              ),
            ),
            Success(
              :final firstItem,
              :final secondItem,
              :final previouslyCompleted,
              :final nextToCompleted,
              :final progressTracker,
            ) =>
              Scaffold(
                backgroundColor: theme.background.bgPrimary,
                appBar: DuploAppBar(
                  title: "",
                  actions: [
                    IconButton(
                      onPressed: () {
                        DuploLogoutBottomSheet.show(
                          context: context,
                          onLogout: () {
                            builderContext.read<ProgressTrackerBloc>().add(
                              ProgressTrackerEvent.logout(),
                            );
                          },
                        );
                      },
                      icon: Icon(
                        Icons.logout,
                        size: 24,
                        color: theme.foreground.fgSecondary,
                      ),
                    ),
                  ],
                ),
                body: SafeArea(
                  child: Column(
                    children: [
                      ProgressIndicatorHeaderWidget(
                        firstItem: firstItem,
                        secondItem: secondItem,
                        previouslyCompleted: previouslyCompleted,
                        nextToCompleted: nextToCompleted,
                      ),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Column(
                              children: [
                                if (secondItem.subState ==
                                    ProgressSubState.inProgress)
                                  _widgetForState(
                                    progressTrackerStep: secondItem,
                                    onButtonPressed:
                                        (action) => _onButtonPressed(action),
                                    onDiscoverMorePressed:
                                        _onDiscoverMorePressed,
                                    onExploreTheAppPressed:
                                        _onExploreTheAppPressed,
                                    progressTrackerData: progressTracker,
                                    discoverMoreItems:
                                        state.progressTracker.discoverMoreItems,
                                  )
                                else
                                  _widgetForState(
                                    progressTrackerStep: firstItem,
                                    onButtonPressed:
                                        (action) => _onButtonPressed(action),
                                    onDiscoverMorePressed:
                                        _onDiscoverMorePressed,
                                    onExploreTheAppPressed:
                                        _onExploreTheAppPressed,
                                    progressTrackerData: progressTracker,
                                    discoverMoreItems:
                                        state.progressTracker.discoverMoreItems,
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          };
        },
      ),
    );
  }

  void _onButtonPressed(ProgressTrackerAction p1) {
    print("Action: $p1");
    final bloc = blocContext.read<ProgressTrackerBloc>();
    bloc.add(ProgressTrackerEvent.onButtonPressed(p1));
  }

  void _onDiscoverMorePressed(String p1) {
    print("Discover more: $p1");
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    // TODO (Aakash): Revisit this. See below comment for more context
    // Context: This was previously added to update progress tracker whenever user
    // comes back to it. But it has a side effect where the progress traker api
    // gets called whenever a route is popped.
    // log('Route Popped: ${route.settings.name ?? 'null'}');
    // final bloc = blocContext.read<ProgressTrackerBloc>();
    // bloc.add(ProgressTrackerEvent.started());
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    log('Route pushed: ${route.settings.name ?? 'null'}');
  }

  void _onExploreTheAppPressed() {
    final bloc = blocContext.read<ProgressTrackerBloc>();
    bloc.add(ProgressTrackerEvent.onExploreTheAppPressed());
  }
}
