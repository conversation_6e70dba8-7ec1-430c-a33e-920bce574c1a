// ignore_for_file: prefer-number-format

import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:onboarding/src/data/progress_tracker/progress_state.dart';
import 'package:onboarding/src/data/progress_tracker/progress_sub_state.dart';
import 'package:onboarding/src/data/progress_tracker/progress_tracker_response_model.dart';
import 'package:onboarding/src/presentation/progress_tracker/widgets/gradient_line.dart';
import 'package:onboarding/src/presentation/progress_tracker/widgets/progress_indicator_node.dart';

class ProgressIndicatorHeaderWidget extends StatefulWidget {
  final ProgressTrackerStep? previouslyCompleted;
  final ProgressTrackerStep? nextToCompleted;
  final ProgressTrackerStep firstItem;
  final ProgressTrackerStep secondItem;

  const ProgressIndicatorHeaderWidget({
    required this.firstItem,
    required this.secondItem,
    this.previouslyCompleted,
    this.nextToCompleted,
    super.key,
  });

  @override
  State<ProgressIndicatorHeaderWidget> createState() =>
      _ProgressIndicatorHeaderWidgetState();
}

class _ProgressIndicatorHeaderWidgetState
    extends State<ProgressIndicatorHeaderWidget> {
  static double itemsWidth = 120;
  static double itemsHeight = 150;
  static double leadingSpace = 16;
  static double trallingSpace = 32;
  static double indicatorsNodeHeight = 50;

  final GlobalKey containerKey = GlobalKey();
  double _containerHeight = 0;

  void _observeHeight() {
    final RenderBox? renderBox =
        containerKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final height = renderBox.size.height;
      if (height != _containerHeight) {
        setState(() {
          _containerHeight = height;
        });
      }
    }
  }

  void _scheduleHeightObservation() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _observeHeight();
    });
  }

  Widget _widgetForState(BuildContext context, ProgressSubState subState) {
    if (subState == ProgressSubState.locked) {
      return Container(
        height: indicatorsNodeHeight,
        width: indicatorsNodeHeight,
        child: Center(child: subState.asset()),
      );
    }
    return ProgressIndicatorNode.viewForState(context, subState);
  }

  @override
  Widget build(BuildContext context) {
    _scheduleHeightObservation();
    final screenSize = MediaQuery.sizeOf(context);
    final lineOneVisibleWidth = ((leadingSpace + (itemsWidth / 2)));
    final lineThreeWidth = ((trallingSpace + (itemsWidth / 2)));
    final lineTwoWidth =
        (screenSize.width - (lineOneVisibleWidth + lineThreeWidth));
    double transformX = 0;

    Color lineTwoStartColor = widget.firstItem.subState.color(context);
    Color lineTwoEndColor = widget.secondItem.subState.color(context);

    Color lineOneStartColor =
        widget.previouslyCompleted?.subState.color(context) ??
        Colors.transparent;
    Color lineOneEndColor =
        widget.previouslyCompleted != null
            ? widget.firstItem.subState.color(context)
            : Colors.transparent;
    Color lineThreeColor =
        widget.nextToCompleted?.subState.color(context) ?? Colors.transparent;

    /// Tracks whether the second item should be centered in the UI.
    /// Set to true when the second item is in progress, causing the entire
    /// widget to transform and center the in-progress item on screen.
    /// In Arabic (RTL), this ensures the correct item appears centered by
    /// forcing LTR direction when items are centered.
    bool isItemCentered = false;

    if (widget.secondItem.subState == ProgressSubState.inProgress) {
      /// IF last item is in progress, move it to the center
      transformX = -((screenSize.width / 2) - lineThreeWidth);
      isItemCentered = true;
    }

    return Transform.translate(
      offset: Offset(transformX, 0),
      child: Container(
        width: screenSize.width,
        height: _containerHeight,
        child: Stack(
          children: [
            Positioned(
              bottom: (indicatorsNodeHeight / 2) + 1,
              right: lineTwoWidth + lineThreeWidth,
              child: GradientLine(
                startColor: lineOneStartColor,
                endColor: lineOneEndColor,
                width: lineTwoWidth,
              ),
            ),
            Positioned(
              bottom: (indicatorsNodeHeight / 2) + 1,
              left: ((leadingSpace + (itemsWidth / 2))),
              child: GradientLine(
                startColor: lineTwoStartColor,
                endColor: lineTwoEndColor,
                width: lineTwoWidth,
              ),
            ),
            Positioned(
              bottom: (indicatorsNodeHeight / 2) + 1,
              right: 0,
              child: Container(
                color: lineThreeColor,
                height: 2,
                width: lineThreeWidth,
              ),
            ),
            Positioned(
              top: 0,
              left: 0,
              child: Container(
                key: containerKey,
                padding: const EdgeInsets.only(top: 16),
                width: screenSize.width,
                color: Colors.transparent,
                child: Row(
                  textDirection: isItemCentered ? TextDirection.ltr : null,
                  children: [
                    SizedBox(width: leadingSpace),
                    _widgetForItem(widget.firstItem, context),
                    Spacer(),
                    _widgetForItem(widget.secondItem, context),
                    SizedBox(width: trallingSpace),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _widgetForItem(ProgressTrackerStep item, BuildContext context) {
    print(item.state);
    final theme = context.duploTheme;
    final testStyles = context.duploTextStyles;
    return Column(
      children: [
        Container(
          height: itemsHeight,
          width: itemsWidth,
          child: Center(child: item.state.asset()),
        ), //Placeholder for the image
        const SizedBox(height: 8),
        DuploText(
          text: item.state.title(context),
          style: testStyles.textSm,
          fontWeight: DuploFontWeight.regular,
          color: theme.text.textPrimary,
        ),
        const SizedBox(height: 8),
        _widgetForState(context, item.subState),
      ],
    );
  }
}
