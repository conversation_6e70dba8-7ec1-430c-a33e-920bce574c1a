import 'dart:async';
import 'dart:developer';

import 'package:domain/domain.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:onboarding/src/data/add_phone_request_model/add_phone_request_model.dart';
import 'package:onboarding/src/domain/exceptions/add_phone_exception/add_phone_exception.dart';
import 'package:onboarding/src/domain/model/mobile_otp_verification_args/mobile_otp_verification_args.dart';
import 'package:onboarding/src/domain/usecase/add_phone_use_case.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:onboarding/src/presentation/personal_details_section/utils/personal_details_error.dart';
import 'package:prelude/prelude.dart';
part 'mobile_number_input_bloc.freezed.dart';
part 'mobile_number_input_event.dart';
part 'mobile_number_input_state.dart';

@injectable
class MobileNumberInputBloc
    extends Bloc<MobileNumberInputEvent, MobileNumberInputState> {
  MobileNumberInputBloc(
    this._navigation,
    this._addPhoneUseCase,
    this._getCountriesUseCase,
    this._changeCredentialsUseCase,
    this._analyticsService,
  ) : super(const MobileNumberInputState()) {
    on<Initial>(_initial);
    on<PhoneNumberChanged>(_onPhoneNumberChanged);
    on<CountryCodeSelected>(_onCountryCodeSelected);
    on<ConfirmButtonPressed>(_onConfirmButtonPressed);
    on<ValidatePhoneNumber>(_onValidatePhoneNumber);
    on<OpenHelpDialog>(_onOpenHelpDialog);
    on<SignInPressed>(_onSignInPressed);
    on<UpdatePhoneArgsEvent>(_onUpdatePhoneArgsEvent);
    on<OnUpdateButtonPressed>(_onUpdateButtonPressed);
    add(const Initial());
  }

  final OnboardingNavigation _navigation;
  final AddPhoneUseCase _addPhoneUseCase;
  final ChangeCredentialsUseCase _changeCredentialsUseCase;
  final GetCountryUseCase _getCountriesUseCase;
  final AnalyticsService _analyticsService;

  Future<void> _initial(
    Initial event,
    Emitter<MobileNumberInputState> emit,
  ) async {
    // Set loading state
    emit(
      state.copyWith(
        processState: const MobileNumberInputProcessState.loading(),
      ),
    );
    // Get countries from use case
    final countryResult = await _getCountriesUseCase().run();

    countryResult.fold(
      (failure) {
        // Handle error case
        if (!isClosed) {
          if (failure is GetCountriesException) {
            switch (failure) {
              case GetCountriesUnknownError():
                emit(
                  state.copyWith(
                    processState: const MobileNumberInputProcessState.error(),
                  ),
                );
            }
          }
          emit(
            state.copyWith(
              processState: const MobileNumberInputProcessState.error(),
            ),
          );
        }
      },
      (allCountriesModel) {
        final countries = allCountriesModel.data?.countries ?? [];
        if (countries.isNotEmpty && !isClosed) {
          //todo(sambhav): hardcoded for now because of requirement to select UAE as default
          //and we dont want to ask for location for this purpose only
          final areIndex = countries.indexWhere(
            (country) => country.code == 'ARE',
          );
          final selectedIndex = areIndex != -1 ? areIndex : 0;
          final selectedCountry = countries.elementAtOrNull(selectedIndex);

          emit(
            state.copyWith(
              countries: countries,
              selectedCountryCode: selectedCountry?.dialCode ?? "",
              selectedCountryFlag: selectedCountry?.code ?? '',
              selectedCountryIndex: selectedIndex,
              placeholderText: _getExamplePlaceholderText(
                selectedCountry?.dialCode ?? "",
              ),
              processState: const MobileNumberInputProcessState.initial(),
            ),
          );
        }
      },
    );
  }

  void _onPhoneNumberChanged(
    PhoneNumberChanged event,
    Emitter<MobileNumberInputState> emit,
  ) {
    emit(state.copyWith(phoneNumber: event.phoneNumber, errorMessage: null));
    add(const MobileNumberInputEvent.validatePhoneNumber());
  }

  void _onCountryCodeSelected(
    CountryCodeSelected event,
    Emitter<MobileNumberInputState> emit,
  ) {
    final country = state.countries.elementAtOrNull(event.selectedIndex);
    emit(
      state.copyWith(
        selectedCountryCode: country?.dialCode ?? '',
        selectedCountryFlag: country?.code ?? '',
        errorMessage: null,
        selectedCountryIndex: event.selectedIndex,
        placeholderText: _getExamplePlaceholderText(country?.dialCode ?? ''),
      ),
    );
    add(const MobileNumberInputEvent.validatePhoneNumber());
  }

  Future<void> _onConfirmButtonPressed(
    ConfirmButtonPressed event,
    Emitter<MobileNumberInputState> emit,
  ) async {
    if (!state.isConfirmButtonEnabled) return;

    if (!isClosed) {
      emit(state.copyWith(isLoading: true));
    }
    try {
      // Get the selected country object using the selected country index
      final selectedCountry = state.countries.elementAtOrNull(
        state.selectedCountryIndex,
      );

      final result =
          await _addPhoneUseCase(
            addPhoneRequestModel: AddPhoneRequestModel(
              phone: PhoneData(
                countryCode:
                    selectedCountry?.dialCode ?? state.selectedCountryCode,
                number: state.phoneNumber.removeSpaces(),
                regionCode: selectedCountry?.code ?? state.selectedCountryFlag,
              ),
            ),
          ).run();

      result.fold(
        (exception) {
          log('exception while adding mobile number : ${exception}');
          if (exception is AddPhoneException) {
            switch (exception) {
              case InvalidPhoneNumber():
                if (!isClosed) {
                  emit(
                    state.copyWith(
                      processState:
                          MobileNumberInputProcessState.invalidMobileNumberError(),
                      isLoading: false,
                      isConfirmButtonEnabled: false,
                    ),
                  );
                }
                _resetProcessState(emit);
                break;
              case DuplicatePhoneNumber():
                if (!isClosed) {
                  emit(
                    state.copyWith(
                      processState:
                          MobileNumberInputProcessState.duplicateMobileNumberError(),
                      isLoading: false,
                      isConfirmButtonEnabled: false,
                    ),
                  );
                }
                _resetProcessState(emit);
                break;
              case AddPhoneUnknownError(:final message):
                if (!isClosed) {
                  emit(
                    state.copyWith(
                      errorMessage: PersonalDetailsError.unknownError,
                      isLoading: false,
                      isConfirmButtonEnabled: false,
                    ),
                  );
                }
                break;
            }
          } else {
            if (!isClosed) {
              emit(
                state.copyWith(
                  //todo: need localization
                  errorMessage: PersonalDetailsError.generalError,
                  isLoading: false,
                  isConfirmButtonEnabled: true,
                ),
              );
            }
          }
        },
        (verifyMobileModel) {
          // Since we only have success case now, we can directly handle it
          if (!isClosed) {
            emit(state.copyWith(isLoading: false));
            _setGlobalAttributes(
              countryCode: state.selectedCountryCode,
              mobileNumber: state.phoneNumber.removeSpaces(),
            );
            _navigation.navigateToVerifyMobile(
              args: MobileOtpVerificationArgs(
                phoneNumber: state.phoneNumber,
                countryCode: state.selectedCountryCode,
                isUpdatingPhoneNumber:
                    state.updatePhoneArgs?.isUpdatingPhoneNumber ?? false,
                origin: state.updatePhoneArgs?.origin,
              ),
            );
          }
        },
      );
    } catch (e) {
      if (!isClosed) {
        emit(
          //todo: need localization
          state.copyWith(
            errorMessage: PersonalDetailsError.generalError,
            isLoading: false,
          ),
        );
      }
    }
  }

  Future<void> _onValidatePhoneNumber(
    ValidatePhoneNumber event,
    Emitter<MobileNumberInputState> emit,
  ) async {
    final phoneNumber = state.phoneNumber;
    final countryCode = state.selectedCountryCode;
    final fullNumber = '$countryCode$phoneNumber';
    PersonalDetailsError? errorMessage;
    bool isEnabled = false;

    final formattedNumber = formatNumberSync(
      fullNumber,
      phoneNumberFormat: PhoneNumberFormat.international,
    );
    try {
      await parse(formattedNumber);
      isEnabled = true;
      errorMessage = null;
    } catch (e) {
      //todo: need localization
      errorMessage =
          _checkPhoneNumberLength(formattedNumber)
              ? PersonalDetailsError.invalidPhoneNumber
              : null;
      isEnabled = false;
    }
    if (!isClosed) {
      emit(
        state.copyWith(
          errorMessage: errorMessage,
          isConfirmButtonEnabled: isEnabled,
        ),
      );
    }
  }

  void _onOpenHelpDialog(
    OpenHelpDialog event,
    Emitter<MobileNumberInputState> emit,
  ) {
    // TODO: [sambhav] Implement help dialog
  }

  void _onSignInPressed(
    SignInPressed event,
    Emitter<MobileNumberInputState> emit,
  ) {
    // TODO: [sambhav] Implement sign in pressed
  }

  void _resetProcessState(Emitter<MobileNumberInputState> emit) {
    emit(
      state.copyWith(
        processState: const MobileNumberInputProcessState.initial(),
        errorMessage: null,
      ),
    );
  }

  String _getExamplePlaceholderText(String countryCode) {
    final formattedNumber = formatNumberSync(
      '$countryCode 790000000000',
      phoneNumberFormat: PhoneNumberFormat.international,
    ).replaceAll(countryCode, '');
    return formattedNumber;
  }

  bool _checkPhoneNumberLength(String formattedNumber) {
    final formattedNumberLength =
        formattedNumber
            .replaceAll(state.selectedCountryCode, '')
            .removeSpaces()
            .length;
    final examplePlaceholderTextLength =
        _getExamplePlaceholderText(
          state.selectedCountryCode,
        ).removeSpaces().length;
    return formattedNumberLength >= examplePlaceholderTextLength;
  }

  FutureOr<void> _onUpdatePhoneArgsEvent(
    UpdatePhoneArgsEvent event,
    Emitter<MobileNumberInputState> emit,
  ) {
    emit(state.copyWith(updatePhoneArgs: event.args));
  }

  Future<void> _onUpdateButtonPressed(
    OnUpdateButtonPressed event,
    Emitter<MobileNumberInputState> emit,
  ) async {
    if (!state.isConfirmButtonEnabled) return;
    if (!isClosed) {
      emit(state.copyWith(isLoading: true));
    }
    try {
      // Get the selected country object using the selected country index
      final selectedCountry = state.countries.elementAtOrNull(
        state.selectedCountryIndex,
      );
      final res =
          await _changeCredentialsUseCase(
            ProfileSettingsModel.phone(
              countryCode:
                  selectedCountry?.dialCode ?? state.selectedCountryCode,
              number: state.phoneNumber.removeSpaces(),
              regionCode: selectedCountry?.code ?? state.selectedCountryFlag,
            ),
          ).run();
      if (isClosed) return;
      emit(state.copyWith(isLoading: false));
      res.fold(
        (error) {
          log("Error occurred while changing phone number: $error");
        },
        (result) {
          log("Phone number changed successfully: $result");
          if (result) {
            _navigation.navigateToVerifyMobile(
              args: MobileOtpVerificationArgs(
                phoneNumber: state.phoneNumber,
                countryCode: state.selectedCountryCode,
                isUpdatingPhoneNumber:
                    state.updatePhoneArgs?.isUpdatingPhoneNumber ?? false,
                origin: state.updatePhoneArgs?.origin,
              ),
            );
          } else {
            emit(
              state.copyWith(
                errorMessage: PersonalDetailsError.generalError,
                isLoading: false,
              ),
            );
          }
        },
      );
    } catch (e) {
      if (!isClosed) {
        emit(
          //todo: need localization
          state.copyWith(
            errorMessage: PersonalDetailsError.generalError,
            isLoading: false,
          ),
        );
      }
    }
  }

  void _setGlobalAttributes({
    required String countryCode,
    required String mobileNumber,
  }) {
    String mobileWithCountryCode = '$countryCode$mobileNumber';
    // ignore: avoid-missing-enum-constant-in-map
    _analyticsService.setGlobalAttributes({
      AnalyticsGlobalAttributes.phone:
          mobileWithCountryCode.removeSpecialChars().hash(),
    });
  }
}
