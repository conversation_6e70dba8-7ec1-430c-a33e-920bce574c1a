import 'dart:developer';
import 'dart:io';

import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/presentation/country_selection/bloc/country_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

class CountrySelection extends StatelessWidget {
  const CountrySelection({super.key, this.onConfirm});
  final void Function(BuildContext context, String? selectedCountry)? onConfirm;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final duploTextStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return BlocProvider(
      create: (_) {
        diContainer<AnalyticsService>().sendEvent(
          eventType:
              OnboardingAnalyticsEvent.countrySelectorStart.eventType.name,
          eventName: OnboardingAnalyticsEvent.countrySelectorStart.eventName,
        );
        return diContainer<CountryBloc>()
          ..add(const CountryEvent.loadCountries());
      },
      child: BlocConsumer<CountryBloc, CountryState>(
        buildWhen: (previous, current) {
          return previous != current;
        },
        listener: (listenerContext, state) {
          if (state.currentState is Blacklisted) {
            _showBlacklistedCountryBottomSheet(listenerContext);
          }
          if (state.currentState is NeedRedirection) {
            _showNeedRedirectionCountryBottomSheet(listenerContext, state);
          }
        },
        builder: (blocContext, state) {
          final isTestRunning = Platform.environment.containsKey(
            'FLUTTER_TEST',
          );
          return switch (state.currentState) {
            Loading() => isTestRunning ? SizedBox() : LoadingView(),
            Success() || Blacklisted() || NeedRedirection() => Scaffold(
              backgroundColor: theme.background.bgPrimary,
              appBar: DuploAppBar(title: ""),
              bottomNavigationBar: SafeArea(
                top: false,
                child: Padding(
                  padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
                  child: DuploButton.defaultPrimary(
                    semanticsIdentifier: 'confirm',
                    title: localization.trader_confirm,
                    onTap: () => _onConfirmButtonPressed(blocContext),
                    trailingIcon: Assets.images.chevronRight.keyName,
                    isDisabled:
                        !state.isCheckboxSelected ||
                        state.selectedIndex == null ||
                        state.selectedIndex == -1,
                  ),
                ),
              ),
              body: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DuploText(
                        text: localization.trader_countryPickerTitle,
                        style: duploTextStyles.textXl,
                        fontWeight: DuploFontWeight.semiBold,
                        color: theme.text.textPrimary,
                        textAlign:
                            Directionality.of(blocContext) == TextDirection.ltr
                                ? TextAlign.left
                                : TextAlign.right,
                      ),
                      SizedBox(height: 8),
                      DuploText(
                        text: localization.trader_countryPickerBody,
                        style: duploTextStyles.textSm,
                        fontWeight: DuploFontWeight.regular,
                        color: theme.text.textSecondary,
                        textAlign:
                            Directionality.of(blocContext) == TextDirection.ltr
                                ? TextAlign.left
                                : TextAlign.right,
                      ),
                      SizedBox(height: 16),
                      DuploDropDown.selector(
                        semanticsIdentifier: 'country_of_residence',
                        context: blocContext,
                        bottomSheetTitle: localization.trader_selectCountry,
                        hint: localization.trader_countryOfResidence,
                        hintText: localization.trader_search,
                        dropDownItemModels:
                            (state.allCountriesData ?? [])
                                .map(
                                  (country) => DropDownItemModel(
                                    title: country.name,
                                    image: FlagProvider.getFlagFromCountryCode(
                                      country.code,
                                    ),
                                  ),
                                )
                                .toList(),
                        selectedIndex: state.selectedIndex ?? -1,
                        onChanged: (index) {
                          blocContext.read<CountryBloc>().add(
                            CountryEvent.selectCountry(index),
                          );
                        },
                        helperText: localization.trader_countryPickerHelper,
                      ),
                      SizedBox(height: 16),
                      DuploCheckBox(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.background.bgSecondary,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        title: localization.trader_countryPickerCheckBoxTitle,
                        body: localization.trader_countryPickerCheckBoxBody,
                        onChanged:
                            (value) =>
                                _onCheckboxChanged(value, blocContext, state),
                        currentValue: state.isCheckboxSelected,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Error() => SafeArea(
              child: Scaffold(
                body: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: DuploSpacing.spacing_xl_16,
                  ),
                  child: EmptyOrErrorStateComponent.error(
                    raiseIssueText: localization.trader_raiseATicket,
                    onRaiseIssue: () {
                      debugPrint('Raise ticket');
                    },
                    svgImage: onboarding.Assets.images.searchError.svg(),
                    title: localization.trader_somethingWentWrong,
                    description: localization.trader_countryLoadingMessage,
                    retryButtonText: localization.trader_tryAgain,
                    onTapRetry: () {
                      blocContext.read<CountryBloc>().add(
                        const CountryEvent.loadCountries(),
                      );
                    },
                  ),
                ),
              ),
            ),
          };
        },
      ),
    );
  }

  /// Handles the checkbox change event.
  ///
  /// If the country is not selected, it shows an error toast message.
  /// Otherwise, it updates the checkbox state in the [CountryBloc].
  ///
  /// [value] is the new value of the checkbox.
  /// [context] is the build context.
  /// [state] is the current state of the [CountryBloc].
  void _onCheckboxChanged(
    bool value,
    BuildContext context,
    CountryState state,
  ) {
    if (state.selectedIndex == -1) {
      final toast = DuploToast();

      toast.showToastMessage(
        context: context,
        widget: DuploToastMessage(
          titleMessage:
              EquitiLocalization.of(context).trader_countryNotSelected,
          descriptionMessage:
              EquitiLocalization.of(context).trader_pleaseSelectCountry,
          messageType: ToastMessageType.error,
          onLeadingAction: () {
            toast.hidesToastMessage();
          },
        ),
      );
      return;
    }
    context.read<CountryBloc>().add(CountryEvent.updateCheckbox(value));
  }

  void _onConfirmButtonPressed(BuildContext context) {
    final bloc = context.read<CountryBloc>();
    final state = bloc.state;
    final countriesList = state.allCountriesData ?? [];
    final selectedIndex = state.selectedIndex ?? -1;

    onConfirm?.call(
      context,
      selectedIndex != -1
          ? countriesList.elementAtOrNull(selectedIndex)?.name
          : null,
    );
    diContainer<AnalyticsService>().sendEvent(
      eventType: OnboardingAnalyticsEvent.countrySelected.eventType.name,
      eventName: OnboardingAnalyticsEvent.countrySelected.eventName,
      metadata: {
        'selectedCountry':
            countriesList.elementAtOrNull(selectedIndex)?.name ?? '',
        'brokerId':
            countriesList.elementAtOrNull(selectedIndex)?.brokerId ?? '',
      },
    );

    // ignore: avoid-missing-enum-constant-in-map
    diContainer<AnalyticsService>().setGlobalAttributes({
      AnalyticsGlobalAttributes.brokerId:
          countriesList.elementAtOrNull(selectedIndex)?.brokerId ?? '',
    });

    if (countriesList.elementAtOrNull(selectedIndex)?.blacklisted ?? false) {
      diContainer<AnalyticsService>().sendEvent(
        eventType:
            OnboardingAnalyticsEvent.blacklistedCountrySelected.eventType.name,
        eventName:
            OnboardingAnalyticsEvent.blacklistedCountrySelected.eventName,
        metadata: {
          'selectedCountry':
              countriesList.elementAtOrNull(selectedIndex)?.name ?? '',
          'brokerId':
              countriesList.elementAtOrNull(selectedIndex)?.brokerId ?? '',
        },
      );
    }

    if (countriesList.elementAtOrNull(selectedIndex)?.needRedirection ??
        false) {
      diContainer<AnalyticsService>().sendEvent(
        eventType: OnboardingAnalyticsEvent.brokerRedirect.eventType.name,
        eventName: OnboardingAnalyticsEvent.brokerRedirect.eventName,
        metadata: {
          'selectedCountry':
              countriesList.elementAtOrNull(selectedIndex)?.name ?? '',
          'redirectUrl':
              countriesList.elementAtOrNull(selectedIndex)?.redirectionUrl ??
              '',
        },
      );
    }

    bloc.add(const CountryEvent.onConfirmButtonPressed());
  }

  void _showBlacklistedCountryBottomSheet(BuildContext listenerContext) {
    final localization = EquitiLocalization.of(listenerContext);
    BaseErrorBottomSheet.show(
      onSecondaryButtonPressed: () {
        Navigator.of(listenerContext).pop();
      },
      title: localization.onboarding_blacklistedCountryTitle,
      body: localization.onboarding_blacklistedCountryBody,
      secondaryButtonText: localization.onboarding_blacklistedCountryButtonText,
      asset: onboarding.Assets.images.blocklistCountry.svg(
        height: 160,
        width: 160,
      ),
    );
  }

  void _showNeedRedirectionCountryBottomSheet(
    BuildContext listenerContext,
    CountryState state,
  ) {
    final localization = EquitiLocalization.of(listenerContext);
    BaseErrorBottomSheet.show(
      onPrimaryButtonPressed: () {
        _launchSignUpUrl(state);
        Navigator.of(listenerContext).pop();
      },
      title: localization.onboarding_redirectCountryTitle,
      body: localization.onboarding_redirectCountryBody,
      primaryButtonText: localization.onboarding_signUpNow,
      asset: onboarding.Assets.images.redirectCountry.svg(
        height: 160,
        width: 160,
      ),
    );
  }

  Future<void> _launchSignUpUrl(CountryState state) async {
    final allCountryModel = state.allCountriesData;
    final selectedIndex = state.selectedIndex;
    final selectedCountry = allCountryModel?.elementAtOrNull(
      selectedIndex ?? -1,
    );
    final url = selectedCountry?.redirectionUrl ?? 'https://equiti.com';
    if (!await launchUrl(Uri.parse(url))) {
      log('Could not launch $url');
    }
  }
}
