import 'dart:async';
import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:prelude/prelude.dart';
import 'package:user_verification/user_verification.dart';

part 'user_verification_bloc.freezed.dart';
part 'user_verification_event.dart';
part 'user_verification_state.dart';

class UserVerificationBloc
    extends Bloc<UserVerificationEvent, UserVerificationState>
    with DisposableMixin {
  final LoggerBase _logger;
  final GetVerificationTokenUsecase _getVerificationTokenUsecase;
  final StartVerificationUsecase _startVerificationUsecase;
  final OnboardingNavigation _navigation;
  final AnalyticsService _analyticsService;

  UserVerificationBloc(
    this._logger,
    this._getVerificationTokenUsecase,
    this._startVerificationUsecase,
    this._navigation,
    this._analyticsService,
  ) : super(UserVerificationState()) {
    on<_GetAccessToken>(_getAccessToken);
    on<_StartUserVerification>(_startUserVerification);
    on<_OnContinuePressed>(_onContinuePressed);
  }

  Future<void> _getAccessToken(
    _GetAccessToken event,
    Emitter<UserVerificationState> emit,
  ) async {
    // Todo (aakash): Can remove this event
    emit(state.copyWith(state: UserVerificationProgressState.loading()));
    final result = await _getVerificationTokenUsecase.getToken();

    result.fold(
      (exception) {
        emit(
          state.copyWith(
            state: UserVerificationProgressState.error(exception.toString()),
          ),
        );
      },
      (token) {
        emit(
          state.copyWith(
            state: UserVerificationProgressState.success(token.toString()),
          ),
        );
      },
    );
  }

  FutureOr<void> _startUserVerification(
    _StartUserVerification event,
    Emitter<UserVerificationState> emit,
  ) async {
    await _startVerificationUsecase.openVerificationProcess(
      event.config,
      event.statusHandler,
      event.accessToken,
    );
  }

  @override
  void onError(Object error, StackTrace stackTrace) {
    super.onError(error, stackTrace);
    _logger.logError(error.toString());
  }

  Future<void> _onContinuePressed(
    _OnContinuePressed event,
    Emitter<UserVerificationState> emit,
  ) async {
    emit(state.copyWith(state: UserVerificationProgressState.loading()));
    final result = await _getVerificationTokenUsecase.getToken();

    result.fold(
      (exception) {
        _analyticsService.sendEvent(
          eventType: OnboardingAnalyticsEvent.kycStartError.eventType.name,
          eventName: OnboardingAnalyticsEvent.kycStartError.eventName,
        );
        emit(
          state.copyWith(
            state: UserVerificationProgressState.error(exception.toString()),
          ),
        );
      },
      (token) async {
        emit(
          state.copyWith(
            state: UserVerificationProgressState.success(token.toString()),
          ),
        );
        add(
          UserVerificationEvent.startUserVerification(
            config: UserVerificationConfig(
              theme: DuploSumsubTheme(theme: event.theme).getTheme(),
              locale: event.locale,
              debug: true,
            ),
            statusHandler: (verificationStatus) {
              _handleNavigation(verificationStatus.currentStatus);
            },
            accessToken: token,
          ),
        );
      },
    );
  }

  void _handleNavigation(UserVerificationStatus currentStatus) {
    switch (currentStatus) {
      case UserVerificationStatus.Pending:
        _analyticsService.sendEvent(
          eventType: OnboardingAnalyticsEvent.kycPending.eventType.name,
          eventName: OnboardingAnalyticsEvent.kycPending.eventName,
        );
        _navigation.setAndNavigateToProgressTracker();
        break;
      case UserVerificationStatus.TemporarilyDeclined:
        _analyticsService.sendEvent(
          eventType:
              OnboardingAnalyticsEvent.kycTemporarilyDeclined.eventType.name,
          eventName: OnboardingAnalyticsEvent.kycTemporarilyDeclined.eventName,
        );
        _navigation.setAndNavigateToProgressTracker();
        break;
      case UserVerificationStatus.FinallyRejected:
        _analyticsService.sendEvent(
          eventType: OnboardingAnalyticsEvent.kycRejected.eventType.name,
          eventName: OnboardingAnalyticsEvent.kycRejected.eventName,
        );
        _navigation.setAndNavigateToProgressTracker();
        break;
      case UserVerificationStatus.Approved:
        _analyticsService.sendEvent(
          eventType: OnboardingAnalyticsEvent.kycSuccess.eventType.name,
          eventName: OnboardingAnalyticsEvent.kycSuccess.eventName,
        );
        _navigation.navigateToMorphFormBuilder();
      default:
        log("No need to navigate");
    }
  }
}
