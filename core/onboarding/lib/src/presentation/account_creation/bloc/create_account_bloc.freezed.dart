// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_account_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$CreateAccountEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateAccountEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateAccountEvent()';
}


}

/// @nodoc
class $CreateAccountEventCopyWith<$Res>  {
$CreateAccountEventCopyWith(CreateAccountEvent _, $Res Function(CreateAccountEvent) __);
}


/// @nodoc


class NavigateToCreateAccountMainScreen implements CreateAccountEvent {
  const NavigateToCreateAccountMainScreen({required this.createAccountFlow});
  

 final  domain.CreateAccountFlow createAccountFlow;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NavigateToCreateAccountMainScreenCopyWith<NavigateToCreateAccountMainScreen> get copyWith => _$NavigateToCreateAccountMainScreenCopyWithImpl<NavigateToCreateAccountMainScreen>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NavigateToCreateAccountMainScreen&&(identical(other.createAccountFlow, createAccountFlow) || other.createAccountFlow == createAccountFlow));
}


@override
int get hashCode => Object.hash(runtimeType,createAccountFlow);

@override
String toString() {
  return 'CreateAccountEvent.navigateToCreareAccountMainScreen(createAccountFlow: $createAccountFlow)';
}


}

/// @nodoc
abstract mixin class $NavigateToCreateAccountMainScreenCopyWith<$Res> implements $CreateAccountEventCopyWith<$Res> {
  factory $NavigateToCreateAccountMainScreenCopyWith(NavigateToCreateAccountMainScreen value, $Res Function(NavigateToCreateAccountMainScreen) _then) = _$NavigateToCreateAccountMainScreenCopyWithImpl;
@useResult
$Res call({
 domain.CreateAccountFlow createAccountFlow
});




}
/// @nodoc
class _$NavigateToCreateAccountMainScreenCopyWithImpl<$Res>
    implements $NavigateToCreateAccountMainScreenCopyWith<$Res> {
  _$NavigateToCreateAccountMainScreenCopyWithImpl(this._self, this._then);

  final NavigateToCreateAccountMainScreen _self;
  final $Res Function(NavigateToCreateAccountMainScreen) _then;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? createAccountFlow = null,}) {
  return _then(NavigateToCreateAccountMainScreen(
createAccountFlow: null == createAccountFlow ? _self.createAccountFlow : createAccountFlow // ignore: cast_nullable_to_non_nullable
as domain.CreateAccountFlow,
  ));
}


}

/// @nodoc


class FetchAccountCreationData implements CreateAccountEvent {
  const FetchAccountCreationData({required this.createAccountFlow});
  

 final  domain.CreateAccountFlow createAccountFlow;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FetchAccountCreationDataCopyWith<FetchAccountCreationData> get copyWith => _$FetchAccountCreationDataCopyWithImpl<FetchAccountCreationData>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FetchAccountCreationData&&(identical(other.createAccountFlow, createAccountFlow) || other.createAccountFlow == createAccountFlow));
}


@override
int get hashCode => Object.hash(runtimeType,createAccountFlow);

@override
String toString() {
  return 'CreateAccountEvent.fetchAccountCreationData(createAccountFlow: $createAccountFlow)';
}


}

/// @nodoc
abstract mixin class $FetchAccountCreationDataCopyWith<$Res> implements $CreateAccountEventCopyWith<$Res> {
  factory $FetchAccountCreationDataCopyWith(FetchAccountCreationData value, $Res Function(FetchAccountCreationData) _then) = _$FetchAccountCreationDataCopyWithImpl;
@useResult
$Res call({
 domain.CreateAccountFlow createAccountFlow
});




}
/// @nodoc
class _$FetchAccountCreationDataCopyWithImpl<$Res>
    implements $FetchAccountCreationDataCopyWith<$Res> {
  _$FetchAccountCreationDataCopyWithImpl(this._self, this._then);

  final FetchAccountCreationData _self;
  final $Res Function(FetchAccountCreationData) _then;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? createAccountFlow = null,}) {
  return _then(FetchAccountCreationData(
createAccountFlow: null == createAccountFlow ? _self.createAccountFlow : createAccountFlow // ignore: cast_nullable_to_non_nullable
as domain.CreateAccountFlow,
  ));
}


}

/// @nodoc


class SelectPlatform implements CreateAccountEvent {
  const SelectPlatform({required this.platform});
  

 final  AccountCreationPlatform platform;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SelectPlatformCopyWith<SelectPlatform> get copyWith => _$SelectPlatformCopyWithImpl<SelectPlatform>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SelectPlatform&&(identical(other.platform, platform) || other.platform == platform));
}


@override
int get hashCode => Object.hash(runtimeType,platform);

@override
String toString() {
  return 'CreateAccountEvent.selectPlatform(platform: $platform)';
}


}

/// @nodoc
abstract mixin class $SelectPlatformCopyWith<$Res> implements $CreateAccountEventCopyWith<$Res> {
  factory $SelectPlatformCopyWith(SelectPlatform value, $Res Function(SelectPlatform) _then) = _$SelectPlatformCopyWithImpl;
@useResult
$Res call({
 AccountCreationPlatform platform
});




}
/// @nodoc
class _$SelectPlatformCopyWithImpl<$Res>
    implements $SelectPlatformCopyWith<$Res> {
  _$SelectPlatformCopyWithImpl(this._self, this._then);

  final SelectPlatform _self;
  final $Res Function(SelectPlatform) _then;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? platform = null,}) {
  return _then(SelectPlatform(
platform: null == platform ? _self.platform : platform // ignore: cast_nullable_to_non_nullable
as AccountCreationPlatform,
  ));
}


}

/// @nodoc


class SelectAccountType implements CreateAccountEvent {
  const SelectAccountType({required this.accountType});
  

 final  String accountType;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SelectAccountTypeCopyWith<SelectAccountType> get copyWith => _$SelectAccountTypeCopyWithImpl<SelectAccountType>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SelectAccountType&&(identical(other.accountType, accountType) || other.accountType == accountType));
}


@override
int get hashCode => Object.hash(runtimeType,accountType);

@override
String toString() {
  return 'CreateAccountEvent.selectAccountType(accountType: $accountType)';
}


}

/// @nodoc
abstract mixin class $SelectAccountTypeCopyWith<$Res> implements $CreateAccountEventCopyWith<$Res> {
  factory $SelectAccountTypeCopyWith(SelectAccountType value, $Res Function(SelectAccountType) _then) = _$SelectAccountTypeCopyWithImpl;
@useResult
$Res call({
 String accountType
});




}
/// @nodoc
class _$SelectAccountTypeCopyWithImpl<$Res>
    implements $SelectAccountTypeCopyWith<$Res> {
  _$SelectAccountTypeCopyWithImpl(this._self, this._then);

  final SelectAccountType _self;
  final $Res Function(SelectAccountType) _then;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? accountType = null,}) {
  return _then(SelectAccountType(
accountType: null == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class SelectCurrency implements CreateAccountEvent {
  const SelectCurrency({required this.currency, this.currencyIndex});
  

 final  String currency;
 final  int? currencyIndex;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SelectCurrencyCopyWith<SelectCurrency> get copyWith => _$SelectCurrencyCopyWithImpl<SelectCurrency>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SelectCurrency&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.currencyIndex, currencyIndex) || other.currencyIndex == currencyIndex));
}


@override
int get hashCode => Object.hash(runtimeType,currency,currencyIndex);

@override
String toString() {
  return 'CreateAccountEvent.selectCurrency(currency: $currency, currencyIndex: $currencyIndex)';
}


}

/// @nodoc
abstract mixin class $SelectCurrencyCopyWith<$Res> implements $CreateAccountEventCopyWith<$Res> {
  factory $SelectCurrencyCopyWith(SelectCurrency value, $Res Function(SelectCurrency) _then) = _$SelectCurrencyCopyWithImpl;
@useResult
$Res call({
 String currency, int? currencyIndex
});




}
/// @nodoc
class _$SelectCurrencyCopyWithImpl<$Res>
    implements $SelectCurrencyCopyWith<$Res> {
  _$SelectCurrencyCopyWithImpl(this._self, this._then);

  final SelectCurrency _self;
  final $Res Function(SelectCurrency) _then;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? currency = null,Object? currencyIndex = freezed,}) {
  return _then(SelectCurrency(
currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,currencyIndex: freezed == currencyIndex ? _self.currencyIndex : currencyIndex // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

/// @nodoc


class SelectVariant implements CreateAccountEvent {
  const SelectVariant({required this.swapFree});
  

 final  bool swapFree;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SelectVariantCopyWith<SelectVariant> get copyWith => _$SelectVariantCopyWithImpl<SelectVariant>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SelectVariant&&(identical(other.swapFree, swapFree) || other.swapFree == swapFree));
}


@override
int get hashCode => Object.hash(runtimeType,swapFree);

@override
String toString() {
  return 'CreateAccountEvent.selectVariant(swapFree: $swapFree)';
}


}

/// @nodoc
abstract mixin class $SelectVariantCopyWith<$Res> implements $CreateAccountEventCopyWith<$Res> {
  factory $SelectVariantCopyWith(SelectVariant value, $Res Function(SelectVariant) _then) = _$SelectVariantCopyWithImpl;
@useResult
$Res call({
 bool swapFree
});




}
/// @nodoc
class _$SelectVariantCopyWithImpl<$Res>
    implements $SelectVariantCopyWith<$Res> {
  _$SelectVariantCopyWithImpl(this._self, this._then);

  final SelectVariant _self;
  final $Res Function(SelectVariant) _then;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? swapFree = null,}) {
  return _then(SelectVariant(
swapFree: null == swapFree ? _self.swapFree : swapFree // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc


class SelectLeverage implements CreateAccountEvent {
  const SelectLeverage({required this.leverage});
  

 final  String leverage;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SelectLeverageCopyWith<SelectLeverage> get copyWith => _$SelectLeverageCopyWithImpl<SelectLeverage>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SelectLeverage&&(identical(other.leverage, leverage) || other.leverage == leverage));
}


@override
int get hashCode => Object.hash(runtimeType,leverage);

@override
String toString() {
  return 'CreateAccountEvent.selectLeverage(leverage: $leverage)';
}


}

/// @nodoc
abstract mixin class $SelectLeverageCopyWith<$Res> implements $CreateAccountEventCopyWith<$Res> {
  factory $SelectLeverageCopyWith(SelectLeverage value, $Res Function(SelectLeverage) _then) = _$SelectLeverageCopyWithImpl;
@useResult
$Res call({
 String leverage
});




}
/// @nodoc
class _$SelectLeverageCopyWithImpl<$Res>
    implements $SelectLeverageCopyWith<$Res> {
  _$SelectLeverageCopyWithImpl(this._self, this._then);

  final SelectLeverage _self;
  final $Res Function(SelectLeverage) _then;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? leverage = null,}) {
  return _then(SelectLeverage(
leverage: null == leverage ? _self.leverage : leverage // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class UpdateNickname implements CreateAccountEvent {
  const UpdateNickname({required this.nickname});
  

 final  String nickname;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateNicknameCopyWith<UpdateNickname> get copyWith => _$UpdateNicknameCopyWithImpl<UpdateNickname>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateNickname&&(identical(other.nickname, nickname) || other.nickname == nickname));
}


@override
int get hashCode => Object.hash(runtimeType,nickname);

@override
String toString() {
  return 'CreateAccountEvent.updateNickname(nickname: $nickname)';
}


}

/// @nodoc
abstract mixin class $UpdateNicknameCopyWith<$Res> implements $CreateAccountEventCopyWith<$Res> {
  factory $UpdateNicknameCopyWith(UpdateNickname value, $Res Function(UpdateNickname) _then) = _$UpdateNicknameCopyWithImpl;
@useResult
$Res call({
 String nickname
});




}
/// @nodoc
class _$UpdateNicknameCopyWithImpl<$Res>
    implements $UpdateNicknameCopyWith<$Res> {
  _$UpdateNicknameCopyWithImpl(this._self, this._then);

  final UpdateNickname _self;
  final $Res Function(UpdateNickname) _then;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? nickname = null,}) {
  return _then(UpdateNickname(
nickname: null == nickname ? _self.nickname : nickname // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class UpdatePassword implements CreateAccountEvent {
  const UpdatePassword({required this.password});
  

 final  String password;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdatePasswordCopyWith<UpdatePassword> get copyWith => _$UpdatePasswordCopyWithImpl<UpdatePassword>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdatePassword&&(identical(other.password, password) || other.password == password));
}


@override
int get hashCode => Object.hash(runtimeType,password);

@override
String toString() {
  return 'CreateAccountEvent.updatePassword(password: $password)';
}


}

/// @nodoc
abstract mixin class $UpdatePasswordCopyWith<$Res> implements $CreateAccountEventCopyWith<$Res> {
  factory $UpdatePasswordCopyWith(UpdatePassword value, $Res Function(UpdatePassword) _then) = _$UpdatePasswordCopyWithImpl;
@useResult
$Res call({
 String password
});




}
/// @nodoc
class _$UpdatePasswordCopyWithImpl<$Res>
    implements $UpdatePasswordCopyWith<$Res> {
  _$UpdatePasswordCopyWithImpl(this._self, this._then);

  final UpdatePassword _self;
  final $Res Function(UpdatePassword) _then;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? password = null,}) {
  return _then(UpdatePassword(
password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class NavigateToNext implements CreateAccountEvent {
  const NavigateToNext({this.pageIndex});
  

 final  int? pageIndex;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NavigateToNextCopyWith<NavigateToNext> get copyWith => _$NavigateToNextCopyWithImpl<NavigateToNext>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NavigateToNext&&(identical(other.pageIndex, pageIndex) || other.pageIndex == pageIndex));
}


@override
int get hashCode => Object.hash(runtimeType,pageIndex);

@override
String toString() {
  return 'CreateAccountEvent.navigateToNext(pageIndex: $pageIndex)';
}


}

/// @nodoc
abstract mixin class $NavigateToNextCopyWith<$Res> implements $CreateAccountEventCopyWith<$Res> {
  factory $NavigateToNextCopyWith(NavigateToNext value, $Res Function(NavigateToNext) _then) = _$NavigateToNextCopyWithImpl;
@useResult
$Res call({
 int? pageIndex
});




}
/// @nodoc
class _$NavigateToNextCopyWithImpl<$Res>
    implements $NavigateToNextCopyWith<$Res> {
  _$NavigateToNextCopyWithImpl(this._self, this._then);

  final NavigateToNext _self;
  final $Res Function(NavigateToNext) _then;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? pageIndex = freezed,}) {
  return _then(NavigateToNext(
pageIndex: freezed == pageIndex ? _self.pageIndex : pageIndex // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

/// @nodoc


class NavigateToPrevious implements CreateAccountEvent {
  const NavigateToPrevious({this.pageIndex});
  

 final  int? pageIndex;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NavigateToPreviousCopyWith<NavigateToPrevious> get copyWith => _$NavigateToPreviousCopyWithImpl<NavigateToPrevious>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NavigateToPrevious&&(identical(other.pageIndex, pageIndex) || other.pageIndex == pageIndex));
}


@override
int get hashCode => Object.hash(runtimeType,pageIndex);

@override
String toString() {
  return 'CreateAccountEvent.navigateToPrevious(pageIndex: $pageIndex)';
}


}

/// @nodoc
abstract mixin class $NavigateToPreviousCopyWith<$Res> implements $CreateAccountEventCopyWith<$Res> {
  factory $NavigateToPreviousCopyWith(NavigateToPrevious value, $Res Function(NavigateToPrevious) _then) = _$NavigateToPreviousCopyWithImpl;
@useResult
$Res call({
 int? pageIndex
});




}
/// @nodoc
class _$NavigateToPreviousCopyWithImpl<$Res>
    implements $NavigateToPreviousCopyWith<$Res> {
  _$NavigateToPreviousCopyWithImpl(this._self, this._then);

  final NavigateToPrevious _self;
  final $Res Function(NavigateToPrevious) _then;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? pageIndex = freezed,}) {
  return _then(NavigateToPrevious(
pageIndex: freezed == pageIndex ? _self.pageIndex : pageIndex // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

/// @nodoc


class SubmitAccountCreationData implements CreateAccountEvent {
  const SubmitAccountCreationData({required this.createAccountFlow});
  

 final  domain.CreateAccountFlow createAccountFlow;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SubmitAccountCreationDataCopyWith<SubmitAccountCreationData> get copyWith => _$SubmitAccountCreationDataCopyWithImpl<SubmitAccountCreationData>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SubmitAccountCreationData&&(identical(other.createAccountFlow, createAccountFlow) || other.createAccountFlow == createAccountFlow));
}


@override
int get hashCode => Object.hash(runtimeType,createAccountFlow);

@override
String toString() {
  return 'CreateAccountEvent.submitAccountCreationData(createAccountFlow: $createAccountFlow)';
}


}

/// @nodoc
abstract mixin class $SubmitAccountCreationDataCopyWith<$Res> implements $CreateAccountEventCopyWith<$Res> {
  factory $SubmitAccountCreationDataCopyWith(SubmitAccountCreationData value, $Res Function(SubmitAccountCreationData) _then) = _$SubmitAccountCreationDataCopyWithImpl;
@useResult
$Res call({
 domain.CreateAccountFlow createAccountFlow
});




}
/// @nodoc
class _$SubmitAccountCreationDataCopyWithImpl<$Res>
    implements $SubmitAccountCreationDataCopyWith<$Res> {
  _$SubmitAccountCreationDataCopyWithImpl(this._self, this._then);

  final SubmitAccountCreationData _self;
  final $Res Function(SubmitAccountCreationData) _then;

/// Create a copy of CreateAccountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? createAccountFlow = null,}) {
  return _then(SubmitAccountCreationData(
createAccountFlow: null == createAccountFlow ? _self.createAccountFlow : createAccountFlow // ignore: cast_nullable_to_non_nullable
as domain.CreateAccountFlow,
  ));
}


}

/// @nodoc
mixin _$CreateAccountState {

 List<TradingPlatform>? get tradingPlatforms; AccountCreationRequestModel? get accountCreationRequestModel; int get selectedCurrencyIndex; int? get pageIndex; CreateAccountProgressState get progressState;
/// Create a copy of CreateAccountState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreateAccountStateCopyWith<CreateAccountState> get copyWith => _$CreateAccountStateCopyWithImpl<CreateAccountState>(this as CreateAccountState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateAccountState&&const DeepCollectionEquality().equals(other.tradingPlatforms, tradingPlatforms)&&(identical(other.accountCreationRequestModel, accountCreationRequestModel) || other.accountCreationRequestModel == accountCreationRequestModel)&&(identical(other.selectedCurrencyIndex, selectedCurrencyIndex) || other.selectedCurrencyIndex == selectedCurrencyIndex)&&(identical(other.pageIndex, pageIndex) || other.pageIndex == pageIndex)&&(identical(other.progressState, progressState) || other.progressState == progressState));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(tradingPlatforms),accountCreationRequestModel,selectedCurrencyIndex,pageIndex,progressState);

@override
String toString() {
  return 'CreateAccountState(tradingPlatforms: $tradingPlatforms, accountCreationRequestModel: $accountCreationRequestModel, selectedCurrencyIndex: $selectedCurrencyIndex, pageIndex: $pageIndex, progressState: $progressState)';
}


}

/// @nodoc
abstract mixin class $CreateAccountStateCopyWith<$Res>  {
  factory $CreateAccountStateCopyWith(CreateAccountState value, $Res Function(CreateAccountState) _then) = _$CreateAccountStateCopyWithImpl;
@useResult
$Res call({
 List<TradingPlatform>? tradingPlatforms, AccountCreationRequestModel? accountCreationRequestModel, int selectedCurrencyIndex, int? pageIndex, CreateAccountProgressState progressState
});


$AccountCreationRequestModelCopyWith<$Res>? get accountCreationRequestModel;$CreateAccountProgressStateCopyWith<$Res> get progressState;

}
/// @nodoc
class _$CreateAccountStateCopyWithImpl<$Res>
    implements $CreateAccountStateCopyWith<$Res> {
  _$CreateAccountStateCopyWithImpl(this._self, this._then);

  final CreateAccountState _self;
  final $Res Function(CreateAccountState) _then;

/// Create a copy of CreateAccountState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? tradingPlatforms = freezed,Object? accountCreationRequestModel = freezed,Object? selectedCurrencyIndex = null,Object? pageIndex = freezed,Object? progressState = null,}) {
  return _then(_self.copyWith(
tradingPlatforms: freezed == tradingPlatforms ? _self.tradingPlatforms : tradingPlatforms // ignore: cast_nullable_to_non_nullable
as List<TradingPlatform>?,accountCreationRequestModel: freezed == accountCreationRequestModel ? _self.accountCreationRequestModel : accountCreationRequestModel // ignore: cast_nullable_to_non_nullable
as AccountCreationRequestModel?,selectedCurrencyIndex: null == selectedCurrencyIndex ? _self.selectedCurrencyIndex : selectedCurrencyIndex // ignore: cast_nullable_to_non_nullable
as int,pageIndex: freezed == pageIndex ? _self.pageIndex : pageIndex // ignore: cast_nullable_to_non_nullable
as int?,progressState: null == progressState ? _self.progressState : progressState // ignore: cast_nullable_to_non_nullable
as CreateAccountProgressState,
  ));
}
/// Create a copy of CreateAccountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountCreationRequestModelCopyWith<$Res>? get accountCreationRequestModel {
    if (_self.accountCreationRequestModel == null) {
    return null;
  }

  return $AccountCreationRequestModelCopyWith<$Res>(_self.accountCreationRequestModel!, (value) {
    return _then(_self.copyWith(accountCreationRequestModel: value));
  });
}/// Create a copy of CreateAccountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CreateAccountProgressStateCopyWith<$Res> get progressState {
  
  return $CreateAccountProgressStateCopyWith<$Res>(_self.progressState, (value) {
    return _then(_self.copyWith(progressState: value));
  });
}
}


/// @nodoc


class _CreateAccountState implements CreateAccountState {
   _CreateAccountState({final  List<TradingPlatform>? tradingPlatforms, this.accountCreationRequestModel, this.selectedCurrencyIndex = 0, this.pageIndex, this.progressState = const CreateAccountProgressState.dataLoading()}): _tradingPlatforms = tradingPlatforms;
  

 final  List<TradingPlatform>? _tradingPlatforms;
@override List<TradingPlatform>? get tradingPlatforms {
  final value = _tradingPlatforms;
  if (value == null) return null;
  if (_tradingPlatforms is EqualUnmodifiableListView) return _tradingPlatforms;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  AccountCreationRequestModel? accountCreationRequestModel;
@override@JsonKey() final  int selectedCurrencyIndex;
@override final  int? pageIndex;
@override@JsonKey() final  CreateAccountProgressState progressState;

/// Create a copy of CreateAccountState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CreateAccountStateCopyWith<_CreateAccountState> get copyWith => __$CreateAccountStateCopyWithImpl<_CreateAccountState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CreateAccountState&&const DeepCollectionEquality().equals(other._tradingPlatforms, _tradingPlatforms)&&(identical(other.accountCreationRequestModel, accountCreationRequestModel) || other.accountCreationRequestModel == accountCreationRequestModel)&&(identical(other.selectedCurrencyIndex, selectedCurrencyIndex) || other.selectedCurrencyIndex == selectedCurrencyIndex)&&(identical(other.pageIndex, pageIndex) || other.pageIndex == pageIndex)&&(identical(other.progressState, progressState) || other.progressState == progressState));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_tradingPlatforms),accountCreationRequestModel,selectedCurrencyIndex,pageIndex,progressState);

@override
String toString() {
  return 'CreateAccountState(tradingPlatforms: $tradingPlatforms, accountCreationRequestModel: $accountCreationRequestModel, selectedCurrencyIndex: $selectedCurrencyIndex, pageIndex: $pageIndex, progressState: $progressState)';
}


}

/// @nodoc
abstract mixin class _$CreateAccountStateCopyWith<$Res> implements $CreateAccountStateCopyWith<$Res> {
  factory _$CreateAccountStateCopyWith(_CreateAccountState value, $Res Function(_CreateAccountState) _then) = __$CreateAccountStateCopyWithImpl;
@override @useResult
$Res call({
 List<TradingPlatform>? tradingPlatforms, AccountCreationRequestModel? accountCreationRequestModel, int selectedCurrencyIndex, int? pageIndex, CreateAccountProgressState progressState
});


@override $AccountCreationRequestModelCopyWith<$Res>? get accountCreationRequestModel;@override $CreateAccountProgressStateCopyWith<$Res> get progressState;

}
/// @nodoc
class __$CreateAccountStateCopyWithImpl<$Res>
    implements _$CreateAccountStateCopyWith<$Res> {
  __$CreateAccountStateCopyWithImpl(this._self, this._then);

  final _CreateAccountState _self;
  final $Res Function(_CreateAccountState) _then;

/// Create a copy of CreateAccountState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? tradingPlatforms = freezed,Object? accountCreationRequestModel = freezed,Object? selectedCurrencyIndex = null,Object? pageIndex = freezed,Object? progressState = null,}) {
  return _then(_CreateAccountState(
tradingPlatforms: freezed == tradingPlatforms ? _self._tradingPlatforms : tradingPlatforms // ignore: cast_nullable_to_non_nullable
as List<TradingPlatform>?,accountCreationRequestModel: freezed == accountCreationRequestModel ? _self.accountCreationRequestModel : accountCreationRequestModel // ignore: cast_nullable_to_non_nullable
as AccountCreationRequestModel?,selectedCurrencyIndex: null == selectedCurrencyIndex ? _self.selectedCurrencyIndex : selectedCurrencyIndex // ignore: cast_nullable_to_non_nullable
as int,pageIndex: freezed == pageIndex ? _self.pageIndex : pageIndex // ignore: cast_nullable_to_non_nullable
as int?,progressState: null == progressState ? _self.progressState : progressState // ignore: cast_nullable_to_non_nullable
as CreateAccountProgressState,
  ));
}

/// Create a copy of CreateAccountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountCreationRequestModelCopyWith<$Res>? get accountCreationRequestModel {
    if (_self.accountCreationRequestModel == null) {
    return null;
  }

  return $AccountCreationRequestModelCopyWith<$Res>(_self.accountCreationRequestModel!, (value) {
    return _then(_self.copyWith(accountCreationRequestModel: value));
  });
}/// Create a copy of CreateAccountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CreateAccountProgressStateCopyWith<$Res> get progressState {
  
  return $CreateAccountProgressStateCopyWith<$Res>(_self.progressState, (value) {
    return _then(_self.copyWith(progressState: value));
  });
}
}

/// @nodoc
mixin _$CreateAccountProgressState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateAccountProgressState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateAccountProgressState()';
}


}

/// @nodoc
class $CreateAccountProgressStateCopyWith<$Res>  {
$CreateAccountProgressStateCopyWith(CreateAccountProgressState _, $Res Function(CreateAccountProgressState) __);
}


/// @nodoc


class DataLoadingState implements CreateAccountProgressState {
  const DataLoadingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DataLoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateAccountProgressState.dataLoading()';
}


}




/// @nodoc


class DataLoadedState implements CreateAccountProgressState {
  const DataLoadedState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DataLoadedState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateAccountProgressState.dataLoaded()';
}


}




/// @nodoc


class DataLoadingError implements CreateAccountProgressState {
  const DataLoadingError();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DataLoadingError);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateAccountProgressState.dataLoadingError()';
}


}




/// @nodoc


class DataSubmittingState implements CreateAccountProgressState {
  const DataSubmittingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DataSubmittingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateAccountProgressState.dataSubmitting()';
}


}




/// @nodoc


class DataSubmittedState implements CreateAccountProgressState {
  const DataSubmittedState({required this.res});
  

 final  AccountCreationResponseData res;

/// Create a copy of CreateAccountProgressState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DataSubmittedStateCopyWith<DataSubmittedState> get copyWith => _$DataSubmittedStateCopyWithImpl<DataSubmittedState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DataSubmittedState&&(identical(other.res, res) || other.res == res));
}


@override
int get hashCode => Object.hash(runtimeType,res);

@override
String toString() {
  return 'CreateAccountProgressState.dataSubmitted(res: $res)';
}


}

/// @nodoc
abstract mixin class $DataSubmittedStateCopyWith<$Res> implements $CreateAccountProgressStateCopyWith<$Res> {
  factory $DataSubmittedStateCopyWith(DataSubmittedState value, $Res Function(DataSubmittedState) _then) = _$DataSubmittedStateCopyWithImpl;
@useResult
$Res call({
 AccountCreationResponseData res
});


$AccountCreationResponseDataCopyWith<$Res> get res;

}
/// @nodoc
class _$DataSubmittedStateCopyWithImpl<$Res>
    implements $DataSubmittedStateCopyWith<$Res> {
  _$DataSubmittedStateCopyWithImpl(this._self, this._then);

  final DataSubmittedState _self;
  final $Res Function(DataSubmittedState) _then;

/// Create a copy of CreateAccountProgressState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? res = null,}) {
  return _then(DataSubmittedState(
res: null == res ? _self.res : res // ignore: cast_nullable_to_non_nullable
as AccountCreationResponseData,
  ));
}

/// Create a copy of CreateAccountProgressState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountCreationResponseDataCopyWith<$Res> get res {
  
  return $AccountCreationResponseDataCopyWith<$Res>(_self.res, (value) {
    return _then(_self.copyWith(res: value));
  });
}
}

/// @nodoc


class DataSubmitError implements CreateAccountProgressState {
  const DataSubmitError({this.message});
  

 final  String? message;

/// Create a copy of CreateAccountProgressState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DataSubmitErrorCopyWith<DataSubmitError> get copyWith => _$DataSubmitErrorCopyWithImpl<DataSubmitError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DataSubmitError&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'CreateAccountProgressState.dataSubmitError(message: $message)';
}


}

/// @nodoc
abstract mixin class $DataSubmitErrorCopyWith<$Res> implements $CreateAccountProgressStateCopyWith<$Res> {
  factory $DataSubmitErrorCopyWith(DataSubmitError value, $Res Function(DataSubmitError) _then) = _$DataSubmitErrorCopyWithImpl;
@useResult
$Res call({
 String? message
});




}
/// @nodoc
class _$DataSubmitErrorCopyWithImpl<$Res>
    implements $DataSubmitErrorCopyWith<$Res> {
  _$DataSubmitErrorCopyWithImpl(this._self, this._then);

  final DataSubmitError _self;
  final $Res Function(DataSubmitError) _then;

/// Create a copy of CreateAccountProgressState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = freezed,}) {
  return _then(DataSubmitError(
message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class DataUpdatedState implements CreateAccountProgressState {
  const DataUpdatedState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DataUpdatedState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateAccountProgressState.dataUpdated()';
}


}




/// @nodoc


class NavigatedNextState implements CreateAccountProgressState {
  const NavigatedNextState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NavigatedNextState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateAccountProgressState.navigatedNext()';
}


}




/// @nodoc


class NavigatedBackState implements CreateAccountProgressState {
  const NavigatedBackState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NavigatedBackState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CreateAccountProgressState.navigatedBack()';
}


}




// dart format on
