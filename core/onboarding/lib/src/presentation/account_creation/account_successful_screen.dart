// ignore_for_file: prefer-number-format
import 'package:broker_settings/broker_settings.dart';
import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:onboarding/src/data/account_creation_response_model.dart';
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:onboarding/src/assets/assets.gen.dart' as onboarding;
import 'package:onboarding/src/navigation/onboarding_route_schema.dart';

class AccountSuccessfulScreen extends StatelessWidget {
  const AccountSuccessfulScreen({
    super.key,
    required this.data,
    required this.createAccountFlow,
    required this.platform,
  });

  final AccountCreationResponseData data;
  final CreateAccountFlow createAccountFlow;
  final AccountCreationPlatform platform;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      body: SafeArea(
        top: false,
        child: Column(
          children: [
            Expanded(
              flex: 40,
              child: DuploLottieView.asset(
                lightAnimation:
                    onboarding.Assets.lotties.accountCreationSuccessful,
                darkAnimation:
                    onboarding.Assets.lotties.accountCreationSuccessful,
                alignment: Alignment.topCenter,
                fit: BoxFit.fill,
              ),
            ),
            Expanded(
              flex: 60,
              child: Semantics(
                identifier: "account_successful_scroll",
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      const SizedBox(height: 28),
                      DuploText(
                        text:
                            createAccountFlow == CreateAccountFlow.demoAccount
                                ? localization.onboarding_demoAccountCreated
                                : localization.onboarding_liveAccountCreated,
                        style: textStyles.textXl,
                        fontWeight: DuploFontWeight.semiBold,
                        color: theme.text.textPrimary,
                      ),
                      const SizedBox(height: 28),
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          color: theme.background.bgSecondary,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: _AccountInformation(
                          createAccountFlow: createAccountFlow,
                          data: data,
                          platform: platform,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            _CtaButtons(createAccountFlow: createAccountFlow),
          ],
        ),
      ),
    );
  }
}

class _AccountInformation extends StatelessWidget {
  const _AccountInformation({
    required this.createAccountFlow,
    required this.data,
    required this.platform,
  });

  final CreateAccountFlow createAccountFlow;
  final AccountCreationResponseData data;
  final AccountCreationPlatform platform;

  @override
  Widget build(BuildContext context) {
    return switch (platform) {
      AccountCreationPlatform.mt4 || AccountCreationPlatform.mt5 =>
        _MtAccountInfo(data: data, platform: platform),
      AccountCreationPlatform.dulcimer => _DulcimerAccountInfo(data: data),
    };
  }
}

class _DulcimerAccountInfo extends StatelessWidget {
  const _DulcimerAccountInfo({required this.data});

  final AccountCreationResponseData data;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            DuploText(
              text: localization.onboarding_nickname,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.medium,
              color: theme.text.textSecondary,
            ),
            DuploText(
              text: data.accountNickname,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textSecondary,
            ),
          ],
        ),
        const SizedBox(height: 26),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DuploText(
              text: localization.onboarding_accountType,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.medium,
              color: theme.text.textSecondary,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                DuploText(
                  text: data.platformAccountType,
                  style: textStyles.textXs,
                  fontWeight: DuploFontWeight.semiBold,
                  color: theme.text.textSecondary,
                ),
                if (data.swapFreeAccount) ...[
                  const SizedBox(height: 4),
                  DuploText(
                    text: localization.onboarding_swap_freeAccount,
                    style: textStyles.textXs,
                    color: theme.text.textSecondary,
                  ),
                ],
              ],
            ),
          ],
        ),
        const SizedBox(height: 26),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            DuploText(
              text: localization.onboarding_accountLeverage,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.medium,
              color: theme.text.textSecondary,
            ),
            DuploText(
              text: '1:${data.leverage}',
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textSecondary,
            ),
          ],
        ),
        const SizedBox(height: 26),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            DuploText(
              text: localization.onboarding_currency,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.medium,
              color: theme.text.textSecondary,
            ),
            DuploText(
              text: data.currency,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textSecondary,
            ),
          ],
        ),
      ],
    );
  }
}

class _MtAccountInfo extends StatelessWidget {
  const _MtAccountInfo({required this.data, required this.platform});

  final AccountCreationResponseData data;
  final AccountCreationPlatform platform;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            DuploText(
              text: localization.onboarding_nickname,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.medium,
              color: theme.text.textSecondary,
            ),
            DuploText(
              text: data.accountNickname,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textSecondary,
            ),
          ],
        ),
        const SizedBox(height: 26),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DuploText(
              text: localization.onboarding_accountType,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.medium,
              color: theme.text.textSecondary,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                DuploText(
                  text: data.platformAccountType,
                  style: textStyles.textXs,
                  fontWeight: DuploFontWeight.semiBold,
                  color: theme.text.textSecondary,
                ),
                if (data.swapFreeAccount) ...[
                  const SizedBox(height: 4),
                  DuploText(
                    text: localization.onboarding_swap_freeAccount,
                    style: textStyles.textXs,
                    color: theme.text.textSecondary,
                  ),
                ],
              ],
            ),
          ],
        ),
        const SizedBox(height: 26),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            DuploText(
              text: localization.onboarding_platform,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.medium,
              color: theme.text.textSecondary,
            ),
            DuploText(
              text: platform.name,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textSecondary,
            ),
          ],
        ),
        const SizedBox(height: 26),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            DuploText(
              text: localization.onboarding_serverName,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.medium,
              color: theme.text.textSecondary,
            ),
            DuploText(
              text: data.tradingServerCode ?? '',
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textSecondary,
            ),
          ],
        ),
        const SizedBox(height: 26),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            DuploText(
              text: localization.onboarding_accountLeverage,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.medium,
              color: theme.text.textSecondary,
            ),
            DuploText(
              text: '1:${data.leverage}',
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textSecondary,
            ),
          ],
        ),
        const SizedBox(height: 26),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            DuploText(
              text: localization.onboarding_currency,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.medium,
              color: theme.text.textSecondary,
            ),
            DuploText(
              text: data.currency,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textSecondary,
            ),
          ],
        ),
      ],
    );
  }
}

class _CtaButtons extends StatelessWidget {
  const _CtaButtons({required this.createAccountFlow});

  final CreateAccountFlow createAccountFlow;

  @override
  Widget build(BuildContext context) {
    return switch (createAccountFlow) {
      CreateAccountFlow.firstLiveAccount =>
        const _FirstLiveAccountFlowCtaButtons(),
      CreateAccountFlow.additionalLiveAccount =>
        const _AdditionalLiveAccountFlowCtaButtons(),
      CreateAccountFlow.demoAccount => const _DemoAccountFlowCtaButtons(),
    };
  }
}

class _FirstLiveAccountFlowCtaButtons extends StatelessWidget {
  const _FirstLiveAccountFlowCtaButtons();

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          DuploButton.defaultPrimary(
            semanticsIdentifier: 'onboarding_create_account',
            title: localization.onboarding_fundAccount,
            isLoading: false,
            trailingIcon: onboarding.Assets.images.continueIc.keyName,
            onTap: () {
              diContainer<OnboardingNavigation>().navigateToDepositIntro();
            },
            useFullWidth: true,
          ),
          const SizedBox(height: 12),
          DuploButton.link(
            semanticsIdentifier: "onboarding_create_account_skip",
            title: localization.onboarding_backToHome,
            onTap: () {
              diContainer<OnboardingNavigation>().navigateToHub(replace: true);
            },
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

class _AdditionalLiveAccountFlowCtaButtons extends StatelessWidget {
  const _AdditionalLiveAccountFlowCtaButtons();

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          DuploButton.defaultPrimary(
            semanticsIdentifier: 'create_account_continue_button',
            title: localization.onboarding_fundAccount,
            isLoading: false,
            trailingIcon: onboarding.Assets.images.continueIc.keyName,
            onTap: _onAddFundsTap,
            useFullWidth: true,
          ),
          const SizedBox(height: 12),
          DuploButton.link(
            semanticsIdentifier: 'create_account_back_button',
            title: localization.onboarding_backToAccounts,
            onTap: () {
              diContainer<OnboardingNavigation>()
                  .navigateBackToAccountsScreen();
            },
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

class _DemoAccountFlowCtaButtons extends StatelessWidget {
  const _DemoAccountFlowCtaButtons();

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          DuploButton.defaultPrimary(
            title: localization.onboarding_backToAccounts,
            isLoading: false,
            trailingIcon: onboarding.Assets.images.continueIc.keyName,
            onTap: () {
              diContainer<OnboardingNavigation>()
                  .navigateBackToAccountsScreen();
            },
            useFullWidth: true,
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

void _onAddFundsTap() {
  final depositFlowConfig = DepositFlowConfig(
    depositType: DepositType.first,
    origin: OnboardingRouteSchema.accountSuccessfulRoute.url,
  );
  diContainer<OnboardingNavigation>().navigateToDepositPaymentOptions(
    depositFlowConfig: depositFlowConfig,
  );
}
