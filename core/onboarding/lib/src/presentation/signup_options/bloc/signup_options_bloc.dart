import 'dart:async';

import 'package:api_client/api_client.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:login/login.dart';
import 'package:login/src/domain/error_code.dart';
import 'package:monitoring/monitoring.dart';
import 'package:onboarding/src/domain/exceptions/sign_up_with_okta_exception/sign_up_with_okta_exception.dart';
import 'package:onboarding/src/domain/usecase/signup_with_okta_usecase.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:prelude/prelude.dart';

part 'signup_options_bloc.freezed.dart';
part 'signup_options_event.dart';
part 'signup_options_state.dart';

class SignupOptionsBloc extends Bloc<SignupOptionsEvent, SignupOptionsState> {
  final OnboardingNavigation _onboardingNavigation;
  final SignupWithOktaUsecase _signupUseCase;
  final SignupWithUaePassUsecase _signupWithUaePassUsecase;
  final LoggerBase _logger;
  final AnalyticsService _analyticsService;

  SignupOptionsBloc(
    this._onboardingNavigation,
    this._signupUseCase,
    this._logger,
    this._signupWithUaePassUsecase,
    this._analyticsService,
  ) : super(SignupOptionsState()) {
    on<_NavigateToLogin>(_navigateToLogin);
    on<_StartSignup>(_startSignup);
    on<_SignUpWithUaePass>(_signUpWithUaePass);
    on<_BottomSheetClosed>(_bottomSheetClosed);
  }

  FutureOr<void> _navigateToLogin(
    _NavigateToLogin event,
    Emitter<SignupOptionsState> emit,
  ) {
    _onboardingNavigation.goToLoginOptions(replace: true);
  }

  FutureOr<void> _startSignup(
    _StartSignup event,
    Emitter<SignupOptionsState> emit,
  ) async {
    if (!isClosed) {
      emit(state.copyWith(processState: SignupOptionsProcessState.loading()));
    }
    final result =
        await _signupUseCase(
          country: event.country,
          countryCode: event.countryCode,
          brokerId: event.brokerId,
          city: event.city,
        ).run();
    result.fold(
      (exception) {
        //todo: handle error properly
        _analyticsService.sendEvent(
          eventType: OnboardingAnalyticsEvent.signupOktaError.eventType.name,
          eventName: OnboardingAnalyticsEvent.signupOktaError.eventName,
        );
        if (exception is SignUpWithOktaException) {
          switch (exception) {
            case SignUpWithOktaUnknownError():
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: SignupOptionsProcessState.initial(),
                  ),
                );
              }
          }
        } else {
          if (!isClosed) {
            emit(
              state.copyWith(processState: SignupOptionsProcessState.initial()),
            );
          }
        }
      },
      (authResult) {
        _logger.logInfo('Success! Access token: ${authResult.toString()}');
        if (!isClosed) {
          emit(
            state.copyWith(processState: SignupOptionsProcessState.initial()),
          );
        }
        final email = authResult.userData?.email;
        if (email != null) {
          // ignore: avoid-missing-enum-constant-in-map
          _analyticsService.setGlobalAttributes({
            AnalyticsGlobalAttributes.hashEmail: email.hash(),
            AnalyticsGlobalAttributes.email: email,
          });
        }
        _analyticsService.sendEvent(
          eventType: OnboardingAnalyticsEvent.signupSuccess.eventType.name,
          eventName: OnboardingAnalyticsEvent.signupSuccess.eventName,
          metadata: {'signupChannel': 'okta'},
        );
        _onboardingNavigation.navigateToMorphFormBuilder();
      },
    );
  }

  FutureOr<void> _signUpWithUaePass(
    _SignUpWithUaePass event,
    Emitter<SignupOptionsState> emit,
  ) async {
    if (!isClosed) {
      emit(state.copyWith(processState: SignupOptionsProcessState.loading()));
    }
    final result =
        await _signupWithUaePassUsecase(
          country: event.country,
          countryCode: event.countryCode,
          brokerId: event.brokerId,
          city: event.city,
        ).run();
    result.fold(
      (exception) {
        // Handle UAE Pass exceptions
        _analyticsService.sendEvent(
          eventType: OnboardingAnalyticsEvent.signupUaePassError.eventType.name,
          eventName: OnboardingAnalyticsEvent.signupUaePassError.eventName,
        );
        if (exception is UaePassException) {
          switch (exception.code) {
            case UaePassExceptionCode.cancelled_by_user:
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: SignupOptionsProcessState.userCancelled(),
                  ),
                );
              }
              break;
            case UaePassExceptionCode.unknown:
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: SignupOptionsProcessState.error(
                      exception.message,
                    ),
                  ),
                );
              }
              break;
          }
          return;
        }
        // Handle token exchange api exceptions
        if (exception is ClientException) {
          final errorCode = exception.mobileBffBaseError?.errorCode;
          switch (errorCode) {
            case ErrorCode.USER_SIGNED_UP_WITH_DIFFERENT_CHANNEL:
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState:
                        SignupOptionsProcessState.userSignedUpWithDifferentChannel(),
                  ),
                );
              }
              break;
            default:
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: SignupOptionsProcessState.error(
                      exception.message,
                    ),
                  ),
                );
              }
          }
          return;
        }
        // Handle other exceptions
        if (!isClosed) {
          emit(
            state.copyWith(
              processState: SignupOptionsProcessState.error(
                exception.toString(),
              ),
            ),
          );
        }
      },
      (authResult) {
        _analyticsService.sendEvent(
          eventType: OnboardingAnalyticsEvent.signupSuccess.eventType.name,
          eventName: OnboardingAnalyticsEvent.signupSuccess.eventName,
          metadata: {'signupChannel': 'uaepass'},
        );
        _onboardingNavigation.navigateToMorphFormBuilder();
      },
    );
  }

  FutureOr<void> _bottomSheetClosed(
    _BottomSheetClosed event,
    Emitter<SignupOptionsState> emit,
  ) {
    if (!isClosed) {
      emit(state.copyWith(processState: SignupOptionsProcessState.initial()));
    }
  }
}
