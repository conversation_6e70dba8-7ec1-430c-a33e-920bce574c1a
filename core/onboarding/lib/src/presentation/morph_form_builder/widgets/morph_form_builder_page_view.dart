import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/data/form/section_model.dart';
import 'package:onboarding/src/data/form/section_submit_result.dart';
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/presentation/morph_form_builder/bloc/morph_form_builder_bloc.dart';
import 'package:onboarding/src/presentation/morph_form_builder/widgets/morph_form_section_builder.dart';
import 'package:onboarding/src/presentation/morph_form_builder/widgets/morph_keep_alive_page.dart';
import 'package:onboarding/src/presentation/morph_form_builder/widgets/morph_progress_view.dart';
import 'package:prelude/prelude.dart';

class MorphFormBuilderPageView extends StatefulWidget {
  final List<SectionModel> sections;

  const MorphFormBuilderPageView({super.key, required this.sections});

  @override
  State<MorphFormBuilderPageView> createState() =>
      _MorphFormBuilderPageViewState();
}

class _MorphFormBuilderPageViewState extends State<MorphFormBuilderPageView> {
  late final PageController _pageController;
  int _currentPage = 0;
  List<SectionModel> sections = [];
  bool canPop = true;

  @override
  void initState() {
    super.initState();
    sections = [...widget.sections];
    _pageController = PageController(initialPage: 0);
    final formModel = context.read<MorphFormBuilderBloc>().state.formModel;
    diContainer<AnalyticsService>().sendEvent(
      eventType: OnboardingAnalyticsEvent.formStart.eventType.name,
      eventName: OnboardingAnalyticsEvent.formStart.eventName,
      metadata: {
        'formId': formModel?.formID,
        'formName': formModel?.name,
        'formVersion': formModel?.formVersion,
      },
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final firstSection = sections.elementAtOrNull(_currentPage);
    final formBloc = context.read<MorphFormBuilderBloc>();
    return PopScope(
      canPop: canPop,
      onPopInvokedWithResult: (didPop, result) {
        _onBackButtonTap();
      },
      child: Scaffold(
        appBar:
            firstSection?.sectionTemplateType == 'introduction' ||
                    firstSection?.sectionTemplateType == 'risk_assessment'
                ? null
                : DuploAppBar(
                  title: firstSection?.sectionName ?? "",
                  leading:
                      Navigator.canPop(context)
                          ? IconTheme(
                            data: IconTheme.of(context).copyWith(
                              color: context.duploTheme.foreground.fgSecondary,
                            ),
                            child: IconButton(
                              icon: const Icon(Icons.arrow_back),
                              onPressed: _onBackButtonTap,
                            ),
                          )
                          : null,
                ),
        backgroundColor: context.duploTheme.background.bgPrimary,
        body: SafeArea(
          top: false,
          child: Column(
            children: [
              firstSection?.sectionTemplateType == 'introduction' ||
                      firstSection?.sectionTemplateType == 'risk_assessment'
                  ? SizedBox()
                  : MorphProgressView(
                    progressValue:
                        sections.elementAtOrNull(_currentPage)?.progress,
                    totalBarCount:
                        formBloc.state.nextFormData?.progress?.totalForms ?? 1,
                    currentBarIndex: _getCurrentBarIndex(),
                  ),
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: widget.sections.length,
                  onPageChanged: (index) {
                    setState(() {
                      _currentPage = index;
                    });
                  },
                  itemBuilder: (_, index) {
                    final section = sections.elementAtOrNull(index)!;
                    return MorphKeepAlivePage(
                      child: MorphFormSectionBuilder(
                        section: section,
                        onBackButtonTap: _onBackButtonTap,
                        onSubmit: onSubmit,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onBackButtonTap() {
    FocusManager.instance.primaryFocus?.unfocus();
    if (_currentPage == 0) {
      setState(() {
        canPop = true;
      });
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (Navigator.canPop(context)) {
          Navigator.of(context).pop();
        }
      });
      return;
    }
    context.read<MorphFormBuilderBloc>().add(
      MorphFormBuilderEvent.removeSectionCubit(
        section: sections.elementAtOrNull(_currentPage),
      ),
    );
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeIn,
    );
  }

  void onSubmit(SectionSubmitResult result) {
    log('onSubmit called with result: ${result.toString()}');
    final isNextPageAvailable =
        result.nextSectionId != null && result.nextSectionId != 0;
    if (isNextPageAvailable) {
      final nextSectionIndex = sections.indexWhere(
        (localSection) => localSection.sectionID == result.nextSectionId,
      );
      if (nextSectionIndex == -1) {
        log('Next section not found');
        return;
      }
      // this cant be null as if we didn't have this element nextSectionIndex would be -1
      final nextSection = sections.elementAtOrNull(nextSectionIndex)!;
      setState(() {
        // Remove next section from current position
        sections.removeAt(nextSectionIndex);
        // Insert it right after the current page
        sections.insert(_currentPage + 1, nextSection);

        canPop = false;
      });

      // Update the bloc
      final formBloc = context.read<MorphFormBuilderBloc>();
      formBloc.add(
        MorphFormBuilderEvent.createSectionCubit(
          section: nextSection,
          isInitial: false,
        ),
      );

      FocusManager.instance.primaryFocus?.unfocus();
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
      );
    } else {
      log('Form submission completed');
      context.read<MorphFormBuilderBloc>().add(
        MorphFormBuilderEvent.onSubmitPressed(),
      );
    }
  }

  int _getCurrentBarIndex() {
    final formBloc = context.read<MorphFormBuilderBloc>();

    final list = formBloc.state.nextFormData?.formIds;
    final index =
        (list?.indexWhere(
              (element) =>
                  element ==
                  formBloc.state.nextFormData?.progress?.currentFormId,
            ) ??
            0) +
        1;
    log(
      'Current bar index: ${EquitiFormatter.formatNumber(value: index, locale: 'en')}',
    );
    return index;
  }
}
