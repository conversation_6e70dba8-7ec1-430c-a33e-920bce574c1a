import 'package:duplo/duplo.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/presentation/city_selection/bloc/city_bloc.dart';

class CitySelection extends StatelessWidget {
  const CitySelection({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final duploTextStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return BlocProvider(
      create: (_) => diContainer<CityBloc>(),
      child: BlocBuilder<CityBloc, CityState>(
        buildWhen: (previous, current) {
          return previous != current;
        },
        builder: (blocContext, state) {
          return Scaffold(
            backgroundColor: theme.background.bgPrimary,
            appBar: DuploAppBar(title: ""),
            bottomNavigationBar: SafeArea(
              top: false,
              child: Padding(
                padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
                child: DuploButton.defaultPrimary(
                  semanticsIdentifier: "confirm_button",
                  title: localization.trader_confirm,
                  trailingIcon: Assets.images.chevronRight.keyName,
                  onTap: () => _onConfirmButtonPressed(blocContext),
                  isDisabled: state.selectedIndex == -1,
                ),
              ),
            ),
            body: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DuploText(
                      text: localization.trader_cityPickerTitle,
                      style: duploTextStyles.textXl,
                      fontWeight: DuploFontWeight.semiBold,
                      color: theme.text.textPrimary,
                      textAlign:
                          Directionality.of(blocContext) == TextDirection.ltr
                              ? TextAlign.left
                              : TextAlign.right,
                    ),
                    SizedBox(height: 8),
                    DuploText(
                      text: localization.trader_cityPickerBody,
                      style: duploTextStyles.textSm,
                      fontWeight: DuploFontWeight.regular,
                      color: theme.text.textSecondary,
                      textAlign:
                          Directionality.of(blocContext) == TextDirection.ltr
                              ? TextAlign.left
                              : TextAlign.right,
                    ),
                    SizedBox(height: 16),
                    DuploDropDown.selector(
                      semanticsIdentifier: "city_drop_down",
                      context: blocContext,
                      bottomSheetTitle: localization.trader_selectCity,
                      hint: localization.trader_cityEmirate,
                      hintText: localization.trader_search,
                      isLoading:
                          state.currentState == CityProcessState.loading(),
                      dropDownItemModels:
                          state.cities
                              ?.map(
                                (city) => DropDownItemModel(title: city.name),
                              )
                              .toList() ??
                          [],
                      selectedIndex: state.selectedIndex,
                      onChanged: (index) {
                        blocContext.read<CityBloc>().add(
                          CityEvent.selectCity(index),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _onConfirmButtonPressed(BuildContext context) {
    final bloc = context.read<CityBloc>();
    final state = bloc.state;
    final selectedCity = state.cities?.elementAtOrNull(state.selectedIndex);
    diContainer<AnalyticsService>().sendEvent(
      eventType: OnboardingAnalyticsEvent.citySelected.eventType.name,
      eventName: OnboardingAnalyticsEvent.citySelected.eventName,
      metadata: {'selectedCity': selectedCity?.name},
    );
    bloc.add(CityEvent.onConfirmButtonPressed(selectedCity: selectedCity));
  }
}
