//@GeneratedMicroModule;OnboardingPackageModule;package:onboarding/src/di/di_initializer.module.dart
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i687;

import 'package:api_client/api_client.dart' as _i633;
import 'package:broker_settings/broker_settings.dart' as _i389;
import 'package:domain/domain.dart' as _i494;
import 'package:equiti_analytics/equiti_analytics.dart' as _i917;
import 'package:equiti_auth/equiti_auth.dart' as _i313;
import 'package:injectable/injectable.dart' as _i526;
import 'package:login/login.dart' as _i944;
import 'package:monitoring/monitoring.dart' as _i472;
import 'package:onboarding/src/di/onboarding_module.dart' as _i787;
import 'package:onboarding/src/domain/repository/create_account_repository.dart'
    as _i261;
import 'package:onboarding/src/domain/repository/mobile_otp_verification_repository.dart'
    as _i277;
import 'package:onboarding/src/domain/repository/morph_repository.dart' as _i73;
import 'package:onboarding/src/domain/repository/preferences_repository.dart'
    as _i248;
import 'package:onboarding/src/domain/repository/progress_tracker_repository.dart'
    as _i1012;
import 'package:onboarding/src/domain/repository/signup_repository.dart'
    as _i563;
import 'package:onboarding/src/domain/repository/user_registration_repository.dart'
    as _i430;
import 'package:onboarding/src/domain/usecase/add_phone_use_case.dart'
    as _i1064;
import 'package:onboarding/src/domain/usecase/get_next_form_model_use_case.dart'
    as _i346;
import 'package:onboarding/src/domain/usecase/get_selected_city_usecase.dart'
    as _i316;
import 'package:onboarding/src/domain/usecase/get_selected_country_usecase.dart'
    as _i1056;
import 'package:onboarding/src/domain/usecase/post_submit_form_use_case.dart'
    as _i887;
import 'package:onboarding/src/domain/usecase/progress_tracker_use_case.dart'
    as _i957;
import 'package:onboarding/src/domain/usecase/send_otp_usecase.dart' as _i410;
import 'package:onboarding/src/domain/usecase/set_selected_city_usecase.dart'
    as _i504;
import 'package:onboarding/src/domain/usecase/set_selected_country_usecase.dart'
    as _i614;
import 'package:onboarding/src/domain/usecase/show_welcome_screen_usecase.dart'
    as _i37;
import 'package:onboarding/src/domain/usecase/signup_with_okta_usecase.dart'
    as _i399;
import 'package:onboarding/src/domain/usecase/submit_account_creation_use_case.dart'
    as _i772;
import 'package:onboarding/src/domain/usecase/user_registration_use_case.dart'
    as _i461;
import 'package:onboarding/src/domain/usecase/verify_otp_usecase.dart' as _i393;
import 'package:onboarding/src/navigation/onboarding_navigation.dart' as _i141;
import 'package:onboarding/src/presentation/account_creation/bloc/create_account_bloc.dart'
    as _i617;
import 'package:onboarding/src/presentation/city_selection/bloc/city_bloc.dart'
    as _i384;
import 'package:onboarding/src/presentation/country_selection/bloc/country_bloc.dart'
    as _i375;
import 'package:onboarding/src/presentation/morph_form_builder/bloc/morph_form_builder_bloc.dart'
    as _i367;
import 'package:onboarding/src/presentation/personal_details_section/intro_pages/bloc/intro_page_bloc.dart'
    as _i885;
import 'package:onboarding/src/presentation/personal_details_section/mobile_number_input_view/bloc/mobile_number_input_bloc.dart'
    as _i447;
import 'package:onboarding/src/presentation/personal_details_section/otp_input_view/bloc/otp_input_view_bloc.dart'
    as _i775;
import 'package:onboarding/src/presentation/progress_tracker/bloc/progress_tracker_bloc.dart'
    as _i544;
import 'package:onboarding/src/presentation/signup_options/bloc/signup_options_bloc.dart'
    as _i441;
import 'package:onboarding/src/presentation/user_registration/bloc/user_registration_bloc.dart'
    as _i265;
import 'package:onboarding/src/presentation/user_registration_uae_pass/bloc/user_registration_uae_pass_bloc.dart'
    as _i12;
import 'package:onboarding/src/presentation/user_verification/bloc/user_verification_bloc.dart'
    as _i365;
import 'package:onboarding/src/presentation/welcome/bloc/welcome_bloc.dart'
    as _i304;
import 'package:preferences/preferences.dart' as _i695;
import 'package:user_account/user_account.dart' as _i43;
import 'package:user_verification/user_verification.dart' as _i881;

class OnboardingPackageModule extends _i526.MicroPackageModule {
  // initializes the registration of main-scope dependencies inside of GetIt
  @override
  _i687.FutureOr<void> init(_i526.GetItHelper gh) {
    final onboardingModule = _$OnboardingModule();
    gh.factory<_i881.UserVerificationManager>(
      () => onboardingModule.userVerificationManager(),
    );
    gh.factory<_i563.SignupRepository>(
      () => onboardingModule.signupRepository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
        gh<_i695.EquitiPreferences>(),
        gh<_i313.TokenManager>(),
        gh<_i313.AuthService>(),
      ),
    );
    gh.factory<_i881.StartVerificationUsecase>(
      () => onboardingModule.startVerificationUsecase(
        gh<_i881.UserVerificationManager>(),
      ),
    );
    gh.factory<_i277.MobileOtpVerificationRepository>(
      () => onboardingModule.mobileOtpVerificationRepository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
      ),
    );
    gh.factory<_i881.UserVerificationRepository>(
      () => onboardingModule.userVerificationRepository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
      ),
    );
    gh.factory<_i261.CreateAccountRepository>(
      () => onboardingModule.accountCreationRepository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
      ),
    );
    gh.factory<_i430.UserRegistrationRepository>(
      () => onboardingModule.userRegistrationRepository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
      ),
    );
    gh.factory<_i1012.ProgressTrackerRepository>(
      () => onboardingModule.progressTrackerRepository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
        gh<_i695.EquitiPreferences>(),
      ),
    );
    gh.factory<_i461.UserRegistrationUseCase>(
      () => onboardingModule.userRegistrationUseCase(
        gh<_i430.UserRegistrationRepository>(),
      ),
    );
    gh.factory<_i772.SubmitAccountCreationUseCase>(
      () => onboardingModule.submitAccountCreationUseCase(
        gh<_i261.CreateAccountRepository>(),
        gh<_i43.GetBrokerIdUseCase>(),
      ),
    );
    gh.factory<_i399.SignupWithOktaUsecase>(
      () =>
          onboardingModule.signupWithOktaUsecase(gh<_i563.SignupRepository>()),
    );
    gh.factory<_i441.SignupOptionsBloc>(
      () => onboardingModule.signupOptionsBloc(
        gh<_i141.OnboardingNavigation>(),
        gh<_i399.SignupWithOktaUsecase>(),
        gh<_i472.LoggerBase>(),
        gh<_i944.SignupWithUaePassUsecase>(),
        gh<_i917.AnalyticsService>(),
      ),
    );
    gh.factory<_i248.PreferencesRepository>(
      () =>
          onboardingModule.preferencesRepository(gh<_i695.EquitiPreferences>()),
    );
    gh.factory<_i617.CreateAccountBloc>(
      () => onboardingModule.accountCreationBloc(
        gh<_i141.OnboardingNavigation>(),
        gh<_i389.GetCreateLiveAccountDataUseCase>(),
        gh<_i389.GetCreateDemoAccountDataUseCase>(),
        gh<_i772.SubmitAccountCreationUseCase>(),
        gh<_i917.AnalyticsService>(),
      ),
    );
    gh.factory<_i73.MorphRepository>(
      () => onboardingModule.repository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
      ),
    );
    gh.factory<_i881.GetVerificationTokenUsecase>(
      () => onboardingModule.getVerificationTokenUsecase(
        gh<_i881.UserVerificationRepository>(),
        gh<_i43.GetUserRegistrationIdUseCase>(),
      ),
    );
    gh.factory<_i957.ProgressTrackerUseCase>(
      () => onboardingModule.progressTrackerUseCase(
        gh<_i1012.ProgressTrackerRepository>(),
      ),
    );
    gh.factory<_i365.UserVerificationBloc>(
      () => onboardingModule.userVerificationBloc(
        gh<_i472.LoggerBase>(),
        gh<_i881.GetVerificationTokenUsecase>(),
        gh<_i881.StartVerificationUsecase>(),
        gh<_i141.OnboardingNavigation>(),
        gh<_i917.AnalyticsService>(),
      ),
    );
    gh.factory<_i1064.AddPhoneUseCase>(
      () => onboardingModule.verifyMobileNumberUseCase(
        gh<_i277.MobileOtpVerificationRepository>(),
      ),
    );
    gh.factory<_i410.SendOtpUseCase>(
      () => onboardingModule.sendOtpUseCase(
        gh<_i277.MobileOtpVerificationRepository>(),
      ),
    );
    gh.factory<_i393.VerifyOtpUseCase>(
      () => onboardingModule.verifyOtpUseCase(
        gh<_i277.MobileOtpVerificationRepository>(),
      ),
    );
    gh.factory<_i265.UserRegistrationBloc>(
      () => onboardingModule.userRegistrationBloc(
        gh<_i461.UserRegistrationUseCase>(),
        gh<_i141.OnboardingNavigation>(),
      ),
    );
    gh.factory<_i12.UserRegistrationUaePassBloc>(
      () => onboardingModule.userRegistrationUaePassBloc(
        gh<_i461.UserRegistrationUseCase>(),
        gh<_i141.OnboardingNavigation>(),
      ),
    );
    gh.factory<_i544.ProgressTrackerBloc>(
      () => onboardingModule.progressTrackerBloc(
        gh<_i957.ProgressTrackerUseCase>(),
        gh<_i141.OnboardingNavigation>(),
        gh<_i944.LogoutUseCase>(),
      ),
    );
    gh.factory<_i775.OtpInputViewBloc>(
      () => onboardingModule.otpInputViewBloc(
        gh<_i141.OnboardingNavigation>(),
        gh<_i393.VerifyOtpUseCase>(),
        gh<_i410.SendOtpUseCase>(),
        gh<_i917.AnalyticsService>(),
      ),
    );
    gh.factory<_i346.GetNextFormModelUseCase>(
      () => onboardingModule.getFormModelUseCase(gh<_i73.MorphRepository>()),
    );
    gh.factory<_i887.PostSubmitFormUseCase>(
      () => onboardingModule.postSubmitFormUseCase(gh<_i73.MorphRepository>()),
    );
    gh.factory<_i37.ShowWelcomeScreenUseCase>(
      () => onboardingModule.hideWelcomeScreenUseCase(
        gh<_i248.PreferencesRepository>(),
      ),
    );
    gh.factory<_i614.SetSelectedCountryUseCase>(
      () => onboardingModule.setSelectedCountryUseCase(
        gh<_i248.PreferencesRepository>(),
      ),
    );
    gh.factory<_i1056.GetSelectedCountryUseCase>(
      () => onboardingModule.getSelectedCountryUseCase(
        gh<_i248.PreferencesRepository>(),
      ),
    );
    gh.factory<_i504.SetSelectedCityUseCase>(
      () => onboardingModule.setSelectedCityUseCase(
        gh<_i248.PreferencesRepository>(),
      ),
    );
    gh.factory<_i316.GetSelectedCityUseCase>(
      () => onboardingModule.getSelectedCityUseCase(
        gh<_i248.PreferencesRepository>(),
      ),
    );
    gh.factory<_i885.IntroPageBloc>(
      () => onboardingModule.introPageBloc(
        gh<_i141.OnboardingNavigation>(),
        gh<_i410.SendOtpUseCase>(),
        gh<_i43.GetUserIdUseCase>(),
      ),
    );
    gh.factory<_i447.MobileNumberInputBloc>(
      () => onboardingModule.mobileNumberInputBloc(
        gh<_i141.OnboardingNavigation>(),
        gh<_i1064.AddPhoneUseCase>(),
        gh<_i494.GetCountryUseCase>(),
        gh<_i494.ChangeCredentialsUseCase>(),
        gh<_i917.AnalyticsService>(),
      ),
    );
    gh.factory<_i367.MorphFormBuilderBloc>(
      () => onboardingModule.morphFormBuilderBloc(
        gh<_i957.ProgressTrackerUseCase>(),
        gh<_i346.GetNextFormModelUseCase>(),
        gh<_i887.PostSubmitFormUseCase>(),
        gh<_i43.GetUserRegistrationIdUseCase>(),
        gh<_i43.GetUserIdUseCase>(),
        gh<_i472.LoggerBase>(),
        gh<_i141.OnboardingNavigation>(),
        gh<_i917.AnalyticsService>(),
      ),
    );
    gh.factory<_i375.CountryBloc>(
      () => onboardingModule.countryBloc(
        gh<_i494.GetCountryUseCase>(),
        gh<_i141.OnboardingNavigation>(),
        gh<_i614.SetSelectedCountryUseCase>(),
      ),
    );
    gh.factory<_i384.CityBloc>(
      () => onboardingModule.cityBloc(
        gh<_i141.OnboardingNavigation>(),
        gh<_i504.SetSelectedCityUseCase>(),
        gh<_i1056.GetSelectedCountryUseCase>(),
      ),
    );
    gh.factory<_i304.WelcomeBloc>(
      () => onboardingModule.welcomeBloc(
        gh<_i37.ShowWelcomeScreenUseCase>(),
        gh<_i141.OnboardingNavigation>(),
      ),
    );
  }
}

class _$OnboardingModule extends _i787.OnboardingModule {}
