import 'package:api_client/api_client.dart';
import 'package:broker_settings/broker_settings.dart';
import 'package:domain/domain.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:injectable/injectable.dart';
import 'package:login/login.dart';
import 'package:monitoring/monitoring.dart';
import 'package:onboarding/src/di/di_container.dart';
import 'package:onboarding/src/domain/repository/create_account_repository.dart';
import 'package:onboarding/src/domain/repository/mobile_otp_verification_repository.dart';
import 'package:onboarding/src/domain/repository/morph_repository.dart';
import 'package:onboarding/src/domain/repository/preferences_repository.dart';
import 'package:onboarding/src/domain/repository/progress_tracker_repository.dart';
import 'package:onboarding/src/domain/repository/signup_repository.dart';
import 'package:onboarding/src/domain/repository/user_registration_repository.dart';
import 'package:onboarding/src/domain/usecase/add_phone_use_case.dart';
import 'package:onboarding/src/domain/usecase/get_next_form_model_use_case.dart';
import 'package:onboarding/src/domain/usecase/get_selected_city_usecase.dart';
import 'package:onboarding/src/domain/usecase/get_selected_country_usecase.dart';
import 'package:onboarding/src/domain/usecase/post_submit_form_use_case.dart';
import 'package:onboarding/src/domain/usecase/progress_tracker_use_case.dart';
import 'package:onboarding/src/domain/usecase/send_otp_usecase.dart';
import 'package:onboarding/src/domain/usecase/set_selected_city_usecase.dart';
import 'package:onboarding/src/domain/usecase/set_selected_country_usecase.dart';
import 'package:onboarding/src/domain/usecase/show_welcome_screen_usecase.dart';
import 'package:onboarding/src/domain/usecase/signup_with_okta_usecase.dart';
import 'package:onboarding/src/domain/usecase/submit_account_creation_use_case.dart';
import 'package:onboarding/src/domain/usecase/user_registration_use_case.dart';
import 'package:onboarding/src/domain/usecase/verify_otp_usecase.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';
import 'package:onboarding/src/presentation/account_creation/bloc/create_account_bloc.dart';
import 'package:onboarding/src/presentation/city_selection/bloc/city_bloc.dart';
import 'package:onboarding/src/presentation/country_selection/bloc/country_bloc.dart';
import 'package:onboarding/src/presentation/morph_form_builder/bloc/morph_form_builder_bloc.dart';
import 'package:onboarding/src/presentation/personal_details_section/intro_pages/bloc/intro_page_bloc.dart';
import 'package:onboarding/src/presentation/personal_details_section/mobile_number_input_view/bloc/mobile_number_input_bloc.dart';
import 'package:onboarding/src/presentation/personal_details_section/otp_input_view/bloc/otp_input_view_bloc.dart';
import 'package:onboarding/src/presentation/progress_tracker/bloc/progress_tracker_bloc.dart';
import 'package:onboarding/src/presentation/signup_options/bloc/signup_options_bloc.dart';
import 'package:onboarding/src/presentation/user_registration/bloc/user_registration_bloc.dart';
import 'package:onboarding/src/presentation/user_registration_uae_pass/bloc/user_registration_uae_pass_bloc.dart';
import 'package:onboarding/src/presentation/user_verification/bloc/user_verification_bloc.dart';
import 'package:onboarding/src/presentation/welcome/bloc/welcome_bloc.dart';
import 'package:preferences/preferences.dart';
import 'package:user_account/user_account.dart';
import 'package:user_verification/user_verification.dart';

@module
abstract class OnboardingModule {
  @injectable
  PreferencesRepository preferencesRepository(EquitiPreferences preferences) =>
      PreferencesRepository(preferences: preferences);

  @injectable
  SignupRepository signupRepository(
    @Named('mobileBffApiClient') ApiClientBase apiClient,
    EquitiPreferences equitiPreferences,
    TokenManager tokenManager,
    AuthService authService,
  ) => SignupRepository(
    apiClient: apiClient,
    equitiPreferences: equitiPreferences,
    tokenManager: tokenManager,
    authService: () => diContainer<AuthService>(),
  );

  @injectable
  SignupWithOktaUsecase signupWithOktaUsecase(SignupRepository repository) =>
      SignupWithOktaUsecase(repository);

  @injectable
  ShowWelcomeScreenUseCase hideWelcomeScreenUseCase(
    PreferencesRepository repository,
  ) => ShowWelcomeScreenUseCase(repository: repository);

  @injectable
  WelcomeBloc welcomeBloc(
    ShowWelcomeScreenUseCase showWelcomeScreenUseCase,
    OnboardingNavigation onboardingNavigation,
  ) => WelcomeBloc(
    showWelcomeScreenUseCase: showWelcomeScreenUseCase,
    onboardingNavigation: onboardingNavigation,
  );
  @injectable
  SignupOptionsBloc signupOptionsBloc(
    OnboardingNavigation onboardingNavigation,
    SignupWithOktaUsecase signupWithOktaUsecase,
    LoggerBase logger,
    SignupWithUaePassUsecase signupWithUaePassUsecase,
    AnalyticsService analyticsService,
  ) => SignupOptionsBloc(
    onboardingNavigation,
    signupWithOktaUsecase,
    logger,
    signupWithUaePassUsecase,
    analyticsService,
  );

  @injectable
  ProgressTrackerBloc progressTrackerBloc(
    ProgressTrackerUseCase progressTrackerUseCase,
    OnboardingNavigation onboardingNavigation,
    LogoutUseCase logoutUseCase,
  ) => ProgressTrackerBloc(
    progressTrackerUseCase: progressTrackerUseCase,
    onboardingNavigation: onboardingNavigation,
    logoutUseCase: logoutUseCase,
  );

  @injectable
  ProgressTrackerUseCase progressTrackerUseCase(
    ProgressTrackerRepository progressTrackerRepository,
  ) => ProgressTrackerUseCase(
    progressTrackerRepository: progressTrackerRepository,
  );

  @injectable
  ProgressTrackerRepository progressTrackerRepository(
    @Named('mobileBffApiClient') ApiClientBase apiClient,
    EquitiPreferences preferences,
  ) =>
      ProgressTrackerRepository(apiClient: apiClient, preferences: preferences);

  @injectable
  IntroPageBloc introPageBloc(
    OnboardingNavigation onboardingNavigation,
    SendOtpUseCase sendOtpUseCase,
    GetUserIdUseCase getUserIdUseCase,
  ) => IntroPageBloc(onboardingNavigation, sendOtpUseCase, getUserIdUseCase);

  @injectable
  MobileOtpVerificationRepository mobileOtpVerificationRepository(
    @Named('mobileBffApiClient') ApiClientBase apiClient,
  ) => MobileOtpVerificationRepository(apiClient: apiClient);

  @injectable
  AddPhoneUseCase verifyMobileNumberUseCase(
    MobileOtpVerificationRepository mobileOtpVerificationRepository,
  ) => AddPhoneUseCase(mobileOtpVerificationRepository);

  @injectable
  SendOtpUseCase sendOtpUseCase(
    MobileOtpVerificationRepository mobileOtpVerificationRepository,
  ) => SendOtpUseCase(repository: mobileOtpVerificationRepository);

  @injectable
  VerifyOtpUseCase verifyOtpUseCase(
    MobileOtpVerificationRepository mobileOtpVerificationRepository,
  ) => VerifyOtpUseCase(mobileOtpVerificationRepository);

  @injectable
  MobileNumberInputBloc mobileNumberInputBloc(
    OnboardingNavigation onboardingNavigation,
    AddPhoneUseCase verifyMobileNumberUseCase,
    GetCountryUseCase getCountriesUseCase,
    ChangeCredentialsUseCase changeCredentialsUseCase,
    AnalyticsService analyticsService,
  ) => MobileNumberInputBloc(
    onboardingNavigation,
    verifyMobileNumberUseCase,
    getCountriesUseCase,
    changeCredentialsUseCase,
    analyticsService,
  );

  @injectable
  OtpInputViewBloc otpInputViewBloc(
    OnboardingNavigation onboardingNavigation,
    VerifyOtpUseCase verifyOtpUseCase,
    SendOtpUseCase sendOtpUseCase,
    AnalyticsService analyticsService,
  ) => OtpInputViewBloc(
    onboardingNavigation,
    verifyOtpUseCase,
    sendOtpUseCase,
    analyticsService,
  );

  @injectable
  UserVerificationRepository userVerificationRepository(
    @Named('mobileBffApiClient') ApiClientBase apiClient,
  ) => UserVerificationRepository(apiClient: apiClient);

  @injectable
  UserVerificationManager userVerificationManager() =>
      UserVerificationManager();

  @injectable
  GetVerificationTokenUsecase getVerificationTokenUsecase(
    UserVerificationRepository repository,
    GetUserRegistrationIdUseCase getUserRegistrationIdUseCase,
  ) => GetVerificationTokenUsecase(repository, getUserRegistrationIdUseCase);

  @injectable
  StartVerificationUsecase startVerificationUsecase(
    UserVerificationManager manager,
  ) => StartVerificationUsecase(manager);

  @injectable
  UserVerificationBloc userVerificationBloc(
    LoggerBase logger,
    GetVerificationTokenUsecase getVerificationTokenUsecase,
    StartVerificationUsecase startVerificationUsecase,
    OnboardingNavigation onboardingNavigation,
    AnalyticsService analyticsService,
  ) => UserVerificationBloc(
    logger,
    getVerificationTokenUsecase,
    startVerificationUsecase,
    onboardingNavigation,
    analyticsService,
  );

  @injectable
  SetSelectedCountryUseCase setSelectedCountryUseCase(
    PreferencesRepository repository,
  ) => SetSelectedCountryUseCase(repository: repository);

  @injectable
  GetSelectedCountryUseCase getSelectedCountryUseCase(
    PreferencesRepository repository,
  ) => GetSelectedCountryUseCase(repository: repository);

  @injectable
  SetSelectedCityUseCase setSelectedCityUseCase(
    PreferencesRepository repository,
  ) => SetSelectedCityUseCase(repository: repository);

  @injectable
  GetSelectedCityUseCase getSelectedCityUseCase(
    PreferencesRepository repository,
  ) => GetSelectedCityUseCase(repository: repository);

  @injectable
  CountryBloc countryBloc(
    GetCountryUseCase getCountryUseCase,
    OnboardingNavigation onboardingNavigation,
    SetSelectedCountryUseCase setSelectedCountryUseCase,
  ) => CountryBloc(
    getCountryUseCase,
    onboardingNavigation,
    setSelectedCountryUseCase,
  );

  @injectable
  CityBloc cityBloc(
    OnboardingNavigation onboardingNavigation,
    SetSelectedCityUseCase setSelectedCityUseCase,
    GetSelectedCountryUseCase getSelectedCountryUseCase,
  ) => CityBloc(
    onboardingNavigation,
    setSelectedCityUseCase,
    getSelectedCountryUseCase,
  );

  @injectable
  MorphRepository repository(
    @Named('mobileBffApiClient') ApiClientBase apiClientBase,
  ) => MorphRepository(apiClientBase);

  @injectable
  GetNextFormModelUseCase getFormModelUseCase(MorphRepository repository) =>
      GetNextFormModelUseCase(repository);

  @injectable
  PostSubmitFormUseCase postSubmitFormUseCase(MorphRepository repository) =>
      PostSubmitFormUseCase(repository);

  @injectable
  MorphFormBuilderBloc morphFormBuilderBloc(
    ProgressTrackerUseCase progressTrackerUseCase,
    GetNextFormModelUseCase getFormModelUseCase,
    PostSubmitFormUseCase postSubmitFormUseCase,
    GetUserRegistrationIdUseCase getUserRegistrationIdUseCase,
    GetUserIdUseCase getUserIdUseCase,
    LoggerBase logger,
    OnboardingNavigation onboardingNavigation,
    AnalyticsService analyticsService,
  ) => MorphFormBuilderBloc(
    progressTrackerUseCase: progressTrackerUseCase,
    getFormModelUseCase: getFormModelUseCase,
    postSubmitFormUseCase: postSubmitFormUseCase,
    getUserRegistrationIdUseCase: getUserRegistrationIdUseCase,
    onboardingNavigation: onboardingNavigation,
    logger: logger,
    analyticsService: analyticsService,
  );

  @injectable
  CreateAccountRepository accountCreationRepository(
    @Named('mobileBffApiClient') ApiClientBase apiClient,
  ) => CreateAccountRepository(apiClient: apiClient);

  @injectable
  SubmitAccountCreationUseCase submitAccountCreationUseCase(
    CreateAccountRepository repository,
    GetBrokerIdUseCase getBrokerIdUseCase,
  ) => SubmitAccountCreationUseCase(
    accountRepository: repository,
    getBrokerIdUseCase: getBrokerIdUseCase,
  );

  @injectable
  UserRegistrationRepository userRegistrationRepository(
    @Named('mobileBffApiClient') ApiClientBase apiClient,
  ) => UserRegistrationRepository(apiClient: apiClient);

  @injectable
  UserRegistrationUseCase userRegistrationUseCase(
    UserRegistrationRepository repository,
  ) => UserRegistrationUseCase(repository: repository);

  @injectable
  UserRegistrationBloc userRegistrationBloc(
    UserRegistrationUseCase userRegistrationUseCase,
    OnboardingNavigation onboardingNavigation,
  ) => UserRegistrationBloc(
    userRegistrationUseCase: userRegistrationUseCase,
    onboardingNavigation: onboardingNavigation,
  );

  @injectable
  UserRegistrationUaePassBloc userRegistrationUaePassBloc(
    UserRegistrationUseCase userRegistrationUseCase,
    OnboardingNavigation onboardingNavigation,
  ) => UserRegistrationUaePassBloc(
    userRegistrationUseCase: userRegistrationUseCase,
    onboardingNavigation: onboardingNavigation,
  );

  @injectable
  CreateAccountBloc accountCreationBloc(
    OnboardingNavigation onboardingNavigation,
    GetCreateLiveAccountDataUseCase getCreateLiveAccountDataUseCase,
    GetCreateDemoAccountDataUseCase getCreateDemoAccountDataUseCase,
    SubmitAccountCreationUseCase submitAccountCreationUseCase,
    AnalyticsService analyticsService,
  ) => CreateAccountBloc(
    onboardingNavigation,
    getCreateLiveAccountDataUseCase,
    getCreateDemoAccountDataUseCase,
    submitAccountCreationUseCase,
    analyticsService,
  );
}
