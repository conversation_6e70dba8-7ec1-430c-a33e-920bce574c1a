import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:onboarding/src/data/progress_tracker/progress_state.dart';
import 'package:onboarding/src/data/progress_tracker/progress_tracker_response_model.dart';
import 'package:onboarding/src/navigation/onboarding_navigation.dart';

/// A class that determines the flow based on the current step in the progress tracker.
///
/// This is useful for deciding whether the app should:
/// - continue with the form building process (e.g., call an API for the next form)
/// - or navigate to a different page entirely.
///
/// Returns:
/// - `true` → proceed with the next form via API.
/// - `false` → navigate to a different screen (and replace the current one).
///
class Decider {
  /// Decides the action based on the current step.
  ///
  /// [progressTracker] is the model that contains the current step.
  /// Returns `true` if the next form should be built using the API,
  /// or `false` if the user should be navigated to another page.
  static bool decide({
    required ProgressTrackerData progressTracker,
    required OnboardingNavigation onboardingNavigation,
    required AnalyticsService analyticsService,
  }) {
    // If registration not started, navigate to add full name screen
    if (!progressTracker.registrationStarted) {
      handleRegistrationFlow(
        onboardingNavigation,
        progressTracker.authenticationChannel,
        progressTracker.idDetails,
      );
      return false;
    }

    // If phone number not added, navigate to phone number input screen
    if (progressTracker.phoneNumberAdded != null &&
        !progressTracker.phoneNumberAdded!) {
      handlePhoneNumberFlow(onboardingNavigation);
      return false;
    }

    final currentStep =
        progressTracker.currentStep ?? ProgressState.personalInfo;

    bool result = false;

    switch (currentStep) {
      case ProgressState.personalInfo:
        result = _handlePersonalInfo();
        break;
      case ProgressState.financialInfo:
        result = _handleFinancialInfo();
        break;
      case ProgressState.verifyIdentity:
        _handleVerifyIdentity(onboardingNavigation: onboardingNavigation);
        break;
      case ProgressState.accountCreation:
        _handleAccountCreation(onboardingNavigation: onboardingNavigation);
        break;
      case ProgressState.addFunds:
        _handleAddFunds(onboardingNavigation: onboardingNavigation);
        break;
      case ProgressState.placeTrade:
        _handlePlaceTrade(
          onboardingNavigation: onboardingNavigation,
          analyticsService: analyticsService,
        );
        break;
    }

    return result;
  }

  /// Handles the 'personalInfo' step.
  /// Returns true to continue to form building.
  static bool _handlePersonalInfo() {
    print("Navigating to Personal Info Step");
    return true;
  }

  /// Handles the 'financialInfo' step.
  /// Returns true to continue to form building.
  static bool _handleFinancialInfo() {
    print("Navigating to Financial Info Step");
    return true;
  }

  /// Handles the 'verifyIdentity' step.
  /// Returns false as it leads to a different screen.
  static void _handleVerifyIdentity({
    required OnboardingNavigation onboardingNavigation,
  }) {
    print("Navigating to Verify Identity Step");
    onboardingNavigation.navigateToVerifyIdentity(replaceRoute: true);
    // navigation logic here
  }

  /// Handles the 'accountCreation' step.
  /// Returns false as it leads to a different screen.
  static void _handleAccountCreation({
    required OnboardingNavigation onboardingNavigation,
  }) {
    print("Navigating to Account Creation Step");
    onboardingNavigation.navigateToCreateAccountIntro(replaceRoute: true);
    // navigation logic here
  }

  /// Handles the 'addFunds' step.
  /// Returns false as it leads to a different screen.
  static void _handleAddFunds({
    required OnboardingNavigation onboardingNavigation,
  }) {
    print("Navigating to Add Funds Step");
    onboardingNavigation.navigateToDepositIntro(replace: true);
  }

  /// Handles the 'placeTrade' step.
  /// Returns false as it leads to a different screen.
  static void _handlePlaceTrade({
    required OnboardingNavigation onboardingNavigation,
    required AnalyticsService analyticsService,
  }) {
    print("Navigating to Place Trade Step");
    analyticsService.removeGlobalAttributes([
      AnalyticsGlobalAttributes.phone,
      AnalyticsGlobalAttributes.email,
    ]);
    onboardingNavigation.navigateToHub(replace: true);
  }

  static void handlePhoneNumberFlow(OnboardingNavigation onboardingNavigator) {
    print("Navigating to Phone Number Flow");
    onboardingNavigator.navigateToMobileNumberInput();
    // navigation logic here
  }

  static void handleRegistrationFlow(
    OnboardingNavigation onboardingNavigator,
    AuthenticationChannel channel,
    IdDetails? idDetails,
  ) {
    switch (channel) {
      case AuthenticationChannel.okta:
        onboardingNavigator.navigateToUserRegistration();
        break;
      case AuthenticationChannel.uaePass:
        onboardingNavigator.navigateToUserRegistrationUaePass(
          idDetails: idDetails,
        );
        break;
    }
  }
}
