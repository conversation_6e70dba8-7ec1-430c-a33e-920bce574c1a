# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
migrate_working_dir/
.cursorrules

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

#If you are building an application, you want to check-in your pubspec.lock
pubspec.lock
!app/equiti_platform/pubspec.lock

#Golden tests
**/failures/*.png

pubspec_overrides.yaml

mason-lock.json
bricks.json

# FVM Version Cache
.fvm/
.vscode/settings.json
.vscode/argv.json
.fvmrc

# Code coverage
**/coverage

api_key.p8
report.xml
**/fastlane/report.xml
equiti_platform_service_account_key.json
features/payments/payment_events.pdf
Production_Key_Store_File
key.properties
