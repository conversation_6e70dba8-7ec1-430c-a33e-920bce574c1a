import 'package:api_client/api_client.dart';
import 'package:flutter/material.dart';
import 'package:host/host.dart';
import 'package:payment/payments.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:trader/di/di_initializer.dart';

DisplayableComponent depositAmount() {
  return DisplayableComponent(
    title: 'Deposit Amount',
    onTap: () {
      diContainer<MockApiInterceptor>()
        ..reset()
        ..reply({
          '/api/v1/conversionRate': [
            MockResponse(
              bodyFilePath: 'resources/mocks/conversion_rate/success.json',
            ),
          ],
        });
      return Scaffold(
        appBar: AppBar(title: const Text('Deposit Amount')),
        body: DepositWithdrawAmountConversionWidget(
          args: (
            accountCurrency: 'USD',
            account: null,
            currencyMinMaxSuggestedAmountList: [
              CurrencyAmountDetail(
                currency: 'AED',
                suggestedAmounts: [500, 1000, 1500],
                minAmount: 1,
                maxAmount: 1000,
              ),
              CurrencyAmountDetail(
                currency: 'USD',
                suggestedAmounts: [50, 100, 150],
                minAmount: 1,
                maxAmount: 2000,
              ),
            ],
            currencies: ["Usd", "AED", "KWD"],
            showSuggestedAmounts: true,
            isStartWithConversionRate: false,
            selectedCurrency: null,
            externalErrorMessage: null,
            paymentType: PaymentType.deposit,
            premierAccountMinAmountForDeposit: null,
            isInputDisabled: false,
          ),
          callback: ({
            required String accountCurrencyAmount,
            required String selectedCurrencyAmount,
            String? selectedCurrency,
            required bool isAmountValid,
            required RatesModel? conversionRateSelectedToAccountCurrency,
            String? conversionRateString,
            required ConversionRateModel? conversionRateData,
          }) {
            print('Amount: $accountCurrencyAmount, Is Valid: $isAmountValid');
            print('Conversion Rate String: ${conversionRateString ?? 'N/A'}');
            print('Target Currency: ${selectedCurrency ?? 'N/A'}');
          },
        ),
      );
    },
  );
}
