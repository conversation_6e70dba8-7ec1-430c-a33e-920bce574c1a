import 'package:api_client/api_client.dart';
import 'package:e_trader/fusion.dart';
import 'package:host/host.dart';
import 'package:socket_client/socket_client.dart';
import 'package:trader/di/di_initializer.dart';

DisplayableComponent productDetails() {
  return DisplayableComponent(
    title: 'Product Details',
    children: [
      DisplayableComponent(
        title: 'Success Default',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              'api/Account/linkedSymbols': [
                MockResponse(
                  code: 200,
                  bodyFilePath:
                      'resources/mocks/product_detail_info/success_default.json',
                ),
              ],
              'api/Symbol/get-holiday': [
                MockResponse(
                  code: 200,
                  bodyFilePath:
                      'resources/mocks/sessions/comprehensive_testing.json',
                ),
              ],
              'api/Symbol/get-session': [
                MockResponse(
                  code: 200,
                  bodyFilePath:
                      'resources/mocks/sessions/comprehensive_testing.json',
                ),
              ],
            });
          diContainer<MockSocketInterceptor>().addMockResponse(
            url: "https://equiti-backend-demo-dev.equiti.me.uk/hubs/productHub",
            eventType: TradingSocketEvent.quotes.subscribe,
            responses: [
              {
                "symbol": "AUDCAD",
                "productName": "AUDCAD",
                "ask": 0.90793,
                "bid": 0.90768,
                "date": "2024-11-22T05:15:03",
                "digits": 5,
                "dailyRateChange": 0,
                "direction": "Up",
              },
            ],
          );

          return ProductDetailsWidget(
            symbolDetail: SymbolDetailViewModel(
              symbolName: 'AUDCAD',
              minLot: 0.1,
              lotsSteps: 1,
              maxLot: 5.0,
              digit: 5,
              platformName: 'AUDCAD',
            ),
            accountNumber: '********',
          );
        },
      ),
      DisplayableComponent(
        title: 'Success - Missing Items',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              'Account/linkedSymbols': [
                MockResponse(
                  code: 200,
                  bodyFilePath:
                      'resources/mocks/product_detail_info/success_missing_items.json',
                ),
              ],
            });

          return ProductDetailsWidget(
            symbolDetail: SymbolDetailViewModel(
              symbolName: 'AUDCAD',
              minLot: 0.1,
              lotsSteps: 1,
              maxLot: 5.0,
              digit: 5,
              platformName: 'AUDCAD',
            ),
            accountNumber: '********',
          );
        },
      ),
      DisplayableComponent(
        title: 'Success - Large Descriptiom',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              'Account/linkedSymbols': [
                MockResponse(
                  code: 200,
                  bodyFilePath:
                      'resources/mocks/product_detail_info/success_large_description.json',
                ),
              ],
            });

          return ProductDetailsWidget(
            symbolDetail: SymbolDetailViewModel(
              symbolName: 'AUDCAD',
              minLot: 0.1,
              maxLot: 5.0,
              lotsSteps: 1,
              digit: 5,
              platformName: 'AUDCAD',
            ),
            accountNumber: '********',
          );
        },
      ),
    ],
  );
}
