import 'package:api_client/api_client.dart';
import 'package:flutter/material.dart';

import 'package:host/host.dart';
import 'package:payment/payments.dart';
import 'package:trader/di/di_initializer.dart';

DisplayableComponent selectPaymentAccountList() {
  return DisplayableComponent(
    title: 'Select Payment Account List',
    children: [
      DisplayableComponent(
        title: 'Success',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/api/client/accounts': [
                MockResponse(
                  bodyFilePath:
                      'resources/mocks/payment_list_of_accounts/success.json',
                ),
              ],
            });
          return Scaffold(
            appBar: AppBar(),
            body: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: AccountListWidget(
                  args: (
                    selectByHighestBalance: false,
                    excludeAccountNumber: null,
                    onEmptyStateChanged: null,
                    isInputDisabled: false,
                  ),
                  onAccountSelected: (selectedAccount) {
                    print("Account Selected");
                  },
                ),
              ),
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'Failure',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/api/client/accounts': [
                MockResponse(
                  bodyFilePath:
                      'resources/mocks/payment_list_of_accounts/failure.json',
                ),
              ],
            });
          return Scaffold(
            appBar: AppBar(),
            body: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: AccountListWidget(
                  args: (
                    selectByHighestBalance: false,
                    excludeAccountNumber: null,
                    onEmptyStateChanged: null,
                    isInputDisabled: false,
                  ),
                  onAccountSelected: (selectedAccount) {
                    print("Account Selected");
                  },
                ),
              ),
            ),
          );
        },
      ),
    ],
  );
}
