import 'package:e_trader/fusion.dart';
import 'package:host/host.dart';
import 'package:trader/presentation/position_options/position_option_view.dart';

DisplayableComponent potitionOptions() {
  return DisplayableComponent(
    title: 'Potition Options',
    children: [
      DisplayableComponent(
        title: 'Success with Position Options',
        onTap: () {
          return PositionOptionView(
            model: SymbolDetailViewModel(
              symbolName: "AUDCAD",
              assetType: "Minor",
              imageURL: '',
              minLot: 0.1,
              lotsSteps: 1,
              maxLot: 5.0,
              digit: 5,
              platformName: 'AUDCAD',
            ),
          );
        },
      ),
    ],
  );
}
