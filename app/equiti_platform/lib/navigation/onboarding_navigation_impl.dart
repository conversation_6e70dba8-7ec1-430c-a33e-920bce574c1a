import 'dart:developer';
import 'package:broker_settings/broker_settings.dart';
import 'package:domain/domain.dart';
import 'package:e_trader/fusion.dart' show EquitiTraderRouteSchema;
import 'package:equiti_platform/di/di_container.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:hub/hub.dart';
import 'package:login/login.dart';
import 'package:onboarding/onboarding.dart';
import 'package:payment/payments.dart';

class OnboardingNavigationImpl extends OnboardingNavigation {
  OnboardingNavigationImpl();

  @override
  void goToLogin() {
    diContainer<EquitiNavigatorBase>().push(LoginRouteSchema.loginroute.label);
  }

  @override
  void goToSignupOptions({required SignupOptionsArgs args}) {
    diContainer<EquitiNavigatorBase>().pushReplacement(
      OnboardingRouteSchema.signupOptionsRoute.label,
      arguments: args,
    );
  }

  @override
  void goToSignup({String? email}) {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.signup.label,
      data: {"email": email},
    );
  }

  @override
  void goToLoginOptions({bool replace = false}) {
    if (replace) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        OnboardingRouteSchema.loginOptionsRoute.label,
      );
    } else {
      diContainer<EquitiNavigatorBase>().push(
        OnboardingRouteSchema.loginOptionsRoute.label,
      );
    }
  }

  @override
  void goToPersonalDetailsIntroPage() {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.personalDetailsIntroRoute.label,
    );
  }

  @override
  void navigateToVerifyMobile({required MobileOtpVerificationArgs args}) {
    log('args for navigating to verify mobile: $args');
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.verifyMobileRoute.label,
      data: args.toJson(),
    );
  }

  @override
  void navigateToMobileNumberInput() {
    diContainer<EquitiNavigatorBase>().pushReplacement(
      OnboardingRouteSchema.mobileNumberInputRoute.label,
    );
  }

  @override
  void navigateToOtpInput({
    required MobileOtpVerificationArgs args,
    required SendOtpResponseData sendOtpModel,
  }) {
    log('args for navigating to otp input: $args $sendOtpModel');
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.otpInputRoute.label,
      data: {"args": args, "sendOtpModel": sendOtpModel},
    );
  }

  @override
  void removeUntilMobileNumberInput() {
    diContainer<EquitiNavigatorBase>().pop();
    diContainer<EquitiNavigatorBase>().pop();
  }

  @override
  void navigateToPhoneNumberVerified({bool replace = false}) {
    if (replace) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        OnboardingRouteSchema.phoneNumberVerifiedRoute.label,
      );
    } else {
      diContainer<EquitiNavigatorBase>().push(
        OnboardingRouteSchema.phoneNumberVerifiedRoute.label,
      );
    }
  }

  @override
  void navigateToCountrySelector() {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.countrySelectorRoute.label,
    );
  }

  @override
  void navigateToProgressTracker() {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.progressTrackerPage.label,
    );
  }

  @override
  void setAndNavigateToProgressTracker() {
    diContainer<EquitiNavigatorBase>().set([
      OnboardingRouteSchema.progressTrackerPage.label,
    ]);
  }

  @override
  void navigateToVerifyIdentity({bool replaceRoute = false}) {
    if (replaceRoute) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        OnboardingRouteSchema.verifyIdentityRoute.label,
      );
    } else {
      diContainer<EquitiNavigatorBase>().push(
        OnboardingRouteSchema.verifyIdentityRoute.label,
      );
    }
  }

  @override
  void navigateToMorphFormBuilder({ProgressTrackerData? progressTracker}) {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.morphFormBuilder.label,
      arguments: progressTracker,
    );
  }

  @override
  void setAndNavigateToMorphFormBuilder() {
    diContainer<EquitiNavigatorBase>().set([
      OnboardingRouteSchema.morphFormBuilder.label,
    ]);
  }

  @override
  void navigateToCreateAccountMain({
    required CreateAccountFlow createAccountFlow,
  }) {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.createAccountMainRoute.label,
      arguments: CreateAccountMainArgs(createAccountFlow: createAccountFlow),
    );
  }

  @override
  void navigateToAccountSuccessful({
    required AccountCreationResponseData data,
    required CreateAccountFlow createAccountFlow,
    bool replace = false,
    required AccountCreationPlatform platform,
  }) {
    if (replace) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        OnboardingRouteSchema.accountSuccessfulRoute.label,
        arguments: AccountSuccessfulArgs(
          data: data,
          createAccountFlow: createAccountFlow,
          platform: platform,
        ),
      );
    } else {
      diContainer<EquitiNavigatorBase>().push(
        OnboardingRouteSchema.accountSuccessfulRoute.label,
        arguments: AccountSuccessfulArgs(
          data: data,
          createAccountFlow: createAccountFlow,
          platform: platform,
        ),
      );
    }
  }

  @override
  void navigateToCreateAccountIntro({bool replaceRoute = false}) {
    if (replaceRoute) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        OnboardingRouteSchema.createAccountIntroRoute.label,
      );
    } else {
      diContainer<EquitiNavigatorBase>().push(
        OnboardingRouteSchema.createAccountIntroRoute.label,
      );
    }
  }

  @override
  void navigateToDepositIntro({bool replace = false}) {
    if (replace) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        OnboardingRouteSchema.depositIntroRoute.label,
      );
    } else {
      diContainer<EquitiNavigatorBase>().pushAndRemoveUntil(
        OnboardingRouteSchema.depositIntroRoute.label,
        OnboardingRouteSchema.depositIntroRoute.label,
      );
    }
  }

  @override
  void navigateToDepositPaymentOptions({
    required DepositFlowConfig depositFlowConfig,
  }) {
    diContainer<EquitiNavigatorBase>().pushReplacement(
      PaymentRouteSchema.depositOptionsRoute.label,
      arguments: depositFlowConfig,
    );
  }

  @override
  void navigateToHub({bool replace = false}) {
    if (replace) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        HubRouteSchema.hubRoute.label,
      );
    } else {
      diContainer<EquitiNavigatorBase>().push(HubRouteSchema.hubRoute.label);
    }
  }

  @override
  void goToCitySelection() {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.citySelectorRoute.label,
    );
  }

  @override
  void navigateToUserRegistration() {
    diContainer<EquitiNavigatorBase>().set([
      OnboardingRouteSchema.userRegistrationRoute.label,
    ]);
  }

  @override
  void navigateToSwitchAccounts({bool replace = false}) {
    if (replace) {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        EquitiTraderRouteSchema.switchAccountRoute.label,
      );
    } else {
      diContainer<EquitiNavigatorBase>().push(
        EquitiTraderRouteSchema.switchAccountRoute.label,
      );
    }
  }

  @override
  void navigateBackToAccountsScreen() {
    diContainer<EquitiNavigatorBase>().globalData = {
      EquitiTraderRouteSchema.switchAccountRoute.url: true,
    };
    diContainer<EquitiNavigatorBase>().popUntil(
      EquitiTraderRouteSchema.switchAccountRoute.label,
    );
  }

  @override
  void logout() {
    diContainer<OnboardingNavigation>().goToLoginOptions(replace: true);
  }

  @override
  void navigateToUserRegistrationUaePass({IdDetails? idDetails}) {
    diContainer<EquitiNavigatorBase>().pushAndRemoveUntil(
      OnboardingRouteSchema.userRegistrationUaePassRoute.label,
      '',
      arguments: UserRegistrationUaePassArgs(idDetails: idDetails),
    );
  }

  @override
  void popUnitRoute({required String popUntil}) {
    diContainer<EquitiNavigatorBase>().popUntil(popUntil);
  }
}
