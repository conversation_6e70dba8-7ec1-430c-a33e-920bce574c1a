import 'dart:developer';

import 'package:domain/domain.dart';
import 'package:e_trader/fusion.dart' hide MobileOtpVerificationArgs;
import 'package:e_trader/src/navigation/switch_account_args.dart';
import 'package:equiti_platform/di/di_container.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:hub/hub.dart';
import 'package:login/login.dart';
import 'package:onboarding/onboarding.dart';
import 'package:payment/payments.dart';
import 'package:user_account/user_account.dart';

class HubNavigationImpl implements HubNavigation {
  const HubNavigationImpl();

  @override
  void gotoBrokerage({AccountType accountType = AccountType.trading}) {
    diContainer<EquitiNavigatorBase>().push(
      EquitiTraderRouteSchema.switchAccountRoute.label,
      arguments: SwitchAccountArgs(accountType: accountType),
    );
  }

  @override
  void gotoGold() {
    diContainer<EquitiNavigatorBase>().push(
      HubRouteSchema.newProductInfoRoute.label,
      arguments: {"identifier": "promotion_gold"},
    );
  }

  @override
  void gotoWealth() {
    diContainer<EquitiNavigatorBase>().push(
      HubRouteSchema.newProductInfoRoute.label,
      arguments: {"identifier": "promotion_wealth"},
    );
  }

  @override
  void gotoSettings() {
    diContainer<EquitiNavigatorBase>().push(
      HubRouteSchema.hubSettingsRoute.label,
    );
  }

  @override
  void goToHub() {
    diContainer<EquitiNavigatorBase>().pushReplacement(
      HubRouteSchema.hubRoute.label,
    );
  }

  @override
  void gotoProfile({
    required ClientProfileData clientProfileData,
    ProfileTabEnum? initialIndex,
    void Function()? thenCallback,
  }) {
    diContainer<EquitiNavigatorBase>()
        .push(
          HubRouteSchema.profileRoute.label,
          arguments: ProfilePageArgs(
            clientProfileData: clientProfileData,
            initialIndex: initialIndex,
          ),
        )
        .then((_) {
          thenCallback?.call();
        });
  }

  @override
  void gotoHistoricalPerformance() {
    diContainer<EquitiNavigatorBase>().push(
      EquitiTraderRouteSchema.historicalPerformanceRoute.label,
    );
  }

  @override
  void logout() {
    diContainer<EquitiTraderNavigation>().navigateToLoginOptions();
  }

  @override
  void goToDepositPaymentOptions(DepositFlowConfig depositFlowConfig) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.depositOptionsRoute.label,
      arguments: depositFlowConfig,
    );
  }

  @override
  void goToWithdrawPaymentOptions(String popUntilRoute) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.withdrawOptionsRoute.label,
      arguments: popUntilRoute,
    );
  }

  @override
  void goToTransferFundsScreen(String originRoute) {
    diContainer<EquitiNavigatorBase>().push(
      PaymentRouteSchema.transferFundsScreen.label,
      arguments: originRoute,
    );
  }

  @override
  void goToTrading() {
    final account = diContainer<GetSelectedAccountUseCase>().call();
    if (account == null) {
      diContainer<EquitiNavigatorBase>().push(
        EquitiTraderRouteSchema.switchAccountRoute.label,
      );
    } else {
      diContainer<EquitiNavigatorBase>().pushReplacement(
        EquitiTraderRouteSchema.navBarRoute.label,
      );
    }
  }

  @override
  void goToChangeCredentials({required CredentialType type}) {
    diContainer<EquitiNavigatorBase>().push(
      HubRouteSchema.changeCredentialsRoute.label,
      arguments: ChangeCredentialsArgs(type: type),
    );
  }

  @override
  void goToUpdatePhoneFlow({required UpdatePhoneArgs args}) {
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.mobileNumberInputRoute.label,
      arguments: args,
    );
  }

  @override
  void goToVerifyPhonePageIntro({required UpdatePhoneArgs args}) {
    final mobileOtpVerificationArgs = MobileOtpVerificationArgs(
      phoneNumber: args.number!,
      countryCode: args.countryCode!,
      origin: args.origin,
      isUpdatingPhoneNumber: true,
    );
    diContainer<EquitiNavigatorBase>().push(
      OnboardingRouteSchema.verifyMobileRoute.label,
      data: mobileOtpVerificationArgs.toJson(),
    );
  }
}
