import 'dart:async';

import 'package:duplo/duplo.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:equiti_platform/config/app_config.dart';
import 'package:equiti_platform/config/equiti_env.dart';
import 'package:equiti_platform/network_connectivity/network_connectivity_manager.dart';
import 'package:network_logging/network_logging.dart';
import 'package:equiti_platform/deep_links/deep_link_handler.dart';
import 'package:equiti_platform/di/di_container.dart';
import 'package:equiti_platform/di/di_initializer.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:feature_flags/feature_flags.dart';
import 'package:firebase/firebase_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get_it/get_it.dart';
import 'package:locale_manager/locale_manager.dart';
import 'package:monitoring/monitoring.dart';
import 'package:onboarding/onboarding.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:custom_action_keyboard/custom_action_keyboard.dart';
import 'package:equiti_identity/equiti_identity.dart';
import 'dart:io' show Platform;

Future<void> runMainApp(GetIt diContainer, AppConfig config) async {
  final widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  // Initialize Firebase first to ensure RemoteConfiguration can initialize
  await FirebaseManagerBase.initialize(config.firebaseEnv);

  await configureDependencies(diContainer, env: config.env.name);
  await EquitiLocalizationManager.init(
    // Send false for prod, true for other environments
    preRelease: config.env != EquitiEnv.prod,
  );

  await diContainer<FeatureFlagService>().activate();
  // Trigger a background refresh of remote flags (non-blocking)
  unawaited(diContainer<FeatureFlagService>().fetch());

  EquitiLocalizationManager.update();

  // Initialize identity manager
  final idManager = diContainer<IdManager>();
  try {
    if (idManager is EquitiIdManager) {
      await idManager.warmUp();
      await idManager.startSession();
      print(
        '${AnalyticsGlobalAttributes.equitiDeviceId.name} ${idManager.getDeviceId()}',
      );
      print(
        '${AnalyticsGlobalAttributes.equitiSessionId.name}: ${idManager.currentSessionId ?? 'null'}',
      );
    }
  } catch (e) {
    diContainer<LoggerBase>().logError(
      'Failed to warm up identity manager: $e',
    );
  }

  // Initialize analytics service
  try {
    final analyticsService = diContainer<AnalyticsService>();
    final analyticsConfig = diContainer<AnalyticsConfig>();
    final success = await analyticsService.initialize(analyticsConfig);
    if (success) {
      print('Analytics service initialized successfully');
      // ignore: avoid-missing-enum-constant-in-map
      await analyticsService.setGlobalAttributes({
        AnalyticsGlobalAttributes.equitiDeviceId: idManager.getDeviceId(),
        AnalyticsGlobalAttributes.equitiSessionId:
            idManager.currentSessionId ?? 'null',
      });
    } else {
      print('Failed to initialize analytics service');
    }
  } catch (e) {
    print('Failed to initialize analytics: $e');
    // Don't let analytics initialization failure prevent app startup
  }

  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]).then((_) {
    diContainer<ReporterBase>().start(
      app: EquitiApp(config: config),
      extraData: {"sentry_dsn_url": config.sentryDSNUrl},
    );
  });
}

class EquitiApp extends StatefulWidget {
  const EquitiApp({super.key, required this.config});

  final AppConfig config;

  @override
  State<EquitiApp> createState() => _EquitiAppState();
}

class _EquitiAppState extends State<EquitiApp> {
  late String nextScreenToShow;

  late final EquitiNavigatorBase _navigator;
  late final RouterDelegate<Object> _routerDelegate;
  late final RouteInformationParser<Object> _routeInformationParser;

  @override
  void initState() {
    super.initState();
    _navigator = diContainer<EquitiNavigatorBase>();
    _routerDelegate = _navigator.routerDelegate;
    _routeInformationParser = _navigator.routeInformationParser;

    // Initialize deep link handler
    _initializeDeepLinks();

    // Initialize network connectivity manager
    _initializeNetworkConnectivity();

    screenDecider();
  }

  @override
  void dispose() {
    DeepLinkHandler.dispose();
    super.dispose();
  }

  /// Initialize deep link handling
  Future<void> _initializeDeepLinks() async {
    try {
      await DeepLinkHandler.initialize();
    } catch (e) {
      debugPrint('Failed to initialize deep links: $e');
    }
  }

  /// Initialize network connectivity manager
  void _initializeNetworkConnectivity() {
    try {
      // The NetworkConnectivityManager is already initialized through DI
      // and the interceptor is already added to the Dio instance
      diContainer<NetworkConnectivityManager>();
    } catch (e) {
      // Silently handle initialization errors
    }
  }

  void _init() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _navigator.pushReplacement(nextScreenToShow);
      FlutterNativeSplash.remove();
      _addDebugFABOverlay();
      _updateInitialNavigationBarColor();
    });
  }

  void _updateInitialNavigationBarColor() {
    if (!Platform.isAndroid) return;

    final themeManager = diContainer<ThemeManager>();
    final isDarkMode = themeManager.isDarkMode;
    final duploThemeData =
        isDarkMode ? DuploThemeData.dark() : DuploThemeData.light();

    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness:
            isDarkMode ? Brightness.light : Brightness.dark,
        systemNavigationBarColor: duploThemeData.background.bgPrimary,
        systemNavigationBarIconBrightness:
            isDarkMode ? Brightness.light : Brightness.dark,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: diContainer<ThemeManager>(),
      builder: (themeContext, _) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (Platform.isAndroid) {
            _updateInitialNavigationBarColor();
          }
        });

        return ListenableBuilder(
          listenable: diContainer<LocaleManager>(),
          builder: (localeContext, _) {
            return MaterialApp.router(
              locale: diContainer<LocaleManager>().locale,
              routerDelegate: _routerDelegate,
              routeInformationParser: _routeInformationParser,
              theme: ThemeData(
                useMaterial3: true,
                actionIconTheme: ActionIconThemeData(
                  backButtonIconBuilder: (backButtonBuilderContext) {
                    final theme = backButtonBuilderContext.duploTheme;
                    return Assets.images
                        .arrowLeftDirectional(backButtonBuilderContext)
                        .svg(
                          colorFilter: ColorFilter.mode(
                            theme.foreground.fgPrimary,
                            BlendMode.srcIn,
                          ),
                        );
                  },
                ),
              ),
              supportedLocales: EquitiLocalization.supportedLocales,
              localizationsDelegates: EquitiLocalization.localizationsDelegates,
              builder: (builderContext, child) {
                final duploThemeData =
                    diContainer<ThemeManager>().isDarkMode
                        ? DuploThemeData.dark()
                        : DuploThemeData.light();

                final locale = Localizations.localeOf(builderContext);

                return MediaQuery(
                  data: MediaQuery.of(
                    builderContext,
                  ).copyWith(textScaler: const TextScaler.linear(1.0)),
                  child: DuploTheme(
                    data: duploThemeData,
                    child: DuploTextStyles(
                      locale: locale,

                      ///This widget observes keyboard visibility using a stream (keyboardVisibilityStream)
                      ///and updates the UI accordingly. It adjusts padding and displays a
                      /// Done button at the bottom of the screen when the keyboard is visible.
                      child: StreamBuilder(
                        stream:
                            diContainer<CustomActionKeyboard>()
                                .keyboardVisibilityStream,
                        builder: (ctx, value) {
                          // Get the current visibility status, default to false if null
                          final isVisible = value.data ?? false;
                          final theme = DuploTheme.of(ctx);
                          final localization = EquitiLocalization.of(ctx);
                          return Stack(
                            alignment: Alignment.bottomCenter,
                            children: [
                              // Shifts the main child upward when keyboard is visible
                              AnimatedPadding(
                                padding: EdgeInsets.only(
                                  bottom: isVisible ? 50 : 0,
                                ),
                                duration: Duration(milliseconds: 500),
                                child: child!,
                              ),
                              // Custom Done button shown at bottom
                              CustomDoneButtonStripWidget(
                                isKeyboardVisible: isVisible,
                                textColor: theme.text.textPrimary,
                                backgroundColor:
                                    diContainer<ThemeManager>().isDarkMode
                                        ? Color(0xFF3c3c3c)
                                        : Color(0xffD1D1D6),
                                text: localization.duplo_done_button,
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  /// Adds debug FAB overlay using Navigator's overlay
  void _addDebugFABOverlay() {
    final navigatorKey = diContainer<GlobalKey<NavigatorState>>();
    final overlay = navigatorKey.currentState?.overlay;
    if (overlay != null) {
      // Get the context from the navigator which has MaterialLocalizations
      final navigatorContext = navigatorKey.currentContext;
      if (navigatorContext != null) {
        NetworkLogManager networkLogManager = diContainer<NetworkLogManager>();
        final logTrackerWidget = networkLogManager.logTrackerWidget(
          navigatorContext,
        );
        if (logTrackerWidget != null) {
          final overlayEntry = OverlayEntry(
            builder: (context) => logTrackerWidget,
          );
          overlay.insert(overlayEntry);
        }
      }
    }
  }

  void screenDecider() async {
    diContainer<ThemeManager>().loadThemeWithSystemFallback(context);
    final TokenManager manager = diContainer<TokenManager>();
    final tokenState = await manager.getState();
    if (tokenState == TokenManagerState.authenticated) {
      nextScreenToShow = OnboardingRouteSchema.progressTrackerPage.label;
    } else if (tokenState == TokenManagerState.loggedOutUser) {
      nextScreenToShow = OnboardingRouteSchema.welcomeRoute.label;
    } else if (tokenState == TokenManagerState.noUser) {
      nextScreenToShow = OnboardingRouteSchema.welcomeRoute.label;
    }
    diContainer<ThemeManager>().loadThemeWithSystemFallback(context);
    _init();
  }
}
