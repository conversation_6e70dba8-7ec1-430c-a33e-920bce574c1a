import 'package:api_client/api_client.dart';
import 'package:dio/dio.dart';
import 'package:equiti_platform/di/di_container.dart';
import 'package:flutter/foundation.dart';

/// Interceptor to handle network connectivity issues and queue failed requests
class NetworkConnectivityInterceptor extends Interceptor {
  NetworkConnectivityInterceptor({
    required this.onNetworkError,
    required this.onNetworkRestored,
    this.dioInstanceName,
  });

  final VoidCallback onNetworkError;
  final VoidCallback onNetworkRestored;

  final List<_QueuedRequest> _queuedRequests = [];
  bool _isNetworkNotAvaliable = true;
  bool _isProcessingQueue = false;
  final String? dioInstanceName;

  /// Notifier for processing queue state changes
  final ValueNotifier<bool> _processingQueueNotifier = ValueNotifier<bool>(
    false,
  );

  /// Get current processing state
  bool get isProcessingQueue => _isProcessingQueue;

  /// Get the processing queue notifier for reactive updates
  ValueListenable<bool> get processingQueueNotifier => _processingQueueNotifier;

  void _setProcessingQueue(bool value) {
    _isProcessingQueue = value;
    _processingQueueNotifier.value = value;
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (_isConnectionError(err)) {
      if (_isNetworkNotAvaliable) {
        _isNetworkNotAvaliable = false;
        onNetworkError();
      }
      if (_isProcessingQueue) {
        handler.next(err);
      } else {
        _queuedRequests.add(_QueuedRequest(err.requestOptions, handler));
      }
    } else {
      handler.next(err);
    }
  }

  @override
  void onResponse(
    Response<Object?> response,
    ResponseInterceptorHandler handler,
  ) {
    if (!_isNetworkNotAvaliable) {
      _isNetworkNotAvaliable = true;
      onNetworkRestored();
    }

    handler.next(response);
  }

  /// Check if the error is connection-related
  bool _isConnectionError(DioException error) {
    if (error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.connectionError ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.sendTimeout) {
      return true;
    }
    final isConnectionTimeout =
        error.type == DioExceptionType.connectionTimeout;
    final isConnectionError = error.type == DioExceptionType.connectionError;
    final isReceiveTimeout = error.type == DioExceptionType.receiveTimeout;
    final isSendTimeout = error.type == DioExceptionType.sendTimeout;

    // For unknown errors, check various indicators
    final errorString = error.error?.toString().toLowerCase() ?? '';
    final messageString = error.message?.toLowerCase() ?? '';

    final isUnknownNetworkError =
        error.type == DioExceptionType.unknown &&
        (messageString.contains('network') ||
            messageString.contains('connection') ||
            messageString.contains('timeout') ||
            messageString.contains('unreachable') ||
            errorString.contains('network') ||
            errorString.contains('connection') ||
            errorString.contains('timeout') ||
            errorString.contains('unreachable') ||
            errorString.contains('failed host lookup') ||
            errorString.contains('no address associated') ||
            errorString.contains('socketexception') ||
            errorString.contains('handshakeexception') ||
            error.message ==
                null // Often network errors have null messages
                );

    return isConnectionTimeout ||
        isConnectionError ||
        isReceiveTimeout ||
        isSendTimeout ||
        isUnknownNetworkError;
  }

  /// Retry all queued requests
  Future<bool> retryQueuedRequests() async {
    if (_isProcessingQueue || _queuedRequests.isEmpty) {
      return _queuedRequests.isEmpty;
    }

    _setProcessingQueue(true);
    try {
      for (final queuedRequest in _queuedRequests) {
        final response = await _retry(queuedRequest.requestOptions);
        queuedRequest.handler.resolve(response);
      }

      /// If all of the requests pass, we will clear the queue.
      _queuedRequests.clear();
      _setProcessingQueue(false);
    } catch (e) {
      _setProcessingQueue(false);
    }
    return true;
  }

  Future<Response<Object>> _retry(RequestOptions requestOptions) async {
    // Update the authorization header with the new token
    final options = Options(
      method: requestOptions.method,
      headers: requestOptions.headers,
      contentType: requestOptions.contentType,
      responseType: requestOptions.responseType,
      followRedirects: requestOptions.followRedirects,
      validateStatus: requestOptions.validateStatus,
      receiveDataWhenStatusError: requestOptions.receiveDataWhenStatusError,
      extra: requestOptions.extra,
    );

    return await diContainer<ApiClientBase>(
      instanceName: dioInstanceName,
    ).request(
      requestOptions.path,
      data: requestOptions.data,
      queryParameters: requestOptions.queryParameters,
      options: options,
    );
  }
}

/// Internal class to hold queued request information
class _QueuedRequest {
  const _QueuedRequest(this.requestOptions, this.handler);

  final RequestOptions requestOptions;
  final ErrorInterceptorHandler handler;
}
