import 'package:duplo/duplo.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:equiti_platform/src/assets/assets.gen.dart' as PlatformAssets;

/// Dialog that shows when network connectivity is lost
class NetworkConnectivityDialog extends StatelessWidget {
  const NetworkConnectivityDialog({
    super.key,
    required this.onRetry,
    required this.processingQueueNotifier,
  });

  final VoidCallback onRetry;
  final ValueListenable<bool> processingQueueNotifier;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;

    return Material(
      color: theme.background.bgPrimary,
      child: PopScope(
        canPop: false, // Prevent dismissing the dialog
        child: AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle(
            statusBarColor: theme.background.bgPrimary,
            statusBarIconBrightness:
                theme.text.textPrimary.computeLuminance() > 0.5
                    ? Brightness.dark
                    : Brightness.light,
            systemNavigationBarColor: theme.background.bgPrimary,
            systemNavigationBarIconBrightness:
                theme.text.textPrimary.computeLuminance() > 0.5
                    ? Brightness.dark
                    : Brightness.light,
          ),
          child: Container(
            width: double.infinity,
            height: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              children: [
                // Top spacing for status bar
                SizedBox(height: MediaQuery.paddingOf(context).top),

                // Expanded content area - centers the icon/title/description
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Offline Icon - using SVG asset
                      PlatformAssets.Assets.svg.offlineImage.svg(
                        width: 80,
                        height: 80,
                      ),
                      // Title
                      DuploText(
                        text: "It looks like you're offline.",
                        style: textStyles.textXl,
                        color: theme.text.textPrimary,
                        fontWeight: DuploFontWeight.semiBold,
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 12),

                      // Description
                      DuploText(
                        text: "Check your connection and try again",
                        style: textStyles.textMd,
                        color: theme.text.textSecondary,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                // Bottom button area
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.only(
                    bottom: MediaQuery.paddingOf(context).bottom + 24,
                  ),
                  child: ValueListenableBuilder<bool>(
                    valueListenable: processingQueueNotifier,
                    builder: (listnerContext, isRetrying, child) {
                      return DuploButton.secondary(
                        title: "Retry",
                        isLoading: isRetrying,
                        onTap: () {
                          onRetry();
                        },
                        useFullWidth: true,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Show the network connectivity dialog
  static void show({
    required BuildContext context,
    required VoidCallback onRetry,
    required ValueListenable<bool> processingQueueNotifier,
  }) {
    try {
      showDialog<void>(
        context: context,
        barrierDismissible: false,
        useSafeArea: false,
        builder: (dialogContext) {
          return NetworkConnectivityDialog(
            onRetry: onRetry,
            processingQueueNotifier: processingQueueNotifier,
          );
        },
      );
    } catch (e) {
      debugPrint('🔴 Error in showDialog: $e');
    }
  }

  /// Dismiss the dialog if it's currently shown
  static void dismiss(BuildContext context) {
    try {
      Navigator.of(context, rootNavigator: true).pop();
    } catch (e) {
      debugPrint('🔄 Error in Navigator.pop(): $e');
    }
  }
}
