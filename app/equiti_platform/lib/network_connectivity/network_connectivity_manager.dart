import 'package:flutter/material.dart';
import 'package:equiti_platform/network_connectivity/network_connectivity_dialog.dart';
import 'package:equiti_platform/network_connectivity/network_connectivity_interceptor.dart';

/// Manager that coordinates network connectivity dialog display and request retry logic
class NetworkConnectivityManager {
  NetworkConnectivityManager({
    required this.navigatorKey,
    String? dioInstanceName,
  }) {
    _interceptor = NetworkConnectivityInterceptor(
      onNetworkError: _showNetworkDialog,
      onNetworkRestored: _hideNetworkDialog,
      dioInstanceName: dioInstanceName,
    );
  }

  final GlobalKey<NavigatorState> navigatorKey;

  late final NetworkConnectivityInterceptor _interceptor;
  bool _isDialogShown = false;

  /// Get the interceptor to add to Dio
  NetworkConnectivityInterceptor get interceptor => _interceptor;

  /// Show the network connectivity dialog
  void _showNetworkDialog() {
    final context = navigatorKey.currentContext;
    if (context == null || _isDialogShown) {
      return;
    }

    _isDialogShown = true;

    NetworkConnectivityDialog.show(
      context: context,
      onRetry: () => _retryFailedRequests(),
      processingQueueNotifier: _interceptor.processingQueueNotifier,
    );
  }

  /// Hide the network connectivity dialog
  void _hideNetworkDialog() {
    final context = navigatorKey.currentContext;

    if (context == null || !_isDialogShown) {
      return;
    }

    _isDialogShown = false;
    NetworkConnectivityDialog.dismiss(context);
  }

  /// Check if requests are currently being processed
  bool get isProcessingQueue => _interceptor.isProcessingQueue;

  /// Retry all failed requests
  Future<void> _retryFailedRequests() async {
    await _interceptor.retryQueuedRequests();
    // Dialog state will be managed automatically through onNetworkRestored callback
    // when requests succeed, or button loading state will be reset when requests fail
  }

  /// Check if dialog is currently shown
  bool get isDialogShown => _isDialogShown;

  /// Manually trigger network error (for testing)
  void triggerNetworkError() {
    _showNetworkDialog();
  }
}
