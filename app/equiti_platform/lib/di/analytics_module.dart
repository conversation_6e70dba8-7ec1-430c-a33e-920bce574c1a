import 'dart:io';

import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:equiti_platform/config/app_config.dart';
import 'package:equiti_platform/config/equiti_env.dart';
import 'package:injectable/injectable.dart';

@module
abstract class AnalyticsModule {
  @singleton
  AnalyticsConfig analyticsConfig(AppConfig config) => AnalyticsConfig(
    newRelicConfig: NewRelicConfig(
      appToken: _getNewRelicAppToken(config),
      analyticsEventEnabled: true,
      networkErrorRequestEnabled: true,
      httpInstrumentationEnabled: true,
      networkRequestEnabled: true,
      httpResponseBodyCaptureEnabled: true,
    ),
    debugMode: config.env != EquitiEnv.prod,
    globalAttributes: {
      'environment': config.env.name,
      'platform':
          Platform.isIOS
              ? 'ios'
              : Platform.isAndroid
              ? 'android'
              : 'unknown',
    },
  );

  String _getNewRelicAppToken(AppConfig config) {
    if (Platform.isIOS) {
      return config.newRelicIOSToken;
    } else if (Platform.isAndroid) {
      return config.newRelicAndroidToken;
    }
    throw Exception('New Relic is not supported on this platform');
  }
}
