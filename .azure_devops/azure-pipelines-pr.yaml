# azure-pipelines/pr-validation-pipeline.yml
name: PR Validation Pipeline
trigger: none

variables:
- name: BASH_ENV
  value: "~/.profile"
- name: DOCKER_REGISTRY_REPO
  value: equiti.flutter
- group: DockerRegistry

stages:
- stage: 'PR_Validation'
  displayName: 'PR Validation'
  jobs:
  - job: PR_Validation_Job
    pool: 
      name: Mobile-Pipeline-VMSS
    container:
      image: $(DOCKER_REGISTRY_SERVER)/$(DOCKER_REGISTRY_REPO):MobileFlutterDockerFile-latest
      endpoint: ACR-01z
      options: --privileged

    steps:
    - checkout: self
      fetchDepth: 0

    - template: templates/initialize-container-user.yaml
    
    - script: dart pub global run melos bs
      displayName: '<PERSON>os Bootstrap'

    - template: templates/analyze.yaml
      parameters:
        enableFormatCheck: true
        enableAnalyzer: true

    - script: dart pub global run melos run test_with_coverage_dark
      displayName: 'Running tests for Arabic and Dark theme'
    
    - template: templates/set-melos-packages.yaml

    - script: dart pub global run melos run dependency_validator
      displayName: "Running dependency validator"

    - script: dart pub global run melos run verify-build
      displayName: "Verify all selected packages build correctly"

    - template: templates/test.yaml

- stage: 'Build_Android_Equiti_Platform'
  displayName: 'Build Android Equiti Platform'
  trigger: manual
  jobs:
  - job: Build_Android_Equiti_Platform_Job
    pool: 
      name: Mobile-Devops Pool
    container:
      image: $(DOCKER_REGISTRY_SERVER)/$(DOCKER_REGISTRY_REPO):MobileFlutterAndroidDockerFile-latest
      endpoint: ACR-01z
      options: --privileged

    steps:
    - checkout: self
      fetchDepth: 0

    - template: templates/initialize-container-user.yaml

    - script: dart pub global run melos bs
      displayName: 'Melos Bootstrap'

    - template: templates/build-android.yaml
      parameters:
        buildType: 'release'
        flavor: 'dev'
        outputFormat: 'apk'
        outputDirectory: 'build/app/outputs'
        verbose: false
        appPath: 'app/equiti_platform'
        configFilePath: '../../.env/config.dev.json'
