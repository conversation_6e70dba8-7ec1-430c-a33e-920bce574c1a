name: Golden Tests Screenshot Generation Pipeline
trigger: none

variables:
- name: BASH_ENV
  value: "~/.profile"
- name: DOCKER_REGISTRY_REPO
  value: equiti.flutter
# - group: Mobile-CI-VG
- group: DockerRegistry

stages:
- stage: 'Goldens_Generation'
  displayName: 'Goldens Generation'
  jobs:
  - job: Goldens_Generation_Job
    pool: 
      name: Mobile-Pipeline-VMSS
    container:
      image: $(DOCKER_REGISTRY_SERVER)/$(DOCKER_REGISTRY_REPO):MobileFlutterDockerFile-latest
      endpoint: ACR-01z
      options: --privileged

    steps:
    - checkout: self
      fetchDepth: 0
      persistCredentials: true
    
    - template: templates/initialize-container-user.yaml

    - script: |
        git config user.email "<EMAIL>"
        git config user.name "Equiti Platform"
      displayName: 'Configure Git'

    - script: dart pub global run melos bs
      displayName: 'Melos Bootstrap'

    # TODO: Move this step to post setting melos packages once tests in all the packages have been fixed for arabic
    - script: dart pub global run melos run update_goldens_dark
      displayName: 'Updating goldens for Dark theme and Arabic'

    - template: templates/set-melos-packages.yaml
    
    - script: dart pub global run melos run update_goldens
      displayName: 'Updating goldens'
      
    - script: |
        if [[ -n $(git status -s) ]]; then
          git add .
          git commit -m "feat: Update golden files"
          git push origin HEAD:$(Build.SourceBranch)
          echo "Changes committed and pushed."
        else
          echo "No changes to commit."
        fi
      displayName: 'Commit and push changes if any'