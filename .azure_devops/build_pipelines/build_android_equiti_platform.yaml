# azure-pipelines/azure-pipelines-build-android-equiti-platform.yaml
name: Build Android Pipeline
trigger: none

parameters:
- name: environment
  displayName: 'Environment'
  type: string
  default: 'development'
  values:
  - development
  - release
  # - staging
  - production
- name: deploy
  displayName: 'Deploy to Firebase App Distribution'
  type: boolean
  default: true

variables:
- name: BASH_ENV
  value: "~/.profile"
- name: DOCKER_REGISTRY_REPO
  value: equiti.flutter
- group: DockerRegistry
- group: FirebaseSecrets
# Environment to app flavor mapping
- name: AppFlavor
  ${{ if eq(parameters.environment, 'development') }}:
    value: 'dev'
  ${{ if eq(parameters.environment, 'staging') }}:
    value: 'stg'
  ${{ if eq(parameters.environment, 'release') }}:
    value: 'rel'
  ${{ if eq(parameters.environment, 'production') }}:
    value: 'prod'

stages:
- stage: 'Build_Android'
  displayName: 'Build Android'
  jobs:
  - job: Build_Android_Job
    pool: 
      name: Mobile-Pipeline-VMSS
    timeoutInMinutes: 90
    container:
      image: $(DOCKER_REGISTRY_SERVER)/$(DOCKER_REGISTRY_REPO):MobileFlutterAndroidDockerFile-latest 
      endpoint: ACR-01z
      options: --privileged

    steps:
    - checkout: self
      fetchDepth: 0

    - template: ../templates/initialize-container-user.yaml
    
    - script: dart pub global run melos bs
      displayName: 'Melos Bootstrap'

    - template: ../templates/build-android.yaml
      parameters:
        buildType: 'release'
        flavor: $(AppFlavor)
        outputFormat: 'apk'
        outputDirectory: 'build/app/outputs'
        verbose: false
        appPath: 'app/equiti_platform'
        configFilePath: '../../.env/config.$(AppFlavor).json'

- stage: 'Deploy_Android'
  displayName: 'Deploy Android'
  dependsOn: Build_Android
  condition: and(succeeded(), eq(${{ parameters.deploy }}, true))
  jobs:
  - job: Deploy_Firebase
    pool: 
      name: Mobile-Devops Pool
    container:
      image: $(DOCKER_REGISTRY_SERVER)/$(DOCKER_REGISTRY_REPO):86569-dev
      endpoint: ACR-01z
      options: --privileged
    steps:
    - checkout: self
      fetchDepth: 0

    - template: ../templates/initialize-container-user.yaml
    
    - template: ../templates/deploy-android.yaml
      parameters:
        artifactName: 'FlutterAPKArtifact'
        appPath: 'app/equiti_platform'
        appFlavor: $(AppFlavor)
        
