import 'package:hub/src/domain/repository/platform_preferences_repository.dart';
import 'package:hub/src/domain/usecase/get_interface_preferences_use_case.dart';
import 'package:hub/src/domain/usecase/get_language_model_from_language_code_usecase.dart';
import 'package:hub/src/domain/usecase/save_interface_preferences_use_case.dart';
import 'package:hub/src/navigation/hub_navigation.dart';
import 'package:hub/src/presentation/language_selection/bloc/language_selection_bloc.dart';
import 'package:hub/src/domain/repository/account_repository.dart';
import 'package:hub/src/domain/usecase/get_account_activity_use_case.dart';
import 'package:hub/src/presentation/activity_notificactions/bloc/activity_notifications_bloc.dart';
import 'package:hub/src/presentation/product_hub_settings/bloc/product_hub_settings_bloc.dart';
import 'package:hub/src/presentation/profile_settings/bloc/profile_settings_bloc.dart';
import 'package:hub/src/presentation/profile_settings/change_credentials/bloc/change_credentials_bloc.dart';
import 'package:hub/src/presentation/theme_selection/bloc/theme_selection_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:locale_manager/locale_manager.dart';
import 'package:login/login.dart';
import 'package:preferences/preferences.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:user_account/user_account.dart';
import 'package:clock/clock.dart';
import 'package:api_client/api_client.dart';
import 'package:domain/domain.dart';

@module
abstract class HubModule {
  @injectable
  ThemeSelectionBloc themeSelectionBloc(ThemeManager themeManager) =>
      ThemeSelectionBloc(themeManager: themeManager);

  @injectable
  GetLanguageModelFromLanguageCodeUsecase
  getLanguageModelFromLanguageCodeUsecase() =>
      GetLanguageModelFromLanguageCodeUsecase();

  @injectable
  GetInterfacePreferencesUseCase getInterfacePreferencesUseCase(
    PlatformPreferencesRepository platformPreferencesRepository,
    LocaleManager localeManager,
  ) => GetInterfacePreferencesUseCase(
    preferences: platformPreferencesRepository,
    localeManager: localeManager,
  );

  @injectable
  SaveInterfacePreferencesUseCase saveInterfacePreferencesUseCase(
    PlatformPreferencesRepository platformPreferencesRepository,
    LocaleManager localeManager,
  ) => SaveInterfacePreferencesUseCase(
    preferences: platformPreferencesRepository,
    localeManager: localeManager,
  );

  @injectable
  LanguageSelectionBloc languageSelectionBloc(
    GetLanguageModelFromLanguageCodeUsecase
    getLanguageModelFromLanguageCodeUsecase,
    GetInterfacePreferencesUseCase getInterfacePreferencesUseCase,
    SaveInterfacePreferencesUseCase saveInterfacePreferencesUseCase,
  ) => LanguageSelectionBloc(
    getLanguageModelFromLanguageCodeUsecase:
        getLanguageModelFromLanguageCodeUsecase,
    getInterfacePreferencesUseCase: getInterfacePreferencesUseCase,
    saveInterfacePreferencesUseCase: saveInterfacePreferencesUseCase,
  );

  @injectable
  PlatformPreferencesRepository platformPreferencesRepository(
    EquitiPreferences preferences,
  ) => PlatformPreferencesRepository(preferences: preferences);

  @injectable
  ProductHubSettingsBloc productHubSettingsBloc(
    ClientProfileUseCase clientProfileUseCase,
    HubNavigation hubNavigation,
    LogoutUseCase logoutUseCase,
  ) => ProductHubSettingsBloc(
    clientProfileUseCase: clientProfileUseCase,
    hubNavigation: hubNavigation,
    logoutUseCase: logoutUseCase,
  );
  @injectable
  ActivityNotificationsBloc activityNotificationsBloc(
    GetAccountActivityUseCase _getFundingUseCase,
    Clock _clock,
  ) => ActivityNotificationsBloc(_getFundingUseCase, _clock);

  @injectable
  GetAccountActivityUseCase getAccountActivityUseCase(
    AccountRepository _repository,
  ) => GetAccountActivityUseCase(_repository);

  @injectable
  AccountRepository accountRepository(ApiClientBase apiClient) =>
      AccountRepository(apiClient);

  @injectable
  ProfileSettingsBloc profileSettingsBloc(
    ClientProfileUseCase clientProfileUseCase,
    UploadImageUseCase uploadImageUseCase,
  ) => ProfileSettingsBloc(
    clientProfileUseCase: clientProfileUseCase,
    uploadImageUseCase: uploadImageUseCase,
  );

  @injectable
  ChangeCredentialsBloc changeCredentialsBloc(
    GetCountryUseCase getCountryUseCase,
    ChangeCredentialsUseCase changeCredentialsUseCase,
  ) => ChangeCredentialsBloc(getCountryUseCase, changeCredentialsUseCase);
}
