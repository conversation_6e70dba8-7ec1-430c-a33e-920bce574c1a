//@GeneratedMicroModule;HubPackageModule;package:hub/src/di/di_initializer.module.dart
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i687;

import 'package:api_client/api_client.dart' as _i633;
import 'package:clock/clock.dart' as _i454;
import 'package:domain/domain.dart' as _i494;
import 'package:hub/src/di/hub_module.dart' as _i955;
import 'package:hub/src/domain/repository/account_repository.dart' as _i967;
import 'package:hub/src/domain/repository/platform_preferences_repository.dart'
    as _i218;
import 'package:hub/src/domain/usecase/get_account_activity_use_case.dart'
    as _i783;
import 'package:hub/src/domain/usecase/get_interface_preferences_use_case.dart'
    as _i448;
import 'package:hub/src/domain/usecase/get_language_model_from_language_code_usecase.dart'
    as _i885;
import 'package:hub/src/domain/usecase/save_interface_preferences_use_case.dart'
    as _i596;
import 'package:hub/src/navigation/hub_navigation.dart' as _i604;
import 'package:hub/src/presentation/activity_notificactions/bloc/activity_notifications_bloc.dart'
    as _i191;
import 'package:hub/src/presentation/language_selection/bloc/language_selection_bloc.dart'
    as _i493;
import 'package:hub/src/presentation/product_hub_settings/bloc/product_hub_settings_bloc.dart'
    as _i271;
import 'package:hub/src/presentation/profile_settings/bloc/profile_settings_bloc.dart'
    as _i574;
import 'package:hub/src/presentation/profile_settings/change_credentials/bloc/change_credentials_bloc.dart'
    as _i919;
import 'package:hub/src/presentation/theme_selection/bloc/theme_selection_bloc.dart'
    as _i114;
import 'package:injectable/injectable.dart' as _i526;
import 'package:locale_manager/locale_manager.dart' as _i385;
import 'package:login/login.dart' as _i944;
import 'package:preferences/preferences.dart' as _i695;
import 'package:theme_manager/theme_manager.dart' as _i811;
import 'package:user_account/user_account.dart' as _i43;

class HubPackageModule extends _i526.MicroPackageModule {
  // initializes the registration of main-scope dependencies inside of GetIt
  @override
  _i687.FutureOr<void> init(_i526.GetItHelper gh) {
    final hubModule = _$HubModule();
    gh.factory<_i885.GetLanguageModelFromLanguageCodeUsecase>(
      () => hubModule.getLanguageModelFromLanguageCodeUsecase(),
    );
    gh.factory<_i271.ProductHubSettingsBloc>(
      () => hubModule.productHubSettingsBloc(
        gh<_i43.ClientProfileUseCase>(),
        gh<_i604.HubNavigation>(),
        gh<_i944.LogoutUseCase>(),
      ),
    );
    gh.factory<_i967.AccountRepository>(
      () => hubModule.accountRepository(gh<_i633.ApiClientBase>()),
    );
    gh.factory<_i574.ProfileSettingsBloc>(
      () => hubModule.profileSettingsBloc(
        gh<_i43.ClientProfileUseCase>(),
        gh<_i43.UploadImageUseCase>(),
      ),
    );
    gh.factory<_i919.ChangeCredentialsBloc>(
      () => hubModule.changeCredentialsBloc(
        gh<_i494.GetCountryUseCase>(),
        gh<_i494.ChangeCredentialsUseCase>(),
      ),
    );
    gh.factory<_i218.PlatformPreferencesRepository>(
      () => hubModule.platformPreferencesRepository(
        gh<_i695.EquitiPreferences>(),
      ),
    );
    gh.factory<_i783.GetAccountActivityUseCase>(
      () => hubModule.getAccountActivityUseCase(gh<_i967.AccountRepository>()),
    );
    gh.factory<_i114.ThemeSelectionBloc>(
      () => hubModule.themeSelectionBloc(gh<_i811.ThemeManager>()),
    );
    gh.factory<_i448.GetInterfacePreferencesUseCase>(
      () => hubModule.getInterfacePreferencesUseCase(
        gh<_i218.PlatformPreferencesRepository>(),
        gh<_i385.LocaleManager>(),
      ),
    );
    gh.factory<_i596.SaveInterfacePreferencesUseCase>(
      () => hubModule.saveInterfacePreferencesUseCase(
        gh<_i218.PlatformPreferencesRepository>(),
        gh<_i385.LocaleManager>(),
      ),
    );
    gh.factory<_i191.ActivityNotificationsBloc>(
      () => hubModule.activityNotificationsBloc(
        gh<_i783.GetAccountActivityUseCase>(),
        gh<_i454.Clock>(),
      ),
    );
    gh.factory<_i493.LanguageSelectionBloc>(
      () => hubModule.languageSelectionBloc(
        gh<_i885.GetLanguageModelFromLanguageCodeUsecase>(),
        gh<_i448.GetInterfacePreferencesUseCase>(),
        gh<_i596.SaveInterfacePreferencesUseCase>(),
      ),
    );
  }
}

class _$HubModule extends _i955.HubModule {}
