import 'package:hub/src/presentation/profile_settings/widgets/profile_tab_enum.dart';
import 'package:user_account/user_account.dart';
import 'package:domain/domain.dart';
import 'package:hub/src/domain/enums/credential_type.dart';

abstract interface class HubNavigation {
  void gotoBrokerage({AccountType accountType = AccountType.trading});
  void gotoGold();
  void gotoWealth();
  void gotoSettings();
  void gotoProfile({
    required ClientProfileData clientProfileData,
    ProfileTabEnum? initialIndex,
    //added then callback so that we can reload once user is back to this screen
    void Function()? thenCallback,
  });
  void gotoHistoricalPerformance();
  void logout();
  void goToDepositPaymentOptions(DepositFlowConfig depositFlowConfig);
  void goToWithdrawPaymentOptions(String popUntilRoute);
  void goToTransferFundsScreen(String originRoute);
  void goToTrading();
  void goToChangeCredentials({required CredentialType type});
  void goToUpdatePhoneFlow({required UpdatePhoneArgs args});
  void goToVerifyPhonePageIntro({required UpdatePhoneArgs args});
  void goToHub();
}
