import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'package:hub/src/di/di_container.dart';
import 'package:hub/src/domain/enums/credential_type.dart';
import 'package:hub/src/presentation/profile_settings/change_credentials/bloc/change_credentials_bloc.dart';
import 'package:hub/src/assets/assets.gen.dart' as hub;

class ChangeCredentialScreen extends StatefulWidget {
  const ChangeCredentialScreen({super.key, required this.type});

  final CredentialType type;

  @override
  State<ChangeCredentialScreen> createState() => _ChangeCredentialScreenState();
}

class _ChangeCredentialScreenState extends State<ChangeCredentialScreen> {
  late final TextEditingController phoneNumberController;
  late final Future<void> _init;

  @override
  void initState() {
    super.initState();
    _init = init();
    phoneNumberController = TextEditingController();
  }

  @override
  void dispose() {
    phoneNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final styles = context.duploTextStyles;
    final theme = context.duploTheme;

    return FutureBuilder(
      future: _init,
      builder: (futureContext, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return LoadingView();
        } else if (snapshot.connectionState == ConnectionState.done) {
          return BlocProvider<ChangeCredentialsBloc>(
            create: (_) {
              final bloc = diContainer<ChangeCredentialsBloc>();
              if (widget.type == CredentialType.phone) {
                bloc.add(const ChangeCredentialsEvent.loadCountries());
              }
              return bloc;
            },
            child: BlocConsumer<ChangeCredentialsBloc, ChangeCredentialsState>(
              listener: (ctx, state) {
                switch (state.processState) {
                  case ChangeCredentialsProcessSuccessState():
                    Navigator.pop(context);
                    _showSuccessText(widget.type);
                    break;
                  case ChangeCredentialsProcessErrorState():
                    _showErrorText(widget.type);
                    break;
                  default:
                    break;
                }
              },

              buildWhen: (previous, current) => previous != current,
              builder: (blocContext, state) {
                final bloc = blocContext.read<ChangeCredentialsBloc>();

                //TODO(shubham): update with generic error screen
                if (state.processState
                    is ChangeCredentialsProcessGetCountriesErrorState) {
                  return Center(
                    child: DuploText(
                      text: 'Something went wrong',
                      style: styles.textMd,
                    ),
                  );
                }

                if (state.processState is ChangeCredentialsLoadingState) {
                  return const LoadingView();
                }

                return Scaffold(
                  backgroundColor: theme.background.bgPrimary,
                  appBar: DuploAppBar(title: getTitle(widget.type)),
                  body: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        DuploText(
                          text: getHeaderText(widget.type),
                          style: styles.textXl,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textPrimary,
                        ),
                        const SizedBox(height: 16),
                        DuploText(
                          text: getSubText(widget.type),
                          style: styles.textSm,
                          color: theme.text.textSecondary,
                        ),
                        const SizedBox(height: 24),
                        getBody(widget.type, blocContext),
                        const Spacer(),
                        DuploButton.defaultPrimary(
                          title: 'Continue',
                          onTap: () {
                            if (state.isConfirmButtonEnabled) {
                              FocusManager.instance.primaryFocus?.unfocus();
                              SystemChannels.textInput.invokeMethod(
                                'TextInput.hide',
                              );
                              showRedirectionSheet();
                              Future<void>.delayed(
                                const Duration(seconds: 2),
                              ).then((_) {
                                Navigator.pop(context);
                                bloc.add(
                                  ChangeCredentialsEvent.confirmButtonPressed(
                                    type: widget.type,
                                  ),
                                );
                              });
                            }
                          },
                          useFullWidth: true,
                          isLoading: state.isLoading,
                          trailingIcon:
                              Assets.images
                                  .chevronRightDirectional(context)
                                  .keyName,
                          isDisabled: !state.isConfirmButtonEnabled,
                        ),
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                );
              },
            ),
          );
        }
        return DuploLoadingIndicator(color: theme.foreground.fgPrimary);
      },
    );
  }

  void _showSuccessText(CredentialType type) {
    final localization = EquitiLocalization.of(context);
    switch (type) {
      case CredentialType.email:
        _showToast(localization.hub_emailChangedSuccessfully);
        break;
      case CredentialType.phone:
        _showToast(localization.hub_phoneChangedSuccessfully);
        break;
      case CredentialType.password:
        _showToast(localization.hub_passwordChangedSuccessfully);
        break;
    }
  }

  void _showErrorText(CredentialType type) {
    final localization = EquitiLocalization.of(context);
    switch (type) {
      case CredentialType.email:
        _showToast(localization.hub_couldntUpdateEmail);
        break;
      case CredentialType.phone:
        _showToast(localization.hub_couldntUpdatePhone);
        break;
      case CredentialType.password:
        _showToast(localization.hub_couldntUpdatePassword);
        break;
    }
  }

  void _showToast(String text) {
    final styles = context.duploTextStyles;
    final theme = context.duploTheme;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: DuploText(
          text: text,
          style: styles.textSm,
          fontWeight: DuploFontWeight.medium,
          color: theme.foreground.fgWhite,
        ),
        showCloseIcon: true,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void showRedirectionSheet() async {
    final styles = context.duploTextStyles;
    final theme = context.duploTheme;
    final localization = EquitiLocalization.of(context);
    await DuploSheet.showModalSheetV2<void>(
      context,
      swipeDismissible: false,
      barrierDismissible: false,
      content: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            hub.Assets.images.redirectionImage.svg(),
            SizedBox(height: 16),
            DuploText(
              text: localization.hub_redirection_title,
              style: styles.textXl,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            DuploText(
              text: localization.hub_redirection_subtitle,
              style: styles.textSm,
              color: theme.text.textSecondary,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showCountrySelector(
    BuildContext context,
    ChangeCredentialsBloc bloc,
    ChangeCredentialsState state,
  ) {
    DuploDropDown.customBottomSheetSelector(
      context: context,
      bottomSheetTitle: 'Select Country',
      items:
          state.countries
              .map(
                (e) => DropDownItemModel(
                  title: "${e.name} (${e.dialCode ?? ''})",
                  image: FlagProvider.getFlagFromCountryCode(e.code),
                ),
              )
              .toList(),
      selectedIndex: state.selectedCountryIndex,
      onChanged: (index) {
        if (index != -1) {
          bloc.add(
            ChangeCredentialsEvent.countryCodeSelected(selectedIndex: index),
          );
          phoneNumberController.clear();
        }
      },
    );
  }

  String getTitle(CredentialType type) {
    final localization = EquitiLocalization.of(context);
    switch (type) {
      case CredentialType.email:
        return localization.hub_changeEmailAddress;
      case CredentialType.phone:
        return localization.hub_changePhoneNumber;
      case CredentialType.password:
        return localization.hub_changePassword;
    }
  }

  String getHeaderText(CredentialType type) {
    final localization = EquitiLocalization.of(context);
    switch (type) {
      case CredentialType.email:
        return localization.hub_changeYourEmailAddress;
      case CredentialType.phone:
        return localization.hub_changeYourPhoneNumber;
      case CredentialType.password:
        return localization.hub_newPassword;
    }
  }

  String getSubText(CredentialType type) {
    final localization = EquitiLocalization.of(context);
    switch (type) {
      case CredentialType.email:
      case CredentialType.phone:
        return localization.hub_credentialPageSubtext;
      case CredentialType.password:
        return localization.hub_createStrongPassword;
    }
  }

  Widget getBody(CredentialType type, BuildContext ctx) {
    final bloc = ctx.read<ChangeCredentialsBloc>();
    final state = ctx.read<ChangeCredentialsBloc>().state;
    final localization = EquitiLocalization.of(context);
    switch (type) {
      case CredentialType.email:
        return DuploTextField(
          label: localization.hub_emailAddress,
          hint: localization.hub_enterNewEmailAddress,
          keyboardType: TextInputType.emailAddress,
          autofillHints: const [AutofillHints.email],
          onChanged: (value) {
            bloc.add(ChangeCredentialsEvent.emailChanged(email: value));
          },
          errorMessage: state.errorMessage,
        );
      case CredentialType.phone:
        return DuploPhoneTextField(
          controller: phoneNumberController,
          label: localization.hub_phoneNumber,
          hint:
              state.placeholderText.isNotEmpty
                  ? state.placeholderText
                  : '7900000',
          autoFocus: false,
          selectedCountryCode: state.selectedCountryCode,
          selectedCountryFlag: state.selectedCountryFlag,
          errorMessage: state.errorMessage,
          onPhoneChanged: (value) {
            bloc.add(
              ChangeCredentialsEvent.phoneNumberChanged(phoneNumber: value),
            );
          },
          onSelectCountryCode: () {
            _showCountrySelector(ctx, bloc, state);
          },
        );
      case CredentialType.password:
        return PasswordFieldValidator(
          semanticsIdentifier: "password_field",
          needValidationComponent: true,
          onPasswordValidation: (password, isValid) {
            bloc.add(
              ChangeCredentialsEvent.passwordChanged(
                password: password,
                isValid: isValid,
              ),
            );
          },
        );
    }
  }
}
