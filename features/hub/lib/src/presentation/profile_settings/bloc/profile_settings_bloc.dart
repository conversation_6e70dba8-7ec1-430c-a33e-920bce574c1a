import 'dart:async';
import 'dart:io';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:user_account/user_account.dart';

part 'profile_settings_event.dart';
part 'profile_settings_state.dart';

part 'profile_settings_bloc.freezed.dart';

class ProfileSettingsBloc
    extends Bloc<ProfileSettingsEvent, ProfileSettingsState> {
  ProfileSettingsBloc({
    required ClientProfileUseCase clientProfileUseCase,
    required UploadImageUseCase uploadImageUseCase,
  }) : _clientProfileUseCase = clientProfileUseCase,
       _uploadImageUseCase = uploadImageUseCase,
       super(_ProfileSettingsState()) {
    on<_UpdateTab>(_updateTab);
    on<_FetchClientProfile>(_fetchClientProfile);
    on<_UploadImage>(_uploadImage);
    on<_ResetBottomSheet>(_onResetBottomSheet);
  }

  final ClientProfileUseCase _clientProfileUseCase;
  final UploadImageUseCase _uploadImageUseCase;

  FutureOr<void> _updateTab(
    _UpdateTab event,
    Emitter<ProfileSettingsState> emit,
  ) {
    emit(state.copyWith(selectedTab: event.tabName));
  }

  FutureOr<void> _fetchClientProfile(
    _FetchClientProfile event,
    Emitter<ProfileSettingsState> emit,
  ) async {
    emit(state.copyWith(processState: ProfileSettingsProcessState.loading()));
    final result = await _clientProfileUseCase().run();

    if (isClosed) return;

    result.fold(
      (failure) {
        emit(state.copyWith(processState: ProfileSettingsProcessState.error()));
        emit(state.copyWith(isImageLoading: false));
      },
      (clientProfile) {
        emit(
          state.copyWith(
            processState: ProfileSettingsProcessState.success(),
            clientProfile: clientProfile,
          ),
        );
        emit(state.copyWith(isImageLoading: false));
      },
    );
  }

  FutureOr<void> _uploadImage(
    _UploadImage event,
    Emitter<ProfileSettingsState> emit,
  ) async {
    emit(state.copyWith(isImageLoading: true));

    await _uploadImageUseCase(event.image).run().then((result) {
      if (isClosed) return;

      result.fold(
        (failure) {
          emit(
            state.copyWith(isImageLoading: false, showErrorBottomSheet: true),
          );
        },
        (_) {
          add(ProfileSettingsEvent.fetchClientProfile());
        },
      );
    });
  }

  FutureOr<void> _onResetBottomSheet(
    _ResetBottomSheet event,
    Emitter<ProfileSettingsState> emit,
  ) {
    emit(state.copyWith(showErrorBottomSheet: false));
  }
}
