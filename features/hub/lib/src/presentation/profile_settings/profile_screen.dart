import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:hub/src/di/di_container.dart';

import 'package:hub/src/presentation/profile_settings/bloc/profile_settings_bloc.dart';
import 'package:hub/src/presentation/profile_settings/widgets/profile_screen_content.dart';
import 'package:hub/src/presentation/profile_settings/widgets/profile_tab_enum.dart';

import 'package:user_account/user_account.dart';

class ProfileScreen extends StatelessWidget {
  final ClientProfileData clientProfileData;
  final ProfileTabEnum initialTab;
  const ProfileScreen({
    super.key,
    required this.clientProfileData,
    this.initialTab = ProfileTabEnum.personal,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ProfileSettingsBloc>(
      create: (_) => diContainer<ProfileSettingsBloc>(),
      child: BlocBuilder<ProfileSettingsBloc, ProfileSettingsState>(
        buildWhen:
            (previous, current) =>
                previous.clientProfile != current.clientProfile,
        builder: (builderContext, state) {
          return ProfileScreenContent(
            clientProfileData: state.clientProfile ?? clientProfileData,
            initialTab: initialTab,
            parentBuildContextForLocalization: context,
          );
        },
      ),
    );
  }
}
