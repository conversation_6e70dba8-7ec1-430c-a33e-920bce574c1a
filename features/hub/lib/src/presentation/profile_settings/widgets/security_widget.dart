import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:hub/src/di/di_container.dart';
import 'package:hub/src/domain/enums/credential_type.dart';
import 'package:hub/src/navigation/hub_navigation.dart';
import 'package:hub/src/presentation/profile_settings/widgets/duplo_image_and_text_widget.dart';

class SecurityWidget extends StatelessWidget {
  const SecurityWidget();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 32),
        DuploImageAndTextWidget(
          imagePath: null,
          text: EquitiLocalization.of(context).hub_change_password_security_tap,
          semanticLabel: 'security_option_change_password',
          onTap: () {
            diContainer<HubNavigation>().goToChangeCredentials(
              type: CredentialType.password,
            );
          },
        ),
      ],
    );
  }
}
