import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hub/src/presentation/profile_settings/bloc/profile_settings_bloc.dart';
import 'package:hub/src/presentation/profile_settings/widgets/profile_tab_enum.dart';
import 'package:hub/src/presentation/profile_settings/widgets/user_profile_edit_widget.dart';
import 'package:hub/src/presentation/profile_settings/widgets/user_profile_info_widget.dart';
import 'package:user_account/user_account.dart';
import 'package:visibility_detector/visibility_detector.dart';

class ProfileScreenContent extends StatefulWidget {
  final ClientProfileData clientProfileData;
  final ProfileTabEnum initialTab;
  final BuildContext parentBuildContextForLocalization;
  const ProfileScreenContent({
    required this.clientProfileData,
    required this.initialTab,
    required this.parentBuildContextForLocalization,
  });

  @override
  State<ProfileScreenContent> createState() => _ProfileScreenContentState();
}

class _ProfileScreenContentState extends State<ProfileScreenContent>
    with SingleTickerProviderStateMixin {
  late final TabController _tabController;
  late bool shouldFetchClientProfile;

  @override
  void initState() {
    super.initState();
    shouldFetchClientProfile = false;
    _tabController = TabController(
      length: ProfileTabEnum.values.length,
      vsync: this,
      initialIndex: widget.initialTab.index,
    );
    _tabController.addListener(() {
      context.read<ProfileSettingsBloc>().add(
        ProfileSettingsEvent.updateTab(
          ProfileTabEnum.values
              .elementAtOrNull(_tabController.index)!
              .getTitle(
                EquitiLocalization.of(widget.parentBuildContextForLocalization),
              ),
        ),
      );
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);

    return BlocConsumer<ProfileSettingsBloc, ProfileSettingsState>(
      listener: (listeneContext, state) {
        if (state.showErrorBottomSheet) {
          DuploErrorSheet.show<Widget>(
            context: context,
            bodyTitle: "Something went wrong",
          ).then<void>((_) {
            listeneContext.read<ProfileSettingsBloc>().add(
              ProfileSettingsEvent.resetBottomSheet(),
            );
          });
        }
      },
      buildWhen: (previous, current) => previous != current,
      builder: (ctx, state) {
        final clientProfileData =
            state.clientProfile ?? widget.clientProfileData;
        return VisibilityDetector(
          key: const Key('profile_screen'),
          onVisibilityChanged: (info) {
            if (shouldFetchClientProfile && info.visibleFraction > 0) {
              context.read<ProfileSettingsBloc>().add(
                ProfileSettingsEvent.fetchClientProfile(),
              );
            }
            shouldFetchClientProfile = true;
          },
          child: Scaffold(
            appBar: AppBar(
              backgroundColor: theme.background.bgPrimary,
              title: DuploText(
                text: localization.hub_profileSettings,
                style: textStyles.textSm,
                color: theme.text.textPrimary,
                fontWeight: DuploFontWeight.semiBold,
              ),
            ),
            body: Column(
              children: [
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 0),
                  switchInCurve: Curves.easeInOut,
                  switchOutCurve: Curves.easeInOut,

                  transitionBuilder: (child, animation) {
                    final slide = Tween<Offset>(
                      begin: const Offset(0.1, 0),
                      end: Offset.zero,
                    ).animate(animation);
                    return SlideTransition(position: slide, child: child);
                  },
                  child:
                      _tabController.index == ProfileTabEnum.personal.index
                          ? UserProfileEditWidget(
                            firstName: widget.clientProfileData.firstName ?? "",
                            lastName: widget.clientProfileData.lastName ?? "",
                            email: widget.clientProfileData.email ?? "",
                            imageUrl:
                                widget.clientProfileData.profileImageUrl ?? "",
                          )
                          : UserProfileInfoWidget(
                            firstName: widget.clientProfileData.firstName ?? "",
                            lastName: widget.clientProfileData.lastName ?? "",
                            email: widget.clientProfileData.email ?? "",
                            imageUrl:
                                widget.clientProfileData.profileImageUrl ?? "",
                          ),
                ),
                Expanded(
                  child: ColoredBox(
                    color: theme.background.bgPrimary,
                    child: DuploTabBar(
                      tabController: _tabController,
                      tabTitles:
                          ProfileTabEnum.values
                              .map(
                                (tab) => DuploTabBarTitle(
                                  text: tab.getTitle(localization),
                                  semanticsIdentifier: tab.semanticsIdentifier,
                                ),
                              )
                              .toList(),
                      isScrollable: true,
                      tabViews:
                          ProfileTabEnum.values
                              .map((tab) => tab.getContent(clientProfileData))
                              .toList(),
                      isFlex: false,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
