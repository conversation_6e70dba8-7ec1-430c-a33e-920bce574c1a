part of 'product_hub_settings_bloc.dart';

@freezed
sealed class ProductHubSettingsEvent with _$ProductHubSettingsEvent {
  const factory ProductHubSettingsEvent.started() = _Started;
  const factory ProductHubSettingsEvent.goToDepositPaymentOptions() =
      _GoToDepositPaymentOptions;
  const factory ProductHubSettingsEvent.goToWithdrawPaymentOptions() =
      _GoToWithdrawPaymentOptions;
  const factory ProductHubSettingsEvent.goToTransferOptions() =
      _GoToTransferOptions;
  const factory ProductHubSettingsEvent.goToGold() = _GoToGold;
  const factory ProductHubSettingsEvent.goToWealth() = _GoToWealth;
  const factory ProductHubSettingsEvent.goToAccounts() = _GoToAccounts;
  const factory ProductHubSettingsEvent.goToWallets() = _GoToWallets;
  const factory ProductHubSettingsEvent.goToTrading() = _GoToTrading;
  const factory ProductHubSettingsEvent.goToProfile() = _GoToProfile;
  const factory ProductHubSettingsEvent.goToSettings() = _GoToSettings;
  const factory ProductHubSettingsEvent.goToSecurity() = _GoToSecurity;
  const factory ProductHubSettingsEvent.goToHub() = _GoToHub;
  const factory ProductHubSettingsEvent.logout() = _Logout;
}
