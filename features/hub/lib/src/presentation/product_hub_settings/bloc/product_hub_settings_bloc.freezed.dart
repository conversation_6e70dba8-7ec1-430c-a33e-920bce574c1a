// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_hub_settings_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ProductHubSettingsEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductHubSettingsEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsEvent()';
}


}

/// @nodoc
class $ProductHubSettingsEventCopyWith<$Res>  {
$ProductHubSettingsEventCopyWith(ProductHubSettingsEvent _, $Res Function(ProductHubSettingsEvent) __);
}


/// @nodoc


class _Started implements ProductHubSettingsEvent {
  const _Started();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Started);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsEvent.started()';
}


}




/// @nodoc


class _GoToDepositPaymentOptions implements ProductHubSettingsEvent {
  const _GoToDepositPaymentOptions();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToDepositPaymentOptions);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsEvent.goToDepositPaymentOptions()';
}


}




/// @nodoc


class _GoToWithdrawPaymentOptions implements ProductHubSettingsEvent {
  const _GoToWithdrawPaymentOptions();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToWithdrawPaymentOptions);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsEvent.goToWithdrawPaymentOptions()';
}


}




/// @nodoc


class _GoToTransferOptions implements ProductHubSettingsEvent {
  const _GoToTransferOptions();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToTransferOptions);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsEvent.goToTransferOptions()';
}


}




/// @nodoc


class _GoToGold implements ProductHubSettingsEvent {
  const _GoToGold();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToGold);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsEvent.goToGold()';
}


}




/// @nodoc


class _GoToWealth implements ProductHubSettingsEvent {
  const _GoToWealth();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToWealth);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsEvent.goToWealth()';
}


}




/// @nodoc


class _GoToAccounts implements ProductHubSettingsEvent {
  const _GoToAccounts();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToAccounts);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsEvent.goToAccounts()';
}


}




/// @nodoc


class _GoToWallets implements ProductHubSettingsEvent {
  const _GoToWallets();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToWallets);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsEvent.goToWallets()';
}


}




/// @nodoc


class _GoToTrading implements ProductHubSettingsEvent {
  const _GoToTrading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToTrading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsEvent.goToTrading()';
}


}




/// @nodoc


class _GoToProfile implements ProductHubSettingsEvent {
  const _GoToProfile();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToProfile);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsEvent.goToProfile()';
}


}




/// @nodoc


class _GoToSettings implements ProductHubSettingsEvent {
  const _GoToSettings();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToSettings);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsEvent.goToSettings()';
}


}




/// @nodoc


class _GoToSecurity implements ProductHubSettingsEvent {
  const _GoToSecurity();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToSecurity);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsEvent.goToSecurity()';
}


}




/// @nodoc


class _GoToHub implements ProductHubSettingsEvent {
  const _GoToHub();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToHub);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsEvent.goToHub()';
}


}




/// @nodoc


class _Logout implements ProductHubSettingsEvent {
  const _Logout();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Logout);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsEvent.logout()';
}


}




/// @nodoc
mixin _$ProductHubSettingsState {

 ProductHubSettingsProcessState get processState; ClientProfileData? get clientProfile;
/// Create a copy of ProductHubSettingsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductHubSettingsStateCopyWith<ProductHubSettingsState> get copyWith => _$ProductHubSettingsStateCopyWithImpl<ProductHubSettingsState>(this as ProductHubSettingsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductHubSettingsState&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.clientProfile, clientProfile) || other.clientProfile == clientProfile));
}


@override
int get hashCode => Object.hash(runtimeType,processState,clientProfile);

@override
String toString() {
  return 'ProductHubSettingsState(processState: $processState, clientProfile: $clientProfile)';
}


}

/// @nodoc
abstract mixin class $ProductHubSettingsStateCopyWith<$Res>  {
  factory $ProductHubSettingsStateCopyWith(ProductHubSettingsState value, $Res Function(ProductHubSettingsState) _then) = _$ProductHubSettingsStateCopyWithImpl;
@useResult
$Res call({
 ProductHubSettingsProcessState processState, ClientProfileData? clientProfile
});


$ProductHubSettingsProcessStateCopyWith<$Res> get processState;$ClientProfileDataCopyWith<$Res>? get clientProfile;

}
/// @nodoc
class _$ProductHubSettingsStateCopyWithImpl<$Res>
    implements $ProductHubSettingsStateCopyWith<$Res> {
  _$ProductHubSettingsStateCopyWithImpl(this._self, this._then);

  final ProductHubSettingsState _self;
  final $Res Function(ProductHubSettingsState) _then;

/// Create a copy of ProductHubSettingsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? processState = null,Object? clientProfile = freezed,}) {
  return _then(_self.copyWith(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as ProductHubSettingsProcessState,clientProfile: freezed == clientProfile ? _self.clientProfile : clientProfile // ignore: cast_nullable_to_non_nullable
as ClientProfileData?,
  ));
}
/// Create a copy of ProductHubSettingsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProductHubSettingsProcessStateCopyWith<$Res> get processState {
  
  return $ProductHubSettingsProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of ProductHubSettingsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ClientProfileDataCopyWith<$Res>? get clientProfile {
    if (_self.clientProfile == null) {
    return null;
  }

  return $ClientProfileDataCopyWith<$Res>(_self.clientProfile!, (value) {
    return _then(_self.copyWith(clientProfile: value));
  });
}
}


/// @nodoc


class _ProductHubSettingsState implements ProductHubSettingsState {
  const _ProductHubSettingsState({this.processState = const ProductHubSettingsProcessState.profileLoading(), this.clientProfile});
  

@override@JsonKey() final  ProductHubSettingsProcessState processState;
@override final  ClientProfileData? clientProfile;

/// Create a copy of ProductHubSettingsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductHubSettingsStateCopyWith<_ProductHubSettingsState> get copyWith => __$ProductHubSettingsStateCopyWithImpl<_ProductHubSettingsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductHubSettingsState&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.clientProfile, clientProfile) || other.clientProfile == clientProfile));
}


@override
int get hashCode => Object.hash(runtimeType,processState,clientProfile);

@override
String toString() {
  return 'ProductHubSettingsState(processState: $processState, clientProfile: $clientProfile)';
}


}

/// @nodoc
abstract mixin class _$ProductHubSettingsStateCopyWith<$Res> implements $ProductHubSettingsStateCopyWith<$Res> {
  factory _$ProductHubSettingsStateCopyWith(_ProductHubSettingsState value, $Res Function(_ProductHubSettingsState) _then) = __$ProductHubSettingsStateCopyWithImpl;
@override @useResult
$Res call({
 ProductHubSettingsProcessState processState, ClientProfileData? clientProfile
});


@override $ProductHubSettingsProcessStateCopyWith<$Res> get processState;@override $ClientProfileDataCopyWith<$Res>? get clientProfile;

}
/// @nodoc
class __$ProductHubSettingsStateCopyWithImpl<$Res>
    implements _$ProductHubSettingsStateCopyWith<$Res> {
  __$ProductHubSettingsStateCopyWithImpl(this._self, this._then);

  final _ProductHubSettingsState _self;
  final $Res Function(_ProductHubSettingsState) _then;

/// Create a copy of ProductHubSettingsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? processState = null,Object? clientProfile = freezed,}) {
  return _then(_ProductHubSettingsState(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as ProductHubSettingsProcessState,clientProfile: freezed == clientProfile ? _self.clientProfile : clientProfile // ignore: cast_nullable_to_non_nullable
as ClientProfileData?,
  ));
}

/// Create a copy of ProductHubSettingsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProductHubSettingsProcessStateCopyWith<$Res> get processState {
  
  return $ProductHubSettingsProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of ProductHubSettingsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ClientProfileDataCopyWith<$Res>? get clientProfile {
    if (_self.clientProfile == null) {
    return null;
  }

  return $ClientProfileDataCopyWith<$Res>(_self.clientProfile!, (value) {
    return _then(_self.copyWith(clientProfile: value));
  });
}
}

/// @nodoc
mixin _$ProductHubSettingsProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductHubSettingsProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsProcessState()';
}


}

/// @nodoc
class $ProductHubSettingsProcessStateCopyWith<$Res>  {
$ProductHubSettingsProcessStateCopyWith(ProductHubSettingsProcessState _, $Res Function(ProductHubSettingsProcessState) __);
}


/// @nodoc


class ProductHubSettingsLoadingProcessState implements ProductHubSettingsProcessState {
  const ProductHubSettingsLoadingProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductHubSettingsLoadingProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsProcessState.profileLoading()';
}


}




/// @nodoc


class ProductHubSettingsSuccessProcessState implements ProductHubSettingsProcessState {
  const ProductHubSettingsSuccessProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductHubSettingsSuccessProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsProcessState.profileSuccess()';
}


}




/// @nodoc


class ProductHubSettingsErrorProcessState implements ProductHubSettingsProcessState {
  const ProductHubSettingsErrorProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductHubSettingsErrorProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ProductHubSettingsProcessState.profileError()';
}


}




// dart format on
