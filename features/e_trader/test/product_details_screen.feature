import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/product_details/product_details_widget.dart';
import 'scenarios/product_details_screen_success_scenario.dart';
import 'scenarios/product_details_screen_failure_scenario.dart';
import 'scenarios/check_unwatchlisted_indicator_success_scenario.dart';
import 'package:bdd_steps/step/i_tap_identifier.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:clock/clock.dart';

Feature: Product Details Screen Golden Tests

@testMethodName: testGoldens
Scenario Outline: Product Details Screen and Overview tab
  Given The toast plugin is mocked
  Given The {ProductDetailsWidget(symbolDetail: SymbolDetailViewModel(symbolName: 'AUDCAD', platformName: 'AUDCAD', minLot: 0.1, maxLot: 5,lotsSteps: 0.01,), accountNumber: '********')} app is rendered <scenario>
  Then i wait for ui
  Then screenshot verified <golden_file_name> with custom pump
  Examples:
    | scenario                                          | golden_file_name                           |
    | scenarios: [productDetailsSuccessSenario]     | 'product_details_screen/chart_tab'    |

@testMethodName: testGoldens
Scenario Outline: Product Details Screen and Events tab
  Given The toast plugin is mocked
  Given The {ProductDetailsWidget(symbolDetail: SymbolDetailViewModel(symbolName: 'AUDCAD', platformName: 'AUDCAD', minLot: 0.1, maxLot: 5 ,lotsSteps: 0.01,), accountNumber: '********')} app is rendered <scenario>
  Then i wait for ui
  Then i tap {"events_tab"} identifier
  Then i wait for ui
  Then screenshot verified <golden_file_name> with custom pump
  Examples:
    | scenario                                          | golden_file_name                           |
    | scenarios: [productDetailsSuccessSenario]     | 'product_details_screen/events_tab'    |

@testMethodName: testGoldens
Scenario Outline: Product Details Screen and Events tab
  Given The toast plugin is mocked
  Given The {ProductDetailsWidget(symbolDetail: SymbolDetailViewModel(symbolName: 'AUDCAD', platformName: 'AUDCAD', minLot: 0.1, maxLot: 5,lotsSteps: 0.01,), accountNumber: '********')} app is rendered <scenario>
  Then i wait for ui
  Then i tap {"events_tab"} identifier
  Then i wait for ui
  Then screenshot verified <golden_file_name> with custom pump
  Examples:
    | scenario                                          | golden_file_name                           |
    | scenarios: [productDetailsSuccessSenario]     | 'product_details_screen/events_tab'    |
    | scenarios: [productDetailsFailSenario]     | 'product_details_screen/events_tab_failed'    |

@testMethodName: testGoldens
Scenario Outline: Product Details Screen and News tab
  Given The toast plugin is mocked
  Given The {ProductDetailsWidget(symbolDetail: SymbolDetailViewModel(symbolName: 'AUDCAD', platformName: 'AUDCAD', minLot: 0.1, maxLot: 5, lotsSteps: 0.01,), accountNumber: '********')} app is rendered <scenario>
  Then i wait for ui
  Then i tap {"news_tab"} identifier
  Then i wait for ui
  Then screenshot verified <golden_file_name> with custom pump
  Examples:
    | scenario                                          | golden_file_name                           |
    | scenarios: [productDetailsSuccessSenario]     | 'product_details_screen/news_tab'    |
    | scenarios: [productDetailsFailSenario]     | 'product_details_screen/news_tab_failed'    |

@testMethodName: testGoldens
Scenario Outline: Product Details Screen and Details tab
  Given The toast plugin is mocked
  Given The {ProductDetailsWidget(symbolDetail: SymbolDetailViewModel(symbolName: 'AUDCAD', platformName: 'AUDCAD', minLot: 0.1, maxLot: 5 ,lotsSteps: 0.01,), accountNumber: '********')} app is rendered <scenario>
  Then i wait for ui
  Then i tap {"details_tab"} identifier
  Then i wait for ui
  Then screenshot verified <golden_file_name> with custom pump
  Examples:
    | scenario                                          | golden_file_name                           |
    | scenarios: [productDetailsSuccessSenario]     | 'product_details_screen/details_tab'    |
    | scenarios: [productDetailsFailSenario]     | 'product_details_screen/details_tab_failed'    |
