// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/product_details/product_details_widget.dart';
import 'scenarios/product_details_screen_success_scenario.dart';
import 'scenarios/product_details_screen_failure_scenario.dart';
import 'scenarios/check_unwatchlisted_indicator_success_scenario.dart';
import 'package:bdd_steps/step/i_tap_identifier.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:clock/clock.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_toast_plugin_is_mocked.dart';
import './step/the_app_is_rendered.dart';
import './step/i_wait_for_ui.dart';
import './step/screenshot_verified_with_custom_pump.dart';
import 'package:bdd_steps/step/i_tap_identifier.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Product Details Screen Golden Tests''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens(
      '''Outline: Product Details Screen and Overview tab (scenarios: [productDetailsSuccessSenario], 'product_details_screen/chart_tab')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Product Details Screen and Overview tab (scenarios: [productDetailsSuccessSenario], 'product_details_screen/chart_tab')''',
          );
          await theToastPluginIsMocked(tester);
          await theAppIsRendered(
            tester,
            ProductDetailsWidget(
              symbolDetail: SymbolDetailViewModel(
                symbolName: 'AUDCAD',
                platformName: 'AUDCAD',
                minLot: 0.1,
                maxLot: 5,
                lotsSteps: 0.01,
              ),
              accountNumber: '********',
            ),
            scenarios: [productDetailsSuccessSenario],
          );
          await iWaitForUi(tester);
          await screenshotVerifiedWithCustomPump(
            tester,
            'product_details_screen/chart_tab',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Product Details Screen and Overview tab (scenarios: [productDetailsSuccessSenario], 'product_details_screen/chart_tab')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Product Details Screen and Events tab (scenarios: [productDetailsSuccessSenario], 'product_details_screen/events_tab')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Product Details Screen and Events tab (scenarios: [productDetailsSuccessSenario], 'product_details_screen/events_tab')''',
          );
          await theToastPluginIsMocked(tester);
          await theAppIsRendered(
            tester,
            ProductDetailsWidget(
              symbolDetail: SymbolDetailViewModel(
                symbolName: 'AUDCAD',
                platformName: 'AUDCAD',
                minLot: 0.1,
                maxLot: 5,
                lotsSteps: 0.01,
              ),
              accountNumber: '********',
            ),
            scenarios: [productDetailsSuccessSenario],
          );
          await iWaitForUi(tester);
          await iTapIdentifier(tester, "events_tab");
          await iWaitForUi(tester);
          await screenshotVerifiedWithCustomPump(
            tester,
            'product_details_screen/events_tab',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Product Details Screen and Events tab (scenarios: [productDetailsSuccessSenario], 'product_details_screen/events_tab')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Product Details Screen and Events tab (scenarios: [productDetailsSuccessSenario], 'product_details_screen/events_tab')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Product Details Screen and Events tab (scenarios: [productDetailsSuccessSenario], 'product_details_screen/events_tab')''',
          );
          await theToastPluginIsMocked(tester);
          await theAppIsRendered(
            tester,
            ProductDetailsWidget(
              symbolDetail: SymbolDetailViewModel(
                symbolName: 'AUDCAD',
                platformName: 'AUDCAD',
                minLot: 0.1,
                maxLot: 5,
                lotsSteps: 0.01,
              ),
              accountNumber: '********',
            ),
            scenarios: [productDetailsSuccessSenario],
          );
          await iWaitForUi(tester);
          await iTapIdentifier(tester, "events_tab");
          await iWaitForUi(tester);
          await screenshotVerifiedWithCustomPump(
            tester,
            'product_details_screen/events_tab',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Product Details Screen and Events tab (scenarios: [productDetailsSuccessSenario], 'product_details_screen/events_tab')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Product Details Screen and Events tab (scenarios: [productDetailsFailSenario], 'product_details_screen/events_tab_failed')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Product Details Screen and Events tab (scenarios: [productDetailsFailSenario], 'product_details_screen/events_tab_failed')''',
          );
          await theToastPluginIsMocked(tester);
          await theAppIsRendered(
            tester,
            ProductDetailsWidget(
              symbolDetail: SymbolDetailViewModel(
                symbolName: 'AUDCAD',
                platformName: 'AUDCAD',
                minLot: 0.1,
                maxLot: 5,
                lotsSteps: 0.01,
              ),
              accountNumber: '********',
            ),
            scenarios: [productDetailsFailSenario],
          );
          await iWaitForUi(tester);
          await iTapIdentifier(tester, "events_tab");
          await iWaitForUi(tester);
          await screenshotVerifiedWithCustomPump(
            tester,
            'product_details_screen/events_tab_failed',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Product Details Screen and Events tab (scenarios: [productDetailsFailSenario], 'product_details_screen/events_tab_failed')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Product Details Screen and News tab (scenarios: [productDetailsSuccessSenario], 'product_details_screen/news_tab')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Product Details Screen and News tab (scenarios: [productDetailsSuccessSenario], 'product_details_screen/news_tab')''',
          );
          await theToastPluginIsMocked(tester);
          await theAppIsRendered(
            tester,
            ProductDetailsWidget(
              symbolDetail: SymbolDetailViewModel(
                symbolName: 'AUDCAD',
                platformName: 'AUDCAD',
                minLot: 0.1,
                maxLot: 5,
                lotsSteps: 0.01,
              ),
              accountNumber: '********',
            ),
            scenarios: [productDetailsSuccessSenario],
          );
          await iWaitForUi(tester);
          await iTapIdentifier(tester, "news_tab");
          await iWaitForUi(tester);
          await screenshotVerifiedWithCustomPump(
            tester,
            'product_details_screen/news_tab',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Product Details Screen and News tab (scenarios: [productDetailsSuccessSenario], 'product_details_screen/news_tab')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Product Details Screen and News tab (scenarios: [productDetailsFailSenario], 'product_details_screen/news_tab_failed')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Product Details Screen and News tab (scenarios: [productDetailsFailSenario], 'product_details_screen/news_tab_failed')''',
          );
          await theToastPluginIsMocked(tester);
          await theAppIsRendered(
            tester,
            ProductDetailsWidget(
              symbolDetail: SymbolDetailViewModel(
                symbolName: 'AUDCAD',
                platformName: 'AUDCAD',
                minLot: 0.1,
                maxLot: 5,
                lotsSteps: 0.01,
              ),
              accountNumber: '********',
            ),
            scenarios: [productDetailsFailSenario],
          );
          await iWaitForUi(tester);
          await iTapIdentifier(tester, "news_tab");
          await iWaitForUi(tester);
          await screenshotVerifiedWithCustomPump(
            tester,
            'product_details_screen/news_tab_failed',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Product Details Screen and News tab (scenarios: [productDetailsFailSenario], 'product_details_screen/news_tab_failed')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Product Details Screen and Details tab (scenarios: [productDetailsSuccessSenario], 'product_details_screen/details_tab')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Product Details Screen and Details tab (scenarios: [productDetailsSuccessSenario], 'product_details_screen/details_tab')''',
          );
          await theToastPluginIsMocked(tester);
          await theAppIsRendered(
            tester,
            ProductDetailsWidget(
              symbolDetail: SymbolDetailViewModel(
                symbolName: 'AUDCAD',
                platformName: 'AUDCAD',
                minLot: 0.1,
                maxLot: 5,
                lotsSteps: 0.01,
              ),
              accountNumber: '********',
            ),
            scenarios: [productDetailsSuccessSenario],
          );
          await iWaitForUi(tester);
          await iTapIdentifier(tester, "details_tab");
          await iWaitForUi(tester);
          await screenshotVerifiedWithCustomPump(
            tester,
            'product_details_screen/details_tab',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Product Details Screen and Details tab (scenarios: [productDetailsSuccessSenario], 'product_details_screen/details_tab')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Product Details Screen and Details tab (scenarios: [productDetailsFailSenario], 'product_details_screen/details_tab_failed')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Product Details Screen and Details tab (scenarios: [productDetailsFailSenario], 'product_details_screen/details_tab_failed')''',
          );
          await theToastPluginIsMocked(tester);
          await theAppIsRendered(
            tester,
            ProductDetailsWidget(
              symbolDetail: SymbolDetailViewModel(
                symbolName: 'AUDCAD',
                platformName: 'AUDCAD',
                minLot: 0.1,
                maxLot: 5,
                lotsSteps: 0.01,
              ),
              accountNumber: '********',
            ),
            scenarios: [productDetailsFailSenario],
          );
          await iWaitForUi(tester);
          await iTapIdentifier(tester, "details_tab");
          await iWaitForUi(tester);
          await screenshotVerifiedWithCustomPump(
            tester,
            'product_details_screen/details_tab_failed',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Product Details Screen and Details tab (scenarios: [productDetailsFailSenario], 'product_details_screen/details_tab_failed')''',
            success,
          );
        }
      },
    );
  });
}
