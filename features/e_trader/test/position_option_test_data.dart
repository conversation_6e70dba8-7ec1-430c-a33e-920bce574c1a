import 'package:e_trader/fusion.dart';
import 'package:flutter/material.dart';

class PositionOptionTestData extends StatelessWidget {
  const PositionOptionTestData({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Semantics(
          identifier: 'show_position_option_sheet',
          child: TextButton(
            onPressed: () {
              showPositionOptionSheet(
                context,
                SymbolDetailViewModel(
                  symbolName: "AUDCAD",
                  imageURL:
                      "https://eqdulcimer03z.blob.core.windows.net/public/logos%2F9c4f3c19-39bf-4438-b50d-47f6ef435aeb_AUDCAD.png",
                  minLot: 0.1,
                  maxLot: 5,
                  digit: 5,
                  platformName: 'AUDCAD',
                  lotsSteps: 0.01,
                ),
                "USD",
              );
            },
            child: Text('Show Position Option'),
          ),
        ),
      ),
    );
  }
}
