import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/order_limit_type.dart';
import 'package:e_trader/src/domain/model/order_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/create_order/create_trade_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/market_order/market_order_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/information_widget.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/input_order_size_widget.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/order_limit_widget.dart';
import 'package:e_trader/src/presentation/duplo/confirmation_sheet.dart';
import 'package:e_trader/src/presentation/model/margin_information_model.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

typedef MarketOrderArgs =
    ({
      String symbol,
      String assetType,
      TradeType? tradeType,
      String accountNumber,
      MarginInformationModel? marginInformation,
      SymbolQuoteModel symbolQuoteModel,
      double minLot,
      double maxLot,
      String symbolImageUrl,
      int tabIndex,
      bool isForex,
      double lotsSteps,
    });

class MarketOrderWidget extends StatelessWidget {
  const MarketOrderWidget({required this.args, super.key});

  final MarketOrderArgs args;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (createContext) => diContainer<MarketOrderBloc>(param1: args),
      child: _MarketOrderContent(args),
    );
  }
}

class _MarketOrderContent extends StatefulWidget {
  const _MarketOrderContent(this.args);

  final MarketOrderArgs args;

  @override
  State<_MarketOrderContent> createState() => _MarketOrderContentState();
}

class _MarketOrderContentState extends State<_MarketOrderContent> {
  @override
  void didUpdateWidget(covariant _MarketOrderContent oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget != widget && widget.args.tabIndex == 0) {
      context.read<MarketOrderBloc>().add(
        MarketOrderEvent.valuesChanged(widget.args),
      );
      if (oldWidget.args.tabIndex != widget.args.tabIndex) {
        final tradeType = context.read<MarketOrderBloc>().state.tradeType;
        if (tradeType == null) return;
        context.read<CreateTradeBloc>().add(
          CreateTradeEvent.updateMarginRequest(
            orderSize:
                context.read<MarketOrderBloc>().state.orderSizeState.value,
            tradeType: tradeType,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final localization = EquitiLocalization.of(context);

    return BlocConsumer<MarketOrderBloc, MarketOrderState>(
      listenWhen:
          (previous, current) => previous.currentState != current.currentState,
      listener: (listenerContext, state) {
        if (state.currentState is MarketOrderSuccessProcessState) {
          showConfirmationSheet(
            context: context,
            productIconURL: widget.args.symbolImageUrl,
            productName: state.symbolQuoteModel.tickerName,
            assetType: widget.args.assetType,
            lots: state.orderSizeState.value,
            tradeType: state.tradeType!,
            orderPrice: state.openPrice,
            digits: state.symbolQuoteModel.digits,
            title: localization.trader_tradePlaced,
            onNavigate: () {
              context.read<MarketOrderBloc>().add(
                MarketOrderEvent.goToPortfolio(),
              );
            },
          );
        } else if (state.currentState is MarketOrderMarketClosedProcessState) {
          final toast = DuploToast();
          toast.showToastMessage(
            context: context,
            widget: DuploToastMessage(
              titleMessage: localization.trader_marketIsClosed,
              descriptionMessage:
                  localization.trader_openTrade_marketIsClosedDescription,
              messageType: ToastMessageType.error,
              onLeadingAction: () => toast.hidesToastMessage(),
            ),
          );
        } else if (state.currentState
            case MarketOrderErrorProcessState errorState) {
          final toast = DuploToast();
          toast.showToastMessage(
            context: context,
            widget: DuploToastMessage(
              titleMessage: localization.trader_tryAgain,
              descriptionMessage:
                  errorState.errorCode ??
                  localization.trader_somethingWentWrong,
              messageType: ToastMessageType.error,
              onLeadingAction: () => toast.hidesToastMessage(),
            ),
          );
        }
      },
      buildWhen:
          (previous, current) =>
              previous != current && widget.args.tabIndex == 0,
      builder: (builderContext, state) {
        final marginAllocationLoading = switch (context
            .read<CreateTradeBloc>()
            .state
            .marginProcessState) {
          MarginSuccess() || MarginInitial() => false,
          _ => true,
        };
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: SingleChildScrollView(
                physics: ClampingScrollPhysics(),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const SizedBox(height: 16.0),
                    Card(
                      elevation: 0,
                      color: theme.background.bgPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16.0),
                        side: BorderSide(
                          color: theme.border.borderSecondary,
                          width: 1.0,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            InputOrderSizeWidget(
                              key: ValueKey(
                                'input_order_size_widget_${state.tradeType ?? ''}',
                              ),
                              args: (
                                minLot: widget.args.minLot,
                                maxLot: widget.args.maxLot,
                                initialOrderSize: widget.args.minLot,
                                isDisabled:
                                    state.tradeType == null ||
                                    state.marginInformation == null,
                                lotsSteps: widget.args.lotsSteps,
                              ),
                              marginAllocationState:
                                  widget.args.tabIndex == 0
                                      ? state.marginAllocationState
                                      : null,
                              onOrderSizeChanged: (orderSizeState) {
                                builderContext.read<MarketOrderBloc>().add(
                                  MarketOrderEvent.orderSizeChanged(
                                    orderSizeState,
                                  ),
                                );

                                if (widget.args.tabIndex == 0 &&
                                    state.tradeType != null)
                                  builderContext.read<CreateTradeBloc>().add(
                                    CreateTradeEvent.updateMarginRequest(
                                      orderSize: orderSizeState.value,
                                      tradeType: state.tradeType!,
                                    ),
                                  );
                              },
                              showBorder: false,
                            ),
                            const SizedBox(height: 16.0),
                            Stack(
                              children: [
                                InformationWidget(
                                  marginInformation:
                                      widget.args.marginInformation,
                                  accountCurrency:
                                      builderContext
                                          .read<CreateTradeBloc>()
                                          .state
                                          .accountCurrency,
                                  isForex: widget.args.isForex,
                                ),
                                Positioned(
                                  top: 40,
                                  bottom: 20,
                                  left: 20,
                                  right: 20,
                                  child:
                                      marginAllocationLoading
                                          ? GradientLoadingIndicator()
                                          : SizedBox.shrink(),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8.0),
                            OrderLimitWidget(
                              orderLimitType: OrderLimitType.takeProfit,
                              digits: widget.args.symbolQuoteModel.digits,
                              tradeType: state.tradeType ?? TradeType.buy,
                              orderType: OrderType.marketOrder,
                              isDisabled:
                                  state.tradeType == null ||
                                  state.marginInformation == null,
                              pipValue:
                                  state
                                      .marginInformation
                                      ?.pipInformation
                                      .pipValue ??
                                  0.0,
                              pipMultipler:
                                  state
                                      .marginInformation
                                      ?.pipInformation
                                      .pipMultipler ??
                                  0.0,
                              pipSize:
                                  state
                                      .marginInformation
                                      ?.pipInformation
                                      .onePip ??
                                  0.0,
                              currentPrice: state.currentPrice,
                              initialPrice: state.currentPrice,
                              onOrderLimitStateChanged:
                                  (orderLimitState) => builderContext
                                      .read<MarketOrderBloc>()
                                      .add(
                                        MarketOrderEvent.takeProfitChanged(
                                          orderLimitState,
                                        ),
                                      ),
                              methodOrder: [
                                MethodTypeEnum.distance,
                                MethodTypeEnum.price,
                                MethodTypeEnum.profitOrLoss,
                              ],
                            ),
                            const SizedBox(height: 8.0),
                            Divider(color: theme.border.borderSecondary),
                            const SizedBox(height: 8.0),
                            OrderLimitWidget(
                              orderType: OrderType.marketOrder,
                              orderLimitType: OrderLimitType.stopLoss,
                              digits: widget.args.symbolQuoteModel.digits,
                              tradeType: state.tradeType ?? TradeType.buy,
                              isDisabled:
                                  state.tradeType == null ||
                                  state.marginInformation == null,
                              pipValue:
                                  state
                                      .marginInformation
                                      ?.pipInformation
                                      .pipValue ??
                                  0.0,
                              pipMultipler:
                                  state
                                      .marginInformation
                                      ?.pipInformation
                                      .pipMultipler ??
                                  0.0,
                              pipSize:
                                  state
                                      .marginInformation
                                      ?.pipInformation
                                      .onePip ??
                                  0.0,
                              currentPrice: state.currentPrice,
                              initialPrice: state.currentPrice,
                              onOrderLimitStateChanged:
                                  (orderLimitState) => builderContext
                                      .read<MarketOrderBloc>()
                                      .add(
                                        MarketOrderEvent.stopLossChanged(
                                          orderLimitState,
                                        ),
                                      ),
                              methodOrder: [
                                MethodTypeEnum.distance,
                                MethodTypeEnum.price,
                                MethodTypeEnum.profitOrLoss,
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 16.0,
                right: 16.0,
                top: 16.0,
                bottom: MediaQuery.viewInsetsOf(context).bottom * .2 + 16,
              ),
              child: Builder(
                builder: (builderCtx) {
                  // Extract common properties
                  final isPlacing =
                      state.currentState is PlacingMarketOrderProcessState;
                  final isValid = state.isValid();

                  final title =
                      isPlacing
                          ? localization.trader_placingTrade
                          : switch (state.tradeType) {
                            TradeType.buy => localization.trader_buy,
                            TradeType.sell => localization.trader_sell,
                            null =>
                              localization
                                  .trader_sellBuy, // Handle null case with fallback text
                          };

                  final onTap =
                      isValid
                          ? () => builderContext.read<MarketOrderBloc>().add(
                            MarketOrderEvent.submit(),
                          )
                          : () {
                            debugPrint(
                              "submit ${state.tradeType?.name ?? 'unknown'}",
                            );
                          };

                  final isDisabled = !isValid || isPlacing;

                  // Return appropriate button based on trade type
                  return switch (state.tradeType) {
                    TradeType.buy => DuploButton.buyPrimary(
                      title: title,
                      isLoading: isPlacing,
                      loadingText: localization.trader_placingTrade,
                      onTap: onTap,
                      isDisabled: isDisabled,
                      useFullWidth: true,
                    ),
                    TradeType.sell => DuploButton.sellPrimary(
                      title: title,
                      isLoading: isPlacing,
                      loadingText: localization.trader_placingTrade,
                      onTap: onTap,
                      isDisabled: isDisabled,
                      useFullWidth: true,
                    ),
                    null => DuploButton.defaultPrimary(
                      title: title,
                      isLoading: isPlacing,
                      loadingText: localization.trader_placingTrade,
                      onTap: onTap,
                      isDisabled: isDisabled,
                      useFullWidth: true,
                    ),
                  };
                },
              ),
            ),
          ],
        );
      },
    );
  }
}
