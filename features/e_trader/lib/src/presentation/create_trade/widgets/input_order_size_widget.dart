import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/formatter/decimal_text_input_formatter.dart';
import 'package:e_trader/src/domain/validators/trade_error.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_size/input_order_size_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_size/order_size_error_code.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/trade_component_state.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

typedef OrderSizeArgs =
    ({
      double minLot,
      double maxLot,
      double initialOrderSize,
      bool isDisabled,
      double lotsSteps,
    });

class InputOrderSizeWidget extends StatelessWidget {
  const InputOrderSizeWidget({
    super.key,
    required this.args,
    required this.onOrderSizeChanged,
    this.showBorder = true,
    this.options = const [],
    this.footer,
    this.marginAllocationState,
  });
  final List<String> options;
  final OrderSizeArgs args;
  final void Function(TradeComponentState<double, OrderSizeErrorCode> state)
  onOrderSizeChanged;
  final bool showBorder;
  final Widget? footer;
  final TradeComponentState<void, TradeError>? marginAllocationState;

  @override
  Widget build(BuildContext buildContext) {
    return BlocProvider(
      create:
          (context) =>
              args.isDisabled
                  ? InputOrderSizeBloc(args: args)
                  : diContainer<InputOrderSizeBloc>(param1: args),
      child: _InputOrderSizeContent(
        args: args,
        options: options,
        showBorder: showBorder,
        onOrderSizeChanged: onOrderSizeChanged,
        footer: footer,
        marginAllocationState: marginAllocationState,
        initLocale: Localizations.localeOf(buildContext).toString(),
      ),
    );
  }
}

class _InputOrderSizeContent extends StatefulWidget {
  const _InputOrderSizeContent({
    required this.args,
    required this.onOrderSizeChanged,
    required this.showBorder,
    this.options = const [],
    this.footer,
    required this.initLocale,
    this.marginAllocationState,
  });

  final OrderSizeArgs args;
  final List<String> options;
  final bool showBorder;
  final void Function(TradeComponentState<double, OrderSizeErrorCode> state)
  onOrderSizeChanged;
  final Widget? footer;
  final String initLocale;
  final TradeComponentState<void, TradeError>? marginAllocationState;

  @override
  State<_InputOrderSizeContent> createState() => _InputOrderSizeContentState();
}

class _InputOrderSizeContentState extends State<_InputOrderSizeContent> {
  late final TextEditingController _inputController;

  @override
  void initState() {
    super.initState();
    _inputController = TextEditingController();
    _inputController.text = EquitiFormatter.formatNumber(
      value: widget.args.initialOrderSize,
      locale: widget.initLocale,
    );
  }

  @override
  void didUpdateWidget(covariant _InputOrderSizeContent oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.args != widget.args) {
      final minLotStr = EquitiFormatter.formatNumber(
        value: widget.args.initialOrderSize,
        locale: Localizations.localeOf(context).toString(),
      );
      ;

      context.read<InputOrderSizeBloc>().add(
        InputOrderSizeEvent.updateOrderSize(
          args: widget.args,
          orderSize: minLotStr,
        ),
      );
    }
  }

  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }

  void _updateInputValue(
    TradeComponentState<double, OrderSizeErrorCode> state,
  ) {
    final value = EquitiFormatter.formatNumber(
      value: state.value,
      locale: Localizations.localeOf(context).toString(),
    );
    _inputController.text = value;
    _inputController.selection = TextSelection.collapsed(offset: value.length);
  }

  int _getLotDecimals(double number) {
    final str = EquitiFormatter.formatToString(value: number);
    final index = str.indexOf('.');
    if (index == -1) return 0;
    var decimals = str.substring(index + 1).replaceFirst(RegExp(r'0+$'), '');
    return decimals.length;
  }

  @override
  Widget build(BuildContext buildContext) => MultiBlocListener(
    listeners: [
      BlocListener<
        InputOrderSizeBloc,
        TradeComponentState<double, OrderSizeErrorCode>
      >(listener: (context, state) => widget.onOrderSizeChanged(state)),
      BlocListener<
        InputOrderSizeBloc,
        TradeComponentState<double, OrderSizeErrorCode>
      >(
        listenWhen:
            (previous, current) => previous != current && current.isValid(),
        listener: (context, state) => _updateInputValue(state),
      ),
    ],
    child: BlocBuilder<
      InputOrderSizeBloc,
      TradeComponentState<double, OrderSizeErrorCode>
    >(
      buildWhen: (previous, current) => previous != current,
      builder: (context, state) {
        final localization = EquitiLocalization.of(buildContext);

        String? errorText;
        if (!state.isValid()) {
          errorText = localization.trader_pleaseSpecifyTheLotSize;
        } else if (!(widget.marginAllocationState?.isValid() ?? false)) {
          errorText =
              widget.marginAllocationState == null
                  ? null
                  : switch (widget.marginAllocationState!) {
                    TradeComponentErrorState() =>
                      localization.trader_marginAllocationNotValid,
                    _ => null,
                  };
        }

        return StepperControlWidget(
          title: localization.trader_lotSize,
          segmentControWidget:
              widget.options.isNotEmpty
                  ? HighlightOptionBoxWidget(
                    selectedIndex: 0,
                    options: widget.options,
                    onSelectionChange:
                        (value, index) =>
                            context.read<InputOrderSizeBloc>().add(
                              InputOrderSizeEvent.updateOrderSize(
                                args: widget.args,
                                orderSize: value,
                              ),
                            ),
                  )
                  : null,
          trailingWidget: DuploTap(
            onTap: () {
              final String description =
                  EquitiLocalization.of(context).trader_lotsizeInfo;
              DuploDialog.showInfoDialog(
                context: context,
                title: localization.trader_lotSize,
                description: description,
              );
            },

            child: Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 0, 8),
              child: Assets.images.help.svg(width: 12, height: 12),
            ),
          ),
          inputWidget: StepperNumberInputWidget(
            controller: _inputController,
            prescisionFactor: _getLotDecimals(widget.args.minLot),
            minimumValue: widget.args.minLot,
            hintText: localization.trader_enterAnumber,
            errorText: errorText,
            enabled: !widget.args.isDisabled,
            textInputFormatter: DecimalTextInputFormatter(
              decimalRange: _getLotDecimals(widget.args.minLot),
            ),
            onValueChange:
                (value) => context.read<InputOrderSizeBloc>().add(
                  InputOrderSizeEvent.updateOrderSize(
                    args: widget.args,
                    orderSize: value,
                  ),
                ),
            changeFactor: widget.args.lotsSteps,
          ),
          footerWidget:
              widget.footer ??
              OrderLimitFooterWidget(
                firstPair: KeyValuePair(
                  label: localization.trader_minSize,
                  value: EquitiFormatter.formatNumber(
                    value: widget.args.minLot,
                    locale: Localizations.localeOf(context).toString(),
                  ),
                ),
                secondPair: KeyValuePair(
                  label: localization.trader_maxSize,
                  value: EquitiFormatter.formatNumber(
                    value: widget.args.maxLot,
                    locale: Localizations.localeOf(context).toString(),
                  ),
                ),
              ),
          bordered: widget.showBorder,
        );
      },
    ),
  );
}
