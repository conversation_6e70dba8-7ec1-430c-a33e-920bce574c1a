import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/order_limit_type.dart';
import 'package:e_trader/src/domain/model/order_type.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/create_order/create_trade_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/pending_order/pending_order_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/information_widget.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/input_order_size_widget.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/order_limit_widget.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/order_price_widget.dart';
import 'package:e_trader/src/presentation/duplo/confirmation_sheet.dart';
import 'package:e_trader/src/presentation/model/margin_information_model.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

typedef PendingOrderArgs =
    ({
      TradeType? tradeType,
      String assetType,
      String accountNumber,
      MarginInformationModel? marginInformation,
      SymbolQuoteModel symbolQuoteModel,
      double minLot,
      double maxLot,
      String symbolImageUrl,
      int tabIndex,
      bool isForex,
      double lotsSteps,
    });

class PendingOrderWidget extends StatelessWidget {
  const PendingOrderWidget({required this.args, super.key});

  final PendingOrderArgs args;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (createContext) => diContainer<PendingOrderBloc>(param1: args),
      child: _PendingOrderContent(args),
    );
  }
}

class _PendingOrderContent extends StatefulWidget {
  const _PendingOrderContent(this.args);

  final PendingOrderArgs args;

  @override
  State<_PendingOrderContent> createState() => __PendingOrderContentState();
}

class __PendingOrderContentState extends State<_PendingOrderContent> {
  @override
  void didUpdateWidget(covariant _PendingOrderContent oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget != widget && widget.args.tabIndex == 1) {
      context.read<PendingOrderBloc>().add(
        PendingOrderEvent.valuesChanged(widget.args),
      );
      if (oldWidget.args.tabIndex != widget.args.tabIndex &&
          widget.args.tabIndex == 1) {
        final tradeType = context.read<PendingOrderBloc>().state.tradeType;
        if (tradeType == null) return;
        context.read<CreateTradeBloc>().add(
          CreateTradeEvent.updateMarginRequest(
            orderSize:
                context.read<PendingOrderBloc>().state.orderSizeState.value,
            tradeType: tradeType,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final localization = EquitiLocalization.of(context);
    late bool marginAllocationLoading;

    return BlocConsumer<PendingOrderBloc, PendingOrderState>(
      listenWhen:
          (previous, current) => previous.currentState != current.currentState,
      buildWhen: (previous, current) => previous != current,
      listener: (listenerContext, state) {
        return switch (state.currentState) {
          PendingOrderMarketClosedProcessState() => () {
            final toast = DuploToast();
            return toast.showToastMessage(
              context: context,
              widget: DuploToastMessage(
                titleMessage: localization.trader_marketIsClosed,
                descriptionMessage:
                    localization
                        .trader_createPendingOrder_marketIsClosedDescription,
                messageType: ToastMessageType.error,
                onLeadingAction: () => toast.hidesToastMessage(),
              ),
            );
          }(),
          PendingOrderSuccessProcessState() => () {
            return showConfirmationSheet(
              context: context,
              productIconURL: widget.args.symbolImageUrl,
              productName: state.symbolQuoteModel.tickerName,
              assetType: widget.args.assetType,
              lots: state.orderSizeState.value,
              tradeType: state.tradeType!,
              orderPrice: state.orderPrice,
              digits: state.symbolQuoteModel.digits,
              title: localization.trader_orderPlaced,
              clickableText: localization.trader_viewAllOrders,
              sellSvg: trader.Assets.images.sellOrder.svg(),
              buySvg: trader.Assets.images.buyOrder.svg(),
              onNavigate:
                  () => listenerContext.read<PendingOrderBloc>().add(
                    PendingOrderEvent.goToPortfolio(),
                  ),
            );
          }(),
          PendingOrderErrorProcessState(:final errorMessage) => () {
            final toast = DuploToast();
            return toast.showToastMessage(
              context: context,
              widget: DuploToastMessage(
                titleMessage: localization.trader_marketIsClosed,
                descriptionMessage:
                    errorMessage ?? localization.trader_somethingWentWrong,
                messageType: ToastMessageType.error,
                onLeadingAction: () => toast.hidesToastMessage(),
              ),
            );
          },
          _ => null,
        };
      },
      builder: (builderContext, state) {
        marginAllocationLoading = switch (context
            .read<CreateTradeBloc>()
            .state
            .marginProcessState) {
          MarginSuccess() || MarginInitial() => false,
          _ => true,
        };
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: SingleChildScrollView(
                physics: ClampingScrollPhysics(),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const SizedBox(height: 16.0),
                    Card(
                      elevation: 0,
                      color: theme.background.bgPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16.0),
                        side: BorderSide(
                          color: theme.border.borderSecondary,
                          width: 1.0,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            InputOrderSizeWidget(
                              args: (
                                minLot: widget.args.minLot,
                                maxLot: widget.args.maxLot,
                                initialOrderSize: widget.args.minLot,
                                isDisabled:
                                    state.tradeType == null ||
                                    state.marginInformation == null,
                                lotsSteps: widget.args.lotsSteps,
                              ),
                              marginAllocationState:
                                  widget.args.tabIndex == 1
                                      ? state.marginAllocationState
                                      : null,
                              onOrderSizeChanged: (orderSizeState) {
                                builderContext.read<PendingOrderBloc>().add(
                                  PendingOrderEvent.orderSizeChanged(
                                    orderSizeState,
                                  ),
                                );

                                if (widget.args.tabIndex == 1 &&
                                    state.tradeType != null) {
                                  builderContext.read<CreateTradeBloc>().add(
                                    CreateTradeEvent.updateMarginRequest(
                                      orderSize: orderSizeState.value,
                                      tradeType: state.tradeType!,
                                    ),
                                  );
                                }
                              },
                              showBorder: false,
                            ),
                            const SizedBox(height: 16.0),
                            OrderPriceWidget(
                              key: ValueKey(
                                'order_price_${state.tradeType?.name ?? 'null'}',
                              ),
                              args: (
                                initialPrice: state.currentPrice,
                                currentPrice: state.currentPrice,
                                digits: widget.args.symbolQuoteModel.digits,
                                isDisabled:
                                    state.marginInformation == null ||
                                    state.tradeType == null,
                              ),
                              onOrderPriceChanged:
                                  (orderPriceState) => builderContext
                                      .read<PendingOrderBloc>()
                                      .add(
                                        PendingOrderEvent.priceChanged(
                                          orderPriceState,
                                        ),
                                      ),
                              tradeType: state.tradeType,
                            ),
                            const SizedBox(height: 16.0),
                            Stack(
                              children: [
                                InformationWidget(
                                  marginInformation:
                                      widget.args.marginInformation,
                                  accountCurrency:
                                      builderContext
                                          .read<CreateTradeBloc>()
                                          .state
                                          .accountCurrency,
                                  isForex: widget.args.isForex,
                                ),
                                Positioned(
                                  top: 40,
                                  bottom: 20,
                                  left: 20,
                                  right: 20,
                                  child:
                                      marginAllocationLoading
                                          ? GradientLoadingIndicator()
                                          : SizedBox.shrink(),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16.0),
                            OrderLimitWidget(
                              orderType: OrderType.pendingOrder,
                              orderLimitType: OrderLimitType.takeProfit,
                              digits: widget.args.symbolQuoteModel.digits,
                              tradeType: state.tradeType ?? TradeType.buy,
                              isDisabled:
                                  state.tradeType == null ||
                                  state.marginInformation == null,
                              pipValue:
                                  state
                                      .marginInformation
                                      ?.pipInformation
                                      .pipValue ??
                                  0.0,
                              pipMultipler:
                                  state
                                      .marginInformation
                                      ?.pipInformation
                                      .pipMultipler ??
                                  0.0,
                              pipSize:
                                  state
                                      .marginInformation
                                      ?.pipInformation
                                      .onePip ??
                                  0.0,
                              currentPrice:
                                  (state.orderPriceState.isValid())
                                      ? state.orderPriceState.value
                                      : 0.0,
                              initialPrice:
                                  (state.orderPriceState.isValid())
                                      ? state.orderPriceState.value
                                      : 0.0,
                              methodOrder: [
                                MethodTypeEnum.distance,
                                MethodTypeEnum.price,
                                MethodTypeEnum.profitOrLoss,
                              ],
                              onOrderLimitStateChanged:
                                  (orderLimitState) => builderContext
                                      .read<PendingOrderBloc>()
                                      .add(
                                        PendingOrderEvent.takeProfitChanged(
                                          orderLimitState,
                                        ),
                                      ),
                            ),
                            const SizedBox(height: 8.0),
                            Divider(color: theme.border.borderSecondary),
                            const SizedBox(height: 8.0),
                            OrderLimitWidget(
                              orderType: OrderType.pendingOrder,
                              orderLimitType: OrderLimitType.stopLoss,
                              digits: widget.args.symbolQuoteModel.digits,
                              tradeType: state.tradeType ?? TradeType.buy,
                              isDisabled:
                                  state.tradeType == null ||
                                  state.marginInformation == null,
                              pipValue:
                                  state
                                      .marginInformation
                                      ?.pipInformation
                                      .pipValue ??
                                  0.0,
                              pipMultipler:
                                  state
                                      .marginInformation
                                      ?.pipInformation
                                      .pipMultipler ??
                                  0.0,
                              pipSize:
                                  state
                                      .marginInformation
                                      ?.pipInformation
                                      .onePip ??
                                  0.0,
                              currentPrice:
                                  (state.orderPriceState.isValid())
                                      ? state.orderPriceState.value
                                      : 0.0,
                              initialPrice:
                                  (state.orderPriceState.isValid())
                                      ? state.orderPriceState.value
                                      : 0.0,
                              methodOrder: [
                                MethodTypeEnum.distance,
                                MethodTypeEnum.price,
                                MethodTypeEnum.profitOrLoss,
                              ],
                              onOrderLimitStateChanged:
                                  (orderLimitState) => builderContext
                                      .read<PendingOrderBloc>()
                                      .add(
                                        PendingOrderEvent.stopLossChanged(
                                          orderLimitState,
                                        ),
                                      ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 16.0,
                right: 16.0,
                top: 16.0,
                bottom: MediaQuery.viewInsetsOf(context).bottom * .2 + 16,
              ),
              child: Builder(
                builder: (builderCtx) {
                  // Extract common properties
                  final isPlacing =
                      state.currentState is PendingOrderPlacingProcessState;
                  final isValid = state.isValid();

                  final title =
                      isPlacing
                          ? localization.trader_placingTrade
                          : switch (state.tradeType) {
                            TradeType.buy => localization.trader_buy,
                            TradeType.sell => localization.trader_sell,
                            null =>
                              localization
                                  .trader_sellBuy, // Handle null case with fallback text
                          };

                  final onTap =
                      isValid
                          ? () => builderContext.read<PendingOrderBloc>().add(
                            PendingOrderEvent.submit(),
                          )
                          : () {
                            debugPrint(
                              "submit ${state.tradeType?.name ?? 'unknown'}",
                            );
                          };

                  final isDisabled = !isValid || isPlacing;

                  // Return appropriate button based on trade type
                  return switch (state.tradeType) {
                    TradeType.buy => DuploButton.buyPrimary(
                      title: title,
                      isLoading: isPlacing,
                      loadingText: localization.trader_placingTrade,
                      onTap: onTap,
                      isDisabled: isDisabled,
                      useFullWidth: true,
                    ),
                    TradeType.sell => DuploButton.sellPrimary(
                      title: title,
                      isLoading: isPlacing,
                      loadingText: localization.trader_placingTrade,
                      onTap: onTap,
                      isDisabled: isDisabled,
                      useFullWidth: true,
                    ),
                    null => DuploButton.defaultPrimary(
                      title: title,
                      isLoading: isPlacing,
                      loadingText: localization.trader_placingTrade,
                      onTap: onTap,
                      isDisabled: isDisabled,
                      useFullWidth: true,
                    ),
                  };
                },
              ),
            ),
          ],
        );
      },
    );
  }
}
