import 'dart:async';
import 'dart:collection';

import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/data/socket/position_response.dart';
import 'package:e_trader/src/domain/analytics/trading_analytics.dart';
import 'package:e_trader/src/domain/model/grouped_positions.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_positions_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_positions_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

part 'position_bloc.freezed.dart';
part 'position_event.dart';
part 'position_state.dart';

class PositionBloc extends Bloc<PositionEvent, PositionState>
    with DisposableMixin {
  final SubscribeToPositionsUseCase _subscribeToPositionsUseCase;
  final UpdatePositionsUseCase _updatePositionsUseCase;
  final LoggerBase _logger;
  final _positionsById = <String, PositionResponse>{};
  final GetSelectedAccountUseCase _getSelectedAccountUseCase;
  final String _platformName;
  final TradingAnalytics _tradingAnalyticsEvent;

  PositionBloc(
    SubscribeToPositionsUseCase subscribeToPositionsUseCase,
    UpdatePositionsUseCase updatePositionsUseCase,
    GetSelectedAccountUseCase getSelectedAccountUseCase,
    this._logger,
    this._platformName,
    this._tradingAnalyticsEvent,
  ) : _subscribeToPositionsUseCase = subscribeToPositionsUseCase,
      _updatePositionsUseCase = updatePositionsUseCase,
      _getSelectedAccountUseCase = getSelectedAccountUseCase,
      super(
        PositionState(
          positions: SplayTreeMap(
            (a, b) => _comparePositions(a, b, <String, PositionResponse>{}),
          ),
          groupedPositions: {},
        ),
      ) {
    on<PositionEvent>((event, emit) async {
      if (state.selectedAccountCurrency == null) {
        final accountCurrency =
            _getSelectedAccountUseCase()?.homeCurrency ?? "USD";
        emit(state.copyWith(selectedAccountCurrency: accountCurrency));
      }

      if (event is _LoadPositions) {
        await _onLoadPositions(emit);
      } else if (event is _UpdatePositions) {
        _updatePositions(event.eventType);
      }
    }, transformer: droppable());

    on<_ProcessPositions>(
      (event, emit) => _processPositions(event, emit),
      transformer: droppable(),
    );
    on<_EmitErrors>(
      (event, emit) => _processError(emit),
      transformer: droppable(),
    );
  }

  // Helper function to compare positions by open time (newest first)
  static int _comparePositions(
    String a,
    String b,
    Map<String, PositionResponse> positionsById,
  ) {
    final positionA = positionsById[a]?.position;
    final positionB = positionsById[b]?.position;

    if (positionA == null || positionB == null) return 0;
    return positionB.positionId.compareTo(
      positionA.positionId,
    ); // Descending order
  }

  FutureOr<void> _onLoadPositions(Emitter<PositionState> emit) async {
    emit(state.copyWith(processState: PositionProcessState.loading()));

    final result =
        await _subscribeToPositionsUseCase(
          subscriberId: '${PositionBloc}_$hashCode',
          eventType: TradingSocketEvent.positions.register,
          symbolName: _platformName,
        ).run();

    result.fold(
      (error) {
        addError(error);
        emit(state.copyWith(processState: PositionProcessState.error()));
      },
      (stream) {
        emit(state.copyWith(processState: PositionProcessState.connected()));
        addSubscription(
          stream.listen(
            (positionResponse) =>
                add(PositionEvent.processPositions(positionResponse)),
            onError: (Object? error) {
              add(PositionEvent.emitErrors());
            },
          ),
        );
      },
    );
  }

  @override
  void addError(Object error, [StackTrace? stackTrace]) {
    super.addError(error, stackTrace);
    _logger.logError(error, stackTrace: stackTrace);
  }

  void _processPositions(_ProcessPositions event, Emitter<PositionState> emit) {
    final positionResponse = event.positionResponse;

    if (positionResponse == null) {
      emit(
        state.copyWith(
          groupedPositions: {},
          processState: PositionProcessState.empty(),
        ),
      );
      return;
    }

    //added analytics event
    if (positionResponse.target == null ||
        positionResponse.target == "PositionAdded") {
      _tradingAnalyticsEvent.positionAdded(
        positionResponse.position.positionId,
        positionResponse.position.platformName,
      );
    }

    // Handle position deletion FIRST before updating tracking map
    if (positionResponse.target == 'PositionDeleted') {
      final platformName = positionResponse.position.platformName;
      final positionId = positionResponse.position.positionId;

      _tradingAnalyticsEvent.positionDeleted(positionId, platformName);
      // Remove from tracking map
      _positionsById.remove(positionId);

      // Remove from grouped positions
      if (state.groupedPositions.containsKey(platformName)) {
        final groupedPosition = state.groupedPositions[platformName]!;
        final updatedPositions =
            groupedPosition.positions
                .where((p) => p.positionId != positionId)
                .toList();

        if (updatedPositions.isEmpty) {
          // If no positions left, remove the symbol entry
          state.groupedPositions.remove(platformName);
        } else {
          // Otherwise update with the filtered positions
          state.groupedPositions[platformName] = groupedPosition.copyWith(
            positions: updatedPositions,
          );
        }
      }

      emit(
        state.copyWith(
          processState: PositionProcessState.success(),
          positions: SplayTreeMap<String, PositionModel>(
            (a, b) => _comparePositions(a, b, _positionsById),
          )..addAll(
            Map.fromEntries(
              _positionsById.values.map(
                (r) => MapEntry(r.position.positionId, r.position),
              ),
            ),
          ),
        ),
      );
      return;
    }

    // Add/update position in our tracking map (only for non-deleted positions)
    _positionsById[positionResponse.position.positionId] = positionResponse;

    if (state.groupedPositions.isEmpty) {
      // Initial load - process all positions at once for efficiency
      final groupedMap = _positionsById.values
          .where((response) => response.target != 'PositionDeleted')
          .map((response) => response.position)
          .groupBy((p) => p.platformName);

      final newGroupedMap = groupedMap.map<String, GroupedPositions>((
        key,
        value,
      ) {
        // Sort positions by open time (newest first)
        final sortedPositions =
            value.toList()
              ..sort((a, b) => b.positionId.compareTo(a.positionId));

        return MapEntry(
          key,
          GroupedPositions(
            platformName: key,
            tickerName: value.firstOrNull?.tickerName ?? '',
            url: value.firstOrNull?.productLogoUrl ?? '',
            positions: sortedPositions,
          ),
        );
      });

      emit(
        state.copyWith(
          groupedPositions: newGroupedMap,
          processState: PositionProcessState.success(),
          positions: SplayTreeMap<String, PositionModel>(
            (a, b) => _comparePositions(a, b, _positionsById),
          )..addAll(
            Map.fromEntries(
              _positionsById.values.map(
                (r) => MapEntry(r.position.positionId, r.position),
              ),
            ),
          ),
        ),
      );
      return;
    }

    // Update the existing map directly for subsequent updates
    final symbol = positionResponse.position.platformName;

    // Handle position addition/update
    final position = positionResponse.position;

    if (state.groupedPositions.containsKey(symbol)) {
      // Update existing symbol entry
      final groupedPosition = state.groupedPositions[symbol]!;
      final existingPositionIndex = groupedPosition.positions.indexWhere(
        (p) => p.positionId == position.positionId,
      );

      final List<PositionModel> updatedPositions;
      if (existingPositionIndex >= 0) {
        // Replace existing position
        updatedPositions = List.of(groupedPosition.positions);
        updatedPositions[existingPositionIndex] = position;
      } else {
        // Add new position to existing symbol
        updatedPositions = [...groupedPosition.positions, position];
      }

      // Sort positions by open time (newest first)
      updatedPositions.sort((a, b) => b.positionId.compareTo(a.positionId));

      state.groupedPositions[symbol] = groupedPosition.copyWith(
        positions: updatedPositions,
      );
    } else {
      state.groupedPositions[symbol] = GroupedPositions(
        platformName: symbol,
        tickerName: position.tickerName,
        url: position.productLogoUrl,
        positions: [position],
      );
    }
    _positionsById[position.positionId] = positionResponse;

    emit(
      state.copyWith(
        processState: PositionProcessState.success(),
        positions: SplayTreeMap<String, PositionModel>(
          (a, b) => _comparePositions(a, b, _positionsById),
        )..addAll(
          Map.fromEntries(
            _positionsById.values.map(
              (r) => MapEntry(r.position.positionId, r.position),
            ),
          ),
        ),
      ),
    );
  }

  FutureOr<void> _processError(Emitter<PositionState> emit) {
    emit(state.copyWith(processState: PositionProcessState.error()));
  }

  void _updatePositions(EventType eventType) =>
      _updatePositionsUseCase(eventType: eventType, symbolName: _platformName);
}
