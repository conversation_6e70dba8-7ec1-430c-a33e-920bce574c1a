import 'dart:async';
import 'dart:collection';

import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/data/socket/order_model.dart';
import 'package:e_trader/src/data/socket/order_response.dart';
import 'package:e_trader/src/domain/analytics/trading_analytics.dart';
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_orders_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_order_hub_use_case.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';
import 'package:socket_client/socket_client.dart';

part 'orders_bloc.freezed.dart';
part 'orders_event.dart';
part 'orders_state.dart';

class OrdersBloc extends Bloc<OrdersEvent, OrdersState> with DisposableMixin {
  final SubscribeToOrdersUseCase _subscribeToOrdersUseCase;
  final UpdateOrderHubUseCase _updateOrderHubUseCase;
  final GetAccountNumberUseCase _getAccountNumberUseCase;
  final String _platformName;
  final TradingAnalytics _tradingAnalyticsEvent;
  final _ordersById = <String, OrderResponse>{};

  OrdersBloc(
    this._subscribeToOrdersUseCase,
    this._updateOrderHubUseCase,
    this._getAccountNumberUseCase,
    this._platformName,
    this._tradingAnalyticsEvent,
  ) : super(
        OrdersState(
          processState: OrdersProcessState.loading(),
          orders: SplayTreeMap(
            (a, b) => _compareOrders(a, b, <String, OrderResponse>{}),
          ),
        ),
      ) {
    on<OrdersEvent>((event, emit) {
      if (event is _LoadOrders) {
        return _onLoadOrders(emit);
      } else if (event is _UpdateOrders) {
        return _onUpdateOrders(event.eventType);
      }
    }, transformer: droppable());
    on<_ProcessOrders>((event, emit) {
      _processOrders(event.orderResponse, emit);
    }, transformer: droppable());
    on<_ProcessErrors>((event, emit) {
      _processErrors(emit);
    }, transformer: droppable());
  }

  // Helper function to compare orders by time (newest first)
  static int _compareOrders(
    String a,
    String b,
    Map<String, OrderResponse> ordersById,
  ) {
    final orderA = ordersById[a]?.order;
    final orderB = ordersById[b]?.order;

    if (orderA == null || orderB == null) return 0;
    return orderB.id.compareTo(orderA.id); // Descending order
  }

  FutureOr<void> _onLoadOrders(Emitter<OrdersState> emit) async {
    emit(state.copyWith(processState: OrdersProcessState.loading()));

    final result =
        await TaskEither<Exception, Stream<OrderResponse?>>.Do(($) async {
          final accountNumber = await $(
            _getAccountNumberUseCase().toTaskEither(),
          );
          final ordersResult = await $(
            _subscribeToOrdersUseCase(
              accountNumber: accountNumber,
              eventType: TradingSocketEvent.orders.register,
              subscriberId: '${OrdersBloc}_$hashCode',
              symbolName: _platformName,
            ),
          );
          return ordersResult;
        }).run();
    result.fold(
      (error) {
        addError(error);
        emit(state.copyWith(processState: OrdersProcessState.error()));
      },
      (stream) {
        emit(state.copyWith(processState: OrdersProcessState.connected()));
        addSubscription(
          stream.listen(
            (data) => add(OrdersEvent.processOrders(data)),
            onError: (Object error) {
              addError(error);
              add(OrdersEvent.processErrors());
            },
          ),
        );
      },
    );
  }

  FutureOr<void> _onUpdateOrders(EventType eventType) {
    _updateOrderHubUseCase(eventType: eventType, symbolName: _platformName);
  }

  void _processOrders(OrderResponse? orderResponse, Emitter<OrdersState> emit) {
    final data = orderResponse;
    if (data == null) {
      emit(
        state.copyWith(
          processState: OrdersProcessState.success(),
          orders: SplayTreeMap(),
        ),
      );
      return;
    }
    //added analytics event
    if (data.target == 'OrderAdded' || data.target == null) {
      _tradingAnalyticsEvent.orderAdded(
        data.order.id.toString(),
        _platformName,
      );
    }

    if (data.target == 'OrderDeleted') {
      _tradingAnalyticsEvent.orderDeleted(
        data.order.id.toString(),
        _platformName,
      );
      _ordersById.remove(data.order.id.toString());
    } else {
      _ordersById[data.order.id.toString()] = data;
    }

    emit(
      state.copyWith(
        processState: OrdersProcessState.success(),
        orders: SplayTreeMap<String, OrderModel>(
          (a, b) => _compareOrders(a, b, _ordersById),
        )..addAll(
          Map.fromEntries(
            _ordersById.values.map(
              (r) => MapEntry(r.order.id.toString(), r.order),
            ),
          ),
        ),
      ),
    );
  }

  void _processErrors(Emitter<OrdersState> emit) {
    emit(state.copyWith(processState: OrdersProcessState.error()));
  }
}
