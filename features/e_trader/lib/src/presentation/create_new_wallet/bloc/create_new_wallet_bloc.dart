import 'package:broker_settings/broker_settings.dart';
import 'package:e_trader/src/domain/model/create_new_wallet_data.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:e_trader/src/domain/usecase/create_wallet_use_case.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'create_new_wallet_event.dart';
part 'create_new_wallet_state.dart';
part 'create_new_wallet_bloc.freezed.dart';

class CreateNewWalletBloc
    extends Bloc<CreateNewWalletEvent, CreateNewWalletState> {
  final CreateWalletUseCase _createWalletUseCase;
  final GetBrokerCurrenciesUseCase _getBrokerCurrenciesUseCase;
  CreateNewWalletBloc({
    required CreateWalletUseCase createWalletUseCase,
    required GetBrokerCurrenciesUseCase getBrokerCurrenciesUseCase,
  }) : _createWalletUseCase = createWalletUseCase,
       _getBrokerCurrenciesUseCase = getBrokerCurrenciesUseCase,
       super(Loading()) {
    on<_FetchBrokerSettings>(_onFetchBrokerSettings);
    on<_CreateWallet>(_onCreateWallet);
    on<_CurrencySelected>(_onCurrencySelected);
  }

  void _onCreateWallet(
    _CreateWallet event,
    Emitter<CreateNewWalletState> emit,
  ) async {
    switch (state) {
      case BrokerSettingsLoaded(:var data) || WalletCreationFailed(:var data):
        emit(Loading());
        final selectedCurrency = data.allCurrencies.elementAtOrNull(
          data.selectedCurrencyIndex,
        );
        if (selectedCurrency == null) {
          if (!isClosed) {
            emit(WalletCreationFailed(data));
          }
          return;
        }
        final result =
            await _createWalletUseCase
                .createWallet(currency: selectedCurrency.code)
                .run();
        result.fold(
          (error) {
            print("Here 7");
            if (!isClosed) {
              print("Here 8");
              emit(WalletCreationFailed(data));
            }
            print("Here 9 $error");
            addError(error);
          },
          (response) {
            print("Here 10");
            if (!isClosed) emit(WalletCreated(data));
          },
        );
      case _:
        break;
    }
    return null;
  }

  Future<void> _onFetchBrokerSettings(
    _FetchBrokerSettings event,
    Emitter<CreateNewWalletState> emit,
  ) async {
    emit(Loading());
    final result =
        await _getBrokerCurrenciesUseCase(
          brokerSettingsType: BrokerSettingsType.additionalAccount,
        ).run();
    result.fold(
      (error) {
        if (!isClosed) {
          emit(BrokerSettingsFailed());
        }
        addError(error);
      },
      (data) {
        final createNewWalletData = CreateNewWalletData(
          allCurrencies: data!,
          selectedCurrencyIndex: 0,
        );
        if (!isClosed) emit(BrokerSettingsLoaded(createNewWalletData));
      },
    );
  }

  void _onCurrencySelected(
    _CurrencySelected event,
    Emitter<CreateNewWalletState> emit,
  ) {
    switch (state) {
      case BrokerSettingsLoaded(:var data):
        final newData = data.copyWith(selectedCurrencyIndex: event.index);
        emit(BrokerSettingsLoaded(newData));
      case _:
        break;
    }
  }
}
