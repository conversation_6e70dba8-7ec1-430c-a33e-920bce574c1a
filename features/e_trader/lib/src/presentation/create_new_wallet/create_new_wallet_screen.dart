import 'dart:io';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/presentation/create_new_wallet/bloc/create_new_wallet_bloc.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CreateNewWalletScreen extends StatelessWidget {
  const CreateNewWalletScreen();
  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);

    return BlocProvider(
      create:
          (createContext) =>
              diContainer<CreateNewWalletBloc>()
                ..add(CreateNewWalletEvent.fetchBrokerSettings()),
      child: BlocConsumer<CreateNewWalletBloc, CreateNewWalletState>(
        buildWhen: (previous, current) => previous != current,
        listenWhen: (previous, current) => previous != current,
        listener: (listenerContext, state) {
          if (state is WalletCreated) {
            if (!Platform.environment.containsKey('FLUTTER_TEST')) {
              final toast = DuploToast();
              toast.showToastMessage(
                context: listenerContext,
                widget: DuploToastMessage(
                  titleMessage: localization.trader_walletCreatedSuccessfully,
                  descriptionMessage:
                      localization.trader_walletCreatedDescription,
                  messageType: ToastMessageType.success,
                  onLeadingAction: () => toast.hidesToastMessage(),
                ),
              );
            }

            Navigator.of(listenerContext).pop(true);
          } else if (state is WalletCreationFailed) {
            if (!Platform.environment.containsKey('FLUTTER_TEST')) {
              final toast = DuploToast();
              toast.showToastMessage(
                context: listenerContext,
                widget: DuploToastMessage(
                  titleMessage: localization.trader_walletCreationFailed,
                  descriptionMessage:
                      localization.trader_walletCreationFailedDescription,
                  messageType: ToastMessageType.error,
                  onLeadingAction: () => toast.hidesToastMessage(),
                  actionButtonTitle: localization.trader_tryAgain,
                  onTap: () {
                    toast.hidesToastMessage();
                  },
                ),
              );
            }
          }
        },
        builder: (builder1Context, state1) {
          return BlocBuilder<CreateNewWalletBloc, CreateNewWalletState>(
            buildWhen: (previous, current) => previous != current,
            builder: (builder2Context, state) {
              return switch (state) {
                Loading() => Center(child: CircularProgressIndicator()),
                BrokerSettingsLoaded(:var data) ||
                WalletCreated(:var data) ||
                WalletCreationFailed(:var data) => Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 8),
                      DuploText(
                        text: localization.trader_chooseAccountCurrency,
                        style: duploTextStyles.textXl,
                        fontWeight: DuploFontWeight.semiBold,
                        color: theme.text.textPrimary,
                      ),
                      const SizedBox(height: 8),
                      DuploText(
                        text: localization.trader_defaultCurrencyDescription,
                        style: duploTextStyles.textSm,
                        fontWeight: DuploFontWeight.regular,
                        color: theme.text.textSecondary,
                      ),
                      const SizedBox(height: 16),
                      DuploDropDown.selector(
                        bottomSheetTitle: localization.trader_selectCurrency,
                        hint: localization.trader_walletCurrency,
                        dropDownItemModels:
                            data.allCurrencies.map((item) {
                              return DropDownItemModel(
                                title: item.code,
                                image: FlagProvider.getFlagFromCurrencyCode(
                                  item.code,
                                ),
                              );
                            }).toList(),
                        onChanged: (index) {
                          builder2Context.read<CreateNewWalletBloc>().add(
                            CreateNewWalletEvent.currencySelected(index),
                          );
                        },
                        context: context,
                        selectedIndex: data.selectedCurrencyIndex,
                      ),
                      Spacer(),
                      DuploButton.defaultPrimary(
                        title: localization.trader_confirm,
                        useFullWidth: true,
                        isDisabled: data.selectedCurrencyIndex == -1,
                        onTap: () {
                          builder2Context.read<CreateNewWalletBloc>().add(
                            CreateNewWalletEvent.createWallet(),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                BrokerSettingsFailed() => Container(
                  child: Center(
                    child: EmptyOrErrorStateComponent.defaultError(
                      builder2Context,
                      () {
                        builder2Context.read<CreateNewWalletBloc>().add(
                          CreateNewWalletEvent.fetchBrokerSettings(),
                        );
                      },
                    ),
                  ),
                ),
              };
            },
          );
        },
      ),
    );
  }
}
