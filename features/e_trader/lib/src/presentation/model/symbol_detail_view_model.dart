import 'package:freezed_annotation/freezed_annotation.dart';

part 'symbol_detail_view_model.freezed.dart';
part 'symbol_detail_view_model.g.dart';

@unfreezed
abstract class SymbolDetailViewModel with _$SymbolDetailViewModel {
  factory SymbolDetailViewModel({
    required String symbolName,
    required String platformName,
    String? imageURL,
    String? assetType,
    required double minLot,
    required double maxLot,
    int? digit,
    @Default(false) bool isForex,
    required double lotsSteps,
  }) = _SymbolDetailViewModel;

  factory SymbolDetailViewModel.fromJson(Map<String, dynamic> json) =>
      _$SymbolDetailViewModelFromJson(json);
}
