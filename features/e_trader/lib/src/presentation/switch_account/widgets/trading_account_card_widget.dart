import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/account_category.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:prelude/prelude.dart';

class TradingAccountCardWidget extends StatelessWidget {
  const TradingAccountCardWidget({
    super.key,
    required this.accountName,
    required this.accountCategory,
    required this.equity,
    required this.profit,
    this.marginLevel,
    required this.balance,
    required this.currency,
    this.onActionPressed,
    required this.onTap,
    this.tags = const [],
    this.isSelected = false,
  });

  final bool isSelected;
  final AccountCategory accountCategory;
  final String accountName;
  final List<String> tags;
  final String currency;
  final double equity;
  final double profit;
  final double? marginLevel;
  final double balance;
  final VoidCallback? onActionPressed;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final formattedEquity = EquitiFormatter.decimalPatternDigits(
      value: equity,
      locale: Localizations.localeOf(context).toString(),
      digits: 2,
    );
    final formattedProfit = EquitiFormatter.decimalPatternDigits(
      value: profit,
      digits: 2,
      locale: Localizations.localeOf(context).toString(),
    );
    final formattedBalance = EquitiFormatter.decimalPatternDigits(
      value: balance,
      digits: 2,
      locale: Localizations.localeOf(context).toString(),
    );

    final String equityWhole = formattedEquity.split('.').firstOrNull!;
    final String equityFraction =
        formattedEquity.split('.').elementAtOrNull(1)!;
    final String profitWhole = formattedProfit.split('.').firstOrNull!;
    final String profitFraction =
        formattedProfit.split('.').elementAtOrNull(1)!;
    final String balanceWhole = formattedBalance.split('.').firstOrNull!;
    final String balanceFraction =
        formattedBalance.split('.').elementAtOrNull(1)!;

    return DuploTap(
      onTap: onTap,
      child: Card(
        elevation: 0,
        color: theme.background.bgSecondaryHover,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color:
                isSelected
                    ? theme.border.borderBrand
                    : theme.border.borderSecondary,
            width: 1.0,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: theme.background.bgPrimary,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0xff0A0D121A),
                      blurRadius: 8,
                      spreadRadius: -12,
                      offset: const Offset(10, 0),
                    ),
                  ],
                ),
                child: Padding(
                  padding: EdgeInsetsDirectional.all(16),
                  child: Column(
                    children: [
                      _Header(
                        accountName: accountName,
                        accountCurrency: currency,
                        accountCategory: accountCategory,
                        tags: tags,
                        onActionPressed: onActionPressed,
                      ),
                      const SizedBox(height: 16),
                      _Content(
                        currency: currency,
                        accountCategory: accountCategory,
                        balanceWhole: balanceWhole,
                        balanceFraction: balanceFraction,
                        equityWhole: equityWhole,
                        equityFraction: equityFraction,
                        profitWhole: profitWhole,
                        profitFraction: profitFraction,
                      ),
                      if (accountCategory == AccountCategory.accounts) ...[
                        const SizedBox(height: 8),
                        _Footer(marginLevel: marginLevel),
                      ],
                    ],
                  ),
                ),
              ),
            ),
            Assets.images
                .chevronRightDirectional(context)
                .svg(
                  height: 24,
                  width: 24,
                  colorFilter: ColorFilter.mode(
                    theme.foreground.fgQuinary,
                    BlendMode.srcIn,
                  ),
                ),
          ],
        ),
      ),
    );
  }
}

class _Header extends StatelessWidget {
  const _Header({
    required this.accountName,
    required this.accountCategory,
    required this.accountCurrency,
    this.tags = const [],
    this.onActionPressed,
  });

  final String accountName;
  final String accountCurrency;
  final AccountCategory accountCategory;
  final List<String> tags;
  final VoidCallback? onActionPressed;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DuploText(
                text: accountName,
                style: textStyles.textXs,
                fontWeight: DuploFontWeight.semiBold,
                color: theme.text.textSecondary,
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 4,
                runSpacing: 4,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  if (accountCategory == AccountCategory.accounts)
                    ...tags
                        .map<Widget>(
                          (tag) => DuploTagContainer.xs(
                            text: tag,
                            type: DuploTagType.neutral,
                          ),
                        )
                        .toList(),
                  if (accountCategory == AccountCategory.accounts)
                    DuploTagContainer.xs(
                      leading: FlagProvider.getFlagFromCurrencyCode(
                        accountCurrency,
                      ),
                      text: accountCurrency,
                      type: DuploTagType.neutral,
                    ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(width: 8),
        if (accountCategory == AccountCategory.accounts)
          IconButton(
            onPressed: onActionPressed,
            icon: Icon(Icons.more_vert, color: theme.foreground.fgQuaternary),
          )
        else
          DuploTagContainer.xs(
            leading: FlagProvider.getFlagFromCurrencyCode(accountCurrency),
            text: accountCurrency,
            type: DuploTagType.neutral,
          ),
      ],
    );
  }
}

class _Content extends StatelessWidget {
  const _Content({
    required this.equityWhole,
    required this.equityFraction,
    required this.profitWhole,
    required this.profitFraction,
    required this.accountCategory,
    required this.balanceWhole,
    required this.balanceFraction,
    required this.currency,
  });

  final String equityWhole;
  final String equityFraction;
  final String profitWhole;
  final String profitFraction;
  final AccountCategory accountCategory;
  final String balanceWhole;
  final String balanceFraction;
  final String currency;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final textStyles = context.duploTextStyles;

    return Row(
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DuploText(
              text:
                  accountCategory == AccountCategory.accounts
                      ? EquitiLocalization.of(context).trader_equity
                      : EquitiLocalization.of(context).trader_balance,
              style: textStyles.textXs,
              fontWeight: DuploFontWeight.medium,
              color: theme.text.textTertiary,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            DuploText.rich(
              spans: [
                DuploTextSpan(
                  text:
                      accountCategory == AccountCategory.accounts
                          ? equityWhole
                          : balanceWhole,
                  style: textStyles.textLg,
                  fontWeight: DuploFontWeight.semiBold,
                  color: theme.text.textPrimary,
                ),
                DuploTextSpan(
                  text: '.',
                  style: textStyles.textSm,
                  color: theme.text.textSecondary,
                ),
                DuploTextSpan(
                  text:
                      accountCategory == AccountCategory.accounts
                          ? equityFraction
                          : balanceFraction,
                  style: textStyles.textSm,
                  color: theme.text.textSecondary,
                ),
              ],
            ),
          ],
        ),
        const Spacer(),
        if (accountCategory == AccountCategory.accounts)
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              DuploText(
                text: EquitiLocalization.of(context).trader_profit,
                style: textStyles.textXs,
                fontWeight: DuploFontWeight.medium,
                color: theme.text.textTertiary,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              DuploText.rich(
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                spans: [
                  DuploTextSpan(
                    text: profitWhole,
                    style: textStyles.textLg,
                    fontWeight: DuploFontWeight.semiBold,
                    color: theme.text.textPrimary,
                  ),
                  DuploTextSpan(
                    text: '.',
                    style: textStyles.textSm,
                    color: theme.text.textSecondary,
                  ),
                  DuploTextSpan(
                    text: profitFraction,
                    style: textStyles.textSm,
                    color: theme.text.textSecondary,
                  ),
                ],
              ),
            ],
          ),
      ],
    );
  }
}

class _Footer extends StatelessWidget {
  const _Footer({required this.marginLevel});

  final double? marginLevel;

  @override
  Widget build(BuildContext context) =>
      DuploMarginProgressBar(marginLevel: marginLevel ?? 0);
}
