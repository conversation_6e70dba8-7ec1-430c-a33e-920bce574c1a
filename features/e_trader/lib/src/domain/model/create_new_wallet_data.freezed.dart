// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'create_new_wallet_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$CreateNewWalletData {

 List<AccountCurrency> get allCurrencies; int get selectedCurrencyIndex;
/// Create a copy of CreateNewWalletData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CreateNewWalletDataCopyWith<CreateNewWalletData> get copyWith => _$CreateNewWalletDataCopyWithImpl<CreateNewWalletData>(this as CreateNewWalletData, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CreateNewWalletData&&const DeepCollectionEquality().equals(other.allCurrencies, allCurrencies)&&(identical(other.selectedCurrencyIndex, selectedCurrencyIndex) || other.selectedCurrencyIndex == selectedCurrencyIndex));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(allCurrencies),selectedCurrencyIndex);

@override
String toString() {
  return 'CreateNewWalletData(allCurrencies: $allCurrencies, selectedCurrencyIndex: $selectedCurrencyIndex)';
}


}

/// @nodoc
abstract mixin class $CreateNewWalletDataCopyWith<$Res>  {
  factory $CreateNewWalletDataCopyWith(CreateNewWalletData value, $Res Function(CreateNewWalletData) _then) = _$CreateNewWalletDataCopyWithImpl;
@useResult
$Res call({
 List<AccountCurrency> allCurrencies, int selectedCurrencyIndex
});




}
/// @nodoc
class _$CreateNewWalletDataCopyWithImpl<$Res>
    implements $CreateNewWalletDataCopyWith<$Res> {
  _$CreateNewWalletDataCopyWithImpl(this._self, this._then);

  final CreateNewWalletData _self;
  final $Res Function(CreateNewWalletData) _then;

/// Create a copy of CreateNewWalletData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? allCurrencies = null,Object? selectedCurrencyIndex = null,}) {
  return _then(_self.copyWith(
allCurrencies: null == allCurrencies ? _self.allCurrencies : allCurrencies // ignore: cast_nullable_to_non_nullable
as List<AccountCurrency>,selectedCurrencyIndex: null == selectedCurrencyIndex ? _self.selectedCurrencyIndex : selectedCurrencyIndex // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// @nodoc


class _CreateNewWalletData implements CreateNewWalletData {
   _CreateNewWalletData({required final  List<AccountCurrency> allCurrencies, required this.selectedCurrencyIndex}): _allCurrencies = allCurrencies;
  

 final  List<AccountCurrency> _allCurrencies;
@override List<AccountCurrency> get allCurrencies {
  if (_allCurrencies is EqualUnmodifiableListView) return _allCurrencies;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_allCurrencies);
}

@override final  int selectedCurrencyIndex;

/// Create a copy of CreateNewWalletData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CreateNewWalletDataCopyWith<_CreateNewWalletData> get copyWith => __$CreateNewWalletDataCopyWithImpl<_CreateNewWalletData>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CreateNewWalletData&&const DeepCollectionEquality().equals(other._allCurrencies, _allCurrencies)&&(identical(other.selectedCurrencyIndex, selectedCurrencyIndex) || other.selectedCurrencyIndex == selectedCurrencyIndex));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_allCurrencies),selectedCurrencyIndex);

@override
String toString() {
  return 'CreateNewWalletData(allCurrencies: $allCurrencies, selectedCurrencyIndex: $selectedCurrencyIndex)';
}


}

/// @nodoc
abstract mixin class _$CreateNewWalletDataCopyWith<$Res> implements $CreateNewWalletDataCopyWith<$Res> {
  factory _$CreateNewWalletDataCopyWith(_CreateNewWalletData value, $Res Function(_CreateNewWalletData) _then) = __$CreateNewWalletDataCopyWithImpl;
@override @useResult
$Res call({
 List<AccountCurrency> allCurrencies, int selectedCurrencyIndex
});




}
/// @nodoc
class __$CreateNewWalletDataCopyWithImpl<$Res>
    implements _$CreateNewWalletDataCopyWith<$Res> {
  __$CreateNewWalletDataCopyWithImpl(this._self, this._then);

  final _CreateNewWalletData _self;
  final $Res Function(_CreateNewWalletData) _then;

/// Create a copy of CreateNewWalletData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? allCurrencies = null,Object? selectedCurrencyIndex = null,}) {
  return _then(_CreateNewWalletData(
allCurrencies: null == allCurrencies ? _self._allCurrencies : allCurrencies // ignore: cast_nullable_to_non_nullable
as List<AccountCurrency>,selectedCurrencyIndex: null == selectedCurrencyIndex ? _self.selectedCurrencyIndex : selectedCurrencyIndex // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
