import 'package:domain/domain.dart';
import 'package:equiti_analytics/equiti_analytics.dart';

class AccountAnalytics {
  final AnalyticsService _analyticsService;

  const AccountAnalytics(this._analyticsService);

  Future<bool> accountSelected(TradingAccountModel? accountModel) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.accountSelected.eventType,
      eventName: TradeAnalyticsEvent.accountSelected.eventName,
      metadata: {'accountModel': accountModel?.toJson()},
    );
  }
}
