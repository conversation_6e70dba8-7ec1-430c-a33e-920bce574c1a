import 'package:e_trader/src/data/api/close_trade_request_model.dart';
import 'package:e_trader/src/data/api/create_order_request_model.dart';
import 'package:e_trader/src/data/api/create_trade_request_model.dart';
import 'package:e_trader/src/domain/model/order_limit_calculation.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:equiti_analytics/equiti_analytics.dart';

class TradingAnalytics {
  final AnalyticsService _analyticsService;

  const TradingAnalytics(this._analyticsService);

  Future<bool> symbolSelected(String symbol) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.symbolSelected.eventType,
      eventName: TradeAnalyticsEvent.symbolSelected.eventName,
      metadata: {'symbol': symbol},
    );
  }

  Future<bool> LotSizeChanged(double lotSize, TradeOrderTabType tabType) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.LotSizeChanged.eventType,
      eventName: TradeAnalyticsEvent.LotSizeChanged.eventName,
      metadata: {'lotSize': lotSize, "tabType": tabType.name},
    );
  }

  Future<bool> buySellSelected(TradeType tradeType) async {
    final TradeAnalyticsEvent eventType =
        tradeType == TradeType.buy
            ? TradeAnalyticsEvent.buySelected
            : TradeAnalyticsEvent.sellSelected;
    return await _analyticsService.sendEvent(
      eventType: eventType.eventType,
      eventName: eventType.eventName,
      metadata: {},
    );
  }

  Future<bool> takeProfitChanged(
    OrderLimitCalculation takeProfit,
    TradeOrderTabType tabType,
    bool isTpActive,
  ) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.tpChanged.eventType,
      eventName: TradeAnalyticsEvent.tpChanged.eventName,
      metadata: {
        'takeProfit': takeProfit.toJson(),
        "tabType": tabType.name,
        "isActive": isTpActive,
      },
    );
  }

  Future<bool> stopLossChanged(
    OrderLimitCalculation stopLoss,
    TradeOrderTabType tabType,
    bool isSlActive,
  ) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.slChanged.eventType,
      eventName: TradeAnalyticsEvent.slChanged.eventName,
      metadata: {
        'stopLoss': stopLoss.toJson(),
        "tabType": tabType.name,
        "isActive": isSlActive,
      },
    );
  }

  Future<bool> placingTrade(CreateTradeRequestModel model) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.placingTrade.eventType,
      eventName: TradeAnalyticsEvent.placingTrade.eventName,
      metadata: {"requestModel": model.toJson()},
    );
  }

  Future<bool> placingOrder(CreateOrderRequestModel model) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.placingOrder.eventType,
      eventName: TradeAnalyticsEvent.placingOrder.eventName,
      metadata: {"requestModel": model.toJson()},
    );
  }

  Future<bool> tradePlacedResult(Map<String, dynamic> metaData) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.tradePlacedResult.eventType,
      eventName: TradeAnalyticsEvent.tradePlacedResult.eventName,
      metadata: metaData,
    );
  }

  Future<bool> orderPlacedResult(Map<String, dynamic> metaData) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.orderPlacedResult.eventType,
      eventName: TradeAnalyticsEvent.orderPlacedResult.eventName,
      metadata: metaData,
    );
  }

  Future<bool> startCloseTrade(
    CloseTradeRequestModel model,
    CloseTradeType type,
  ) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.startCloseTrade.eventType,
      eventName: TradeAnalyticsEvent.startCloseTrade.eventName,
      metadata: {"tradeType": type.name, "requestModel": model.toJson()},
    );
  }

  Future<bool> closeTradeResult(Map<String, dynamic> metaData) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.closeTradeResult.eventType,
      eventName: TradeAnalyticsEvent.closeTradeResult.eventName,
      metadata: metaData,
    );
  }

  Future<bool> positionAdded(String? positionId, String? symbol) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.positionAdded.eventType,
      eventName: TradeAnalyticsEvent.positionAdded.eventName,
      metadata: {"positionId": positionId, "symbol": symbol},
    );
  }

  Future<bool> positionDeleted(String? positionId, String? symbol) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.positionDeleted.eventType,
      eventName: TradeAnalyticsEvent.positionDeleted.eventName,
      metadata: {"positionId": positionId, "symbol": symbol},
    );
  }

  Future<bool> orderAdded(String? orderId, String? symbol) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.orderAdded.eventType,
      eventName: TradeAnalyticsEvent.orderAdded.eventName,
      metadata: {"orderId": orderId, "symbol": symbol},
    );
  }

  Future<bool> orderDeleted(String? orderId, String? symbol) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.orderDeleted.eventType,
      eventName: TradeAnalyticsEvent.orderDeleted.eventName,
      metadata: {"orderId": orderId, "symbol": symbol},
    );
  }

  Future<bool> alertAdded(String? alertId, String? symbol) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.alertAdded.eventType,
      eventName: TradeAnalyticsEvent.alertAdded.eventName,
      metadata: {"alertId": alertId, "symbol": symbol},
    );
  }

  Future<bool> alertDeleted(
    String? alertId,
    String? symbol,
    PriceAlertDeletionReason deletionReason,
  ) async {
    return await _analyticsService.sendEvent(
      eventType: TradeAnalyticsEvent.alertDeleted.eventType,
      eventName: TradeAnalyticsEvent.alertDeleted.eventName,
      metadata: {
        "alertId": alertId,
        "symbol": symbol,
        "deletionReason": deletionReason.name,
      },
    );
  }
}

enum TradeOrderTabType { tradeTab, orderTab }

enum CloseTradeType { partialClose, close, quickClose }

enum PriceAlertDeletionReason { deleted, triggered }
