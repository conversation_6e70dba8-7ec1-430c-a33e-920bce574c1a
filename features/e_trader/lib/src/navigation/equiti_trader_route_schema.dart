enum EquitiTraderRouteSchema {
  symbolsRoute(label: 'symbols', url: '/symbols'),
  symbolsDetailRoute(label: 'symbolsDetail', url: '/symbolsDetails'),
  portfolioRoute(label: 'portfolio', url: '/portfolio'),
  navBarRoute(label: 'navBar', url: '/navBar'),
  switchAccountRoute(label: 'switchAccount', url: '/switchAccount'),
  loginRoute(label: 'login', url: '/login'),
  verifyMobileRoute(label: 'verifyMobile', url: '/verifyMobile'),
  mobileNumberInputRoute(label: 'mobileNumberInput', url: '/mobileNumberInput'),
  otpInputRoute(label: 'otpInput', url: '/otpInput'),
  fullChartRoute(label: 'fullChart', url: '/fullChart'),
  phoneNumberVerifiedRoute(
    label: 'phoneNumberVerified',
    url: '/phoneNumberVerified',
  ),

  historicalPerformanceRoute(
    label: "historicalPerformance",
    url: "/historicalPerformance",
  ),

  createWalletRoute(label: "createWallet", url: "/createWallet");

  const EquitiTraderRouteSchema({required this.label, required this.url});
  final String label;
  final String url;
}
