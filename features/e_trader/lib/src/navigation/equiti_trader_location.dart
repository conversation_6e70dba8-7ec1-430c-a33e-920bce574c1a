import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/navigation/historical_performance_page.dart';
import 'package:e_trader/src/navigation/nav_bar_page.dart';
import 'package:e_trader/src/navigation/portfolio_page.dart';
import 'package:e_trader/src/navigation/switch_account_page.dart';
import 'package:e_trader/src/navigation/symbol_detail_page.dart';
import 'package:e_trader/src/navigation/create_wallet_page.dart';
import 'package:equiti_router/equiti_router.dart';

class EquitiTraderLocation extends EquitiRouteLocation {
  @override
  EquitiPage get initialPage => diContainer<PortfolioPage>();

  @override
  List<EquitiPage> get pages => [
    PortfolioPage(),
    SymbolDetailPage(),
    NavBarPage(),
    SwitchAccountPage(),
    HistoricalPerformancePage(),
    CreateWalletPage(),
  ];
}
