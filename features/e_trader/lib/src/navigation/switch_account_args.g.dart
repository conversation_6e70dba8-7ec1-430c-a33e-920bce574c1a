// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'switch_account_args.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SwitchAccountArgs _$SwitchAccountArgsFromJson(Map<String, dynamic> json) =>
    $checkedCreate('_SwitchAccountArgs', json, ($checkedConvert) {
      final val = _SwitchAccountArgs(
        accountType: $checkedConvert(
          'accountType',
          (v) => $enumDecode(_$AccountTypeEnumMap, v),
        ),
      );
      return val;
    });

Map<String, dynamic> _$SwitchAccountArgsToJson(_SwitchAccountArgs instance) =>
    <String, dynamic>{
      'accountType': _$AccountTypeEnumMap[instance.accountType]!,
    };

const _$AccountTypeEnumMap = {
  AccountType.trading: 'Trading',
  AccountType.landingWallet: 'LandingWallet',
  AccountType.unknown: 'unknown',
};
