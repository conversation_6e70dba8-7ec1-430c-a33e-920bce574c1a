import 'package:duplo/duplo.dart';
import 'package:e_trader/src/navigation/equiti_trader_route_schema.dart';
import 'package:e_trader/src/presentation/create_new_wallet/create_new_wallet_screen.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/material.dart';

class CreateWalletPage extends EquitiPage {
  const CreateWalletPage();

  @override
  Widget builder(
    BuildContext context,
    EquitiRoute routeData,
    Map<String, dynamic> globalData,
  ) {
    return Scaffold(
      appBar: DuploAppBar(
        title: EquitiLocalization.of(context).payments_add_new_wallet,
      ),
      backgroundColor: context.duploTheme.background.bgPrimary,
      body: const CreateNewWalletScreen(),
    );
  }

  @override
  String get label => EquitiTraderRouteSchema.createWalletRoute.label;

  @override
  String get url => EquitiTraderRouteSchema.createWalletRoute.url;
}
