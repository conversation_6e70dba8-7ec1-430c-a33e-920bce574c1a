import 'package:e_trader/src/navigation/equiti_trader_route_schema.dart';
import 'package:e_trader/src/navigation/switch_account_args.dart';
import 'package:e_trader/src/presentation/switch_account/switch_account_screen.dart';
import 'package:equiti_router/equiti_router.dart';
import 'package:flutter/src/widgets/framework.dart';

class SwitchAccountPage extends EquitiPage {
  const SwitchAccountPage();

  @override
  Widget builder(
    BuildContext context,
    EquitiRoute routeData,
    Map<String, dynamic> globalData,
  ) {
    final args = routeData.arguments as SwitchAccountArgs;
    final accountType = args.accountType;
    return SwitchAccountScreen(accountType: accountType);
  }

  @override
  String get label => EquitiTraderRouteSchema.switchAccountRoute.label;

  @override
  String get url => EquitiTraderRouteSchema.switchAccountRoute.url;
}
