// ignore_for_file: prefer-sealed-bloc-events, prefer-immutable-bloc-events

import 'package:equiti_analytics/equiti_analytics.dart';

class DepositAnalyticsEvent {
  DepositAnalyticsEvent(this._analyticsService);

  final AnalyticsService _analyticsService;

  static const String _paymentType = 'deposit';

  String? _selectedMop;

  Map<String, dynamic> _getBaseAttributes() {
    return {
      'paymentType': _paymentType,
      if (_selectedMop != null) 'mop': _selectedMop,
    };
  }

  Future<bool> depositStart() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositStart.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositStart.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> depositMopSelected(String mop) async {
    _selectedMop = mop;
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositMopSelected.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositMopSelected.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> depositAccountSelected({
    required String type,
    required String accountCurrency,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositAccountSelected.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositAccountSelected.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'type': type,
        'currency': accountCurrency,
      },
    );
  }

  Future<bool> depositCurrencyChanged({
    String? limits,
    required String accountCurrency,
    required String selectedCurrency,
    required double conversionRate,
    required String conversionRateString,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositCurrencyChanged.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositCurrencyChanged.eventName,
      metadata: {
        ..._getBaseAttributes(),
        if (limits != null) 'limits': limits,
        'accountCurrency': accountCurrency,
        'selectedCurrency': selectedCurrency,
        'conversionRate': conversionRate,
        'conversionRateString': conversionRateString,
      },
    );
  }

  Future<bool> depositSuggestedAmountSelected({
    required num amount,
    required String currency,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.depositSuggestedAmountSelected.eventType.name,
      eventName:
          PaymentsAnalyticsEvent.depositSuggestedAmountSelected.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'amount': amount,
        'currency': currency,
      },
    );
  }

  Future<bool> depositInitiated({
    required String transactionId,
    required String amount,
    required String convertedAmount,
    required String accountCurrency,
    required String selectedCurrency,
    required double conversionRate,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositInitiated.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositInitiated.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'transactionId': transactionId,
        'amount': amount,
        'convertedAmount': convertedAmount,
        'accountCurrency': accountCurrency,
        'selectedCurrency': selectedCurrency,
        'conversionRate': conversionRate,
      },
    );
  }

  Future<bool> depositWebviewLoadStarted() async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.depositWebviewLoadStarted.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositWebviewLoadStarted.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> depositWebviewLoaded() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositWebviewLoaded.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositWebviewLoaded.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> depositWebviewCallback({
    required String status,
    required String gatewayCode,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositWebviewCallback.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositWebviewCallback.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'status': status,
        'gatewayCode': gatewayCode,
      },
    );
  }

  Future<bool> depositWebviewExitConfirmation({required bool userExit}) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.depositWebviewExitConfirmation.eventType.name,
      eventName:
          PaymentsAnalyticsEvent.depositWebviewExitConfirmation.eventName,
      metadata: {..._getBaseAttributes(), 'userExit': userExit},
    );
  }

  Future<bool> depositSdkLoadStarted() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositSdkLoadStarted.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositSdkLoadStarted.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> depositSdkLoaded() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositSdkLoaded.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositSdkLoaded.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> depositSdkEvent({
    required String status,
    required String gatewayCode,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositSdkEvent.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositSdkEvent.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'status': status,
        'gatewayCode': gatewayCode,
      },
    );
  }

  Future<bool> depositCompleted({required String status}) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositCompleted.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositCompleted.eventName,
      metadata: {..._getBaseAttributes(), 'status': status},
    );
  }

  Future<bool> depositBankDetailsCopied({
    required String bankName,
    required String currency,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositBankDetailsCopied.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositBankDetailsCopied.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'bankName': bankName,
        'currency': currency,
      },
    );
  }

  Future<bool> depositBankPdfDownloaded({
    required String bankName,
    required String currency,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositBankPdfDownloaded.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositBankPdfDownloaded.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'bankName': bankName,
        'currency': currency,
      },
    );
  }

  Future<bool> depositNotAvailable() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.depositNotAvailable.eventType.name,
      eventName: PaymentsAnalyticsEvent.depositNotAvailable.eventName,
      metadata: {..._getBaseAttributes()},
    );
  }
}
