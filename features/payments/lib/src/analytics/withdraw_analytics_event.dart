// ignore_for_file: prefer-sealed-bloc-events, prefer-immutable-bloc-events

import 'package:equiti_analytics/equiti_analytics.dart';

class WithdrawAnalyticsEvent {
  WithdrawAnalyticsEvent(this._analyticsService);

  final AnalyticsService _analyticsService;

  static const String _paymentType = 'withdraw';

  String? _selectedMop;

  Map<String, dynamic> _getBaseAttributes() {
    return {
      'paymentType': _paymentType,
      if (_selectedMop != null) 'mop': _selectedMop,
    };
  }

  Future<bool> withdrawStart() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawStart.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawStart.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> withdrawMopSelected({required String mop}) async {
    _selectedMop = mop;
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawMopSelected.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawMopSelected.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> withdrawAccountSelected({
    String? limits,
    required String accountType,
    required String accountCurrency,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawAccountSelected.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawAccountSelected.eventName,
      metadata: {
        ..._getBaseAttributes(),
        if (limits != null) 'limits': limits,
        'accountType': accountType,
        'accountCurrency': accountCurrency,
      },
    );
  }

  Future<bool> withdrawCurrencyChanged({
    String? limits,
    required String accountCurrency,
    required String selectedCurrency,
    required double conversionRate,
    required String conversionRateString,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawCurrencyChanged.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawCurrencyChanged.eventName,
      metadata: {
        ..._getBaseAttributes(),
        if (limits != null) 'limits': limits,
        'accountCurrency': accountCurrency,
        'selectedCurrency': selectedCurrency,
        'conversionRate': conversionRate,
        'conversionRateString': conversionRateString,
      },
    );
  }

  Future<bool> withdrawSuggestedAmountSelected({
    required String amount,
    required String currency,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.withdrawSuggestedAmountSelected.eventType.name,
      eventName:
          PaymentsAnalyticsEvent.withdrawSuggestedAmountSelected.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'amount': amount,
        'currency': currency,
      },
    );
  }

  Future<bool> withdawCardsLoaded({required String numOfCards}) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdawCardsLoaded.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdawCardsLoaded.eventName,
      metadata: {..._getBaseAttributes(), 'numOfCards': numOfCards},
    );
  }

  Future<bool> withdrawCardSelected() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawCardSelected.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawAccountSelected.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> withdrawFeesChanged({
    required String fees,
    required String accountCurrency,
    required String selectedCurrency,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawFeesChanged.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawFeesChanged.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'fees': fees,
        'accountCurrency': accountCurrency,
        'selectedCurrency': selectedCurrency,
      },
    );
  }

  Future<bool> withdrawMFAInitiated() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawMFAInitiated.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawMFAInitiated.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> withdrawMFACompleted() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawMFACompleted.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawMFACompleted.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> withdrawInitiated({
    required String amount,
    required String convertedAmount,
    required String accountCurrency,
    required String selectedCurrency,
    required String conversionRate,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawInitiated.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawInitiated.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'amount': amount,
        'convertedAmount': convertedAmount,
        'accountCurrency': accountCurrency,
        'selectedCurrency': selectedCurrency,
        'conversionRate': conversionRate,
      },
    );
  }

  Future<bool> withdrawBanksLoaded({required String numOfBanks}) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawBanksLoaded.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawBanksLoaded.eventName,
      metadata: {..._getBaseAttributes(), 'numOfBanks': numOfBanks},
    );
  }

  Future<bool> withdrawBankSelected({required String bankId}) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawBankSelected.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawBankSelected.eventName,
      metadata: {..._getBaseAttributes(), 'bankId': bankId},
    );
  }

  Future<bool> withdrawBankDeleted({required String bankId}) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawBankDeleted.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawBankDeleted.eventName,
      metadata: {..._getBaseAttributes(), 'bankId': bankId},
    );
  }

  Future<bool> withdrawBankTransferTypesLoaded({required String types}) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.withdrawBankTransferTypesLoaded.eventType.name,
      eventName:
          PaymentsAnalyticsEvent.withdrawBankTransferTypesLoaded.eventName,
      metadata: {..._getBaseAttributes(), 'types': types},
    );
  }

  Future<bool> withdrawBankTransferTypeSelected({
    required String selectedType,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent
              .withdrawBankTransferTypeSelected
              .eventType
              .name,
      eventName:
          PaymentsAnalyticsEvent.withdrawBankTransferTypeSelected.eventName,
      metadata: {..._getBaseAttributes(), 'selectedType': selectedType},
    );
  }

  Future<bool> withdrawBankFeesChanged({
    required String fees,
    required String transferType,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawBankFeesChanged.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawBankFeesChanged.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'fees': fees,
        'transferType': transferType,
      },
    );
  }

  Future<bool> withdrawAddNewBankStart() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawAddNewBankStart.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawAddNewBankStart.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> withdrawAddNewBankInititate() async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.withdrawAddNewBankInititate.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawAddNewBankInititate.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> withdrawAddNewBankComplete({required String status}) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.withdrawAddNewBankComplete.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawAddNewBankComplete.eventName,
      metadata: {..._getBaseAttributes(), 'status': status},
    );
  }

  Future<bool> withdrawBankDocUploadPageLoaded() async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.withdrawBankDocUploadPageLoaded.eventType.name,
      eventName:
          PaymentsAnalyticsEvent.withdrawBankDocUploadPageLoaded.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> withdrawBankDocUploadFileSelected({
    required String size,
    required String fileType,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent
              .withdrawBankDocUploadFileSelected
              .eventType
              .name,
      eventName:
          PaymentsAnalyticsEvent.withdrawBankDocUploadFileSelected.eventName,
      metadata: {..._getBaseAttributes(), 'size': size, 'fileType': fileType},
    );
  }

  Future<bool> withdrawSkrillAccountsLoaded({
    required String numOfAccount,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.withdrawSkrillAccountsLoaded.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawSkrillAccountsLoaded.eventName,
      metadata: {..._getBaseAttributes(), 'numOfAccount': numOfAccount},
    );
  }

  Future<bool> withdrawSkrillAccountSelected({
    required String accountId,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.withdrawSkrillAccountSelected.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawSkrillAccountSelected.eventName,
      metadata: {..._getBaseAttributes(), 'accountId': accountId},
    );
  }

  Future<bool> withdrawNetellerAccountsLoaded({
    required String numOfAccount,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.withdrawNetellerAccountsLoaded.eventType.name,
      eventName:
          PaymentsAnalyticsEvent.withdrawNetellerAccountsLoaded.eventName,
      metadata: {..._getBaseAttributes(), 'numOfAccount': numOfAccount},
    );
  }

  Future<bool> withdrawNetellerAccountSelected({
    required String accountId,
  }) async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.withdrawNetellerAccountSelected.eventType.name,
      eventName:
          PaymentsAnalyticsEvent.withdrawNetellerAccountSelected.eventName,
      metadata: {..._getBaseAttributes(), 'accountId': accountId},
    );
  }

  Future<bool> mopNotApplicable() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.mopNotApplicable.eventType.name,
      eventName: PaymentsAnalyticsEvent.mopNotApplicable.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> withdrawCompleted({required String status}) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawCompleted.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawCompleted.eventName,
      metadata: {..._getBaseAttributes(), 'status': status},
    );
  }

  Future<bool> withdrawNotAvailable() async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.withdrawNotAvailable.eventType.name,
      eventName: PaymentsAnalyticsEvent.withdrawNotAvailable.eventName,
      metadata: _getBaseAttributes(),
    );
  }
}
