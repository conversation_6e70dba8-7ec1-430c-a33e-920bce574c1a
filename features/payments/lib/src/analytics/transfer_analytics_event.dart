// ignore_for_file: prefer-sealed-bloc-events, prefer-immutable-bloc-events

import 'package:equiti_analytics/equiti_analytics.dart';

class TransferAnalyticsEvent {
  const TransferAnalyticsEvent(this._analyticsService);

  final AnalyticsService _analyticsService;

  static const String _paymentType = 'transfer';

  Map<String, dynamic> _getBaseAttributes() {
    return {'paymentType': _paymentType};
  }

  Future<bool> transferSourceAccountPageLoaded() async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.transferSourceAccountPageLoaded.eventType.name,
      eventName:
          PaymentsAnalyticsEvent.transferSourceAccountPageLoaded.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> transferSourceAccountSelected() async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent.transferSourceAccountSelected.eventType.name,
      eventName: PaymentsAnalyticsEvent.transferSourceAccountSelected.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> transferDestinationAccountPageLoaded() async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent
              .transferDestinationAccountPageLoaded
              .eventType
              .name,
      eventName:
          PaymentsAnalyticsEvent.transferDestinationAccountPageLoaded.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> transferDestinationAccountSelected() async {
    return await _analyticsService.sendEvent(
      eventType:
          PaymentsAnalyticsEvent
              .transferDestinationAccountSelected
              .eventType
              .name,
      eventName:
          PaymentsAnalyticsEvent.transferDestinationAccountSelected.eventName,
      metadata: _getBaseAttributes(),
    );
  }

  Future<bool> transferInititated({
    required String amount,
    required String convertedAmount,
    required String accountCurrency,
    required String selectedCurrency,
    required String conversionRate,
  }) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.transferInititated.eventType.name,
      eventName: PaymentsAnalyticsEvent.transferInititated.eventName,
      metadata: {
        ..._getBaseAttributes(),
        'amount': amount,
        'convertedAmount': convertedAmount,
        'accountCurrency': accountCurrency,
        'selectedCurrency': selectedCurrency,
        'conversionRate': conversionRate,
      },
    );
  }

  Future<bool> transferCompleted({required String status}) async {
    return await _analyticsService.sendEvent(
      eventType: PaymentsAnalyticsEvent.transferCompleted.eventType.name,
      eventName: PaymentsAnalyticsEvent.transferCompleted.eventName,
      metadata: {..._getBaseAttributes(), 'status': status},
    );
  }
}
