import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/analytics/deposit_analytics_event.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/domain/usecase/get_deposit_details_usecase.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:payment/src/presentation/deposit_accounts_and_amount/widgets/deposit_accounts_amount_loaded_screen.dart';

import 'package:payment/src/presentation/widgets/transaction_status_screen.dart';
import 'package:payment/src/utils/payment/apple_pay_utils.dart';
import 'package:payment/src/utils/payment/google_pay_utils.dart';

import 'bloc/deposit_accounts_and_amount_bloc.dart';

class DepositAccountsAndAmountScreen extends StatelessWidget {
  const DepositAccountsAndAmountScreen({
    super.key,
    required this.paymentMethod,
    this.maxPollingAttempts,
    this.pollingFrequencySeconds,
    required this.depositFlowConfig,
  });
  final DepositPaymentMethod paymentMethod;
  final num? maxPollingAttempts;
  final num? pollingFrequencySeconds;
  final DepositFlowConfig depositFlowConfig;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (ctx) => DepositAccountsAndAmountBloc(
            getDepositDetailsUsecase: diContainer<GetDepositDetailsUsecase>(),
            paymentNavigation: diContainer<PaymentNavigation>(),
            googlePayUtils: diContainer<GooglePayUtils>(),
            applePayUtils: diContainer<ApplePayUtils>(),
            maxPollingAttempts: maxPollingAttempts,
            pollingFrequencySeconds: pollingFrequencySeconds,
            depositFlowConfig: depositFlowConfig,
            depositAnalyticsEvent: diContainer<DepositAnalyticsEvent>(),
          ),
      child: BlocBuilder<
        DepositAccountsAndAmountBloc,
        DepositAccountsAndAmountState
      >(
        buildWhen:
            (previous, current) =>
                previous.processState != current.processState ||
                previous.isFullPageLoadingEnabled !=
                    current.isFullPageLoadingEnabled,
        builder: (ctx, state) {
          final bloc = ctx.read<DepositAccountsAndAmountBloc>();
          if (state.processState is PaymentSuccessState &&
              depositFlowConfig.depositType == DepositType.first) {
            diContainer<AnalyticsService>().sendEvent(
              eventType:
                  OnboardingAnalyticsEvent.firstDepositComplete.eventType.name,
              eventName:
                  OnboardingAnalyticsEvent.firstDepositComplete.eventName,
            );
          }
          return switch (state.processState) {
            ErrorState() => const Center(child: Text('Error')),
            PaymentFailedState() => TransactionStatusScreen.error(
              context: context,
              onChangePaymentMethod: () {
                bloc.add(const OnPaymentMethodChange());
              },
            ),
            PaymentRejectedState() => TransactionStatusScreen.declined(
              context: context,
              onChangePaymentMethod: () {
                bloc.add(const OnPaymentMethodChange());
              },
              onTryAgain: () {
                bloc.add(const ResetProcessStateEvent());
              },
              gatewayCode: null,
            ),
            PaymentSuccessState() => TransactionStatusScreen.success(
              context: context,
              onContinue: () {
                bloc.add(const OnPaymentSuccessContinuePressed());
              },
              onMakeAnotherDeposit: () {
                bloc.add(const OnPaymentMethodChange());
              },
            ),
            LoadedState() => DepositAccountsAmountLoadedScreen(
              paymentMethod: paymentMethod,
              isFullScreenLoaderEnabled: state.isFullPageLoadingEnabled,
            ),
          };
        },
      ),
    );
  }
}
