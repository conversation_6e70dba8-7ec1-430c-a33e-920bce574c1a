part of 'deposit_accounts_and_amount_bloc.dart';

@freezed
sealed class DepositAccountsAndAmountState
    with _$DepositAccountsAndAmountState {
  const factory DepositAccountsAndAmountState({
    TradingAccountModel? selectedAccount,
    @Default(false) bool isButtonEnabled,
    @Default('') String accountCurrencyAmount,
    @Default('') String selectedCurrencyAmount,
    @Default(null) RatesModel? ratesModel,
    @Default(false) bool isButtonLoading,
    @Default('') String conversionRateString,
    @Default('') String selectedCurrency,
    @Default(LoadedState()) DepositAccountsAndAmountProcessState processState,
    @Default(null) String? errorMessage,
    @Default(false) bool isFullPageLoadingEnabled,
  }) = _DepositAccountsAndAmountState;
}

@freezed
sealed class DepositAccountsAndAmountProcessState
    with _$DepositAccountsAndAmountProcessState {
  const factory DepositAccountsAndAmountProcessState.loaded() = LoadedState;
  const factory DepositAccountsAndAmountProcessState.error() = ErrorState;
  const factory DepositAccountsAndAmountProcessState.paymentFailed() =
      PaymentFailedState;
  const factory DepositAccountsAndAmountProcessState.paymentRejected() =
      PaymentRejectedState;
  const factory DepositAccountsAndAmountProcessState.paymentSuccess() =
      PaymentSuccessState;
}
