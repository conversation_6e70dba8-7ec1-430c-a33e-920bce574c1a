import 'dart:developer';

import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/domain/data/deposit_mop.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/presentation/deposit_accounts_and_amount/bloc/deposit_accounts_and_amount_bloc.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/account_list_widget.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/deposit_withdraw_amount_conversion_widget.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/amount_conversion_utils.dart';

class DepositAccountsAmountLoadedScreen extends StatelessWidget {
  const DepositAccountsAmountLoadedScreen({
    super.key,
    required this.paymentMethod,
    required this.isFullScreenLoaderEnabled,
  });
  final DepositPaymentMethod paymentMethod;
  final bool isFullScreenLoaderEnabled;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final EquitiLocalization localization = EquitiLocalization.of(context);
    return Stack(
      children: [
        Scaffold(
          backgroundColor: theme.background.bgPrimary,
          appBar: DuploAppBar(
            title:
                "${localization.payments_deposit_with} ${paymentMethod.name}",
          ),
          body: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 10,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    DuploText(
                      text: localization.payments_deposit_to_account,
                      style: DuploTextStyles.of(context).textLg,
                      fontWeight: DuploFontWeight.semiBold,
                      color: theme.text.textPrimary,
                    ),
                    const SizedBox(height: 5),
                    DuploText(
                      text:
                          localization.payments_deposit_select_trading_account,
                      style: DuploTextStyles.of(context).textSm,
                      fontWeight: DuploFontWeight.regular,
                      color: theme.text.textSecondary,
                    ),
                    const SizedBox(height: 15),
                    BlocBuilder<
                      DepositAccountsAndAmountBloc,
                      DepositAccountsAndAmountState
                    >(
                      buildWhen:
                          (previous, current) =>
                              current.selectedAccount != null &&
                              current.isButtonLoading !=
                                  previous.isButtonLoading,
                      builder: (builderContext, state) {
                        return AccountListWidget(
                          args: (
                            selectByHighestBalance: false,
                            excludeAccountNumber: null,
                            onEmptyStateChanged: null,
                            isInputDisabled: state.isButtonLoading,
                          ),
                          onAccountSelected: (TradingAccountModel account) {
                            log("Account selected: ${account.toJson()}");
                            builderContext
                                .read<DepositAccountsAndAmountBloc>()
                                .add(
                                  DepositAccountsAndAmountEvent.onAccountChange(
                                    account,
                                  ),
                                );
                          },
                        );
                      },
                    ),
                    const SizedBox(height: 15),

                    BlocBuilder<
                      DepositAccountsAndAmountBloc,
                      DepositAccountsAndAmountState
                    >(
                      buildWhen:
                          (previous, current) =>
                              previous.selectedAccount !=
                                  current.selectedAccount ||
                              previous.errorMessage != current.errorMessage ||
                              previous.isButtonLoading !=
                                  current.isButtonLoading,
                      builder: (builderContext, state) {
                        return state.selectedAccount != null
                            ? DepositWithdrawAmountConversionWidget(
                              args: (
                                account: state.selectedAccount!,
                                accountCurrency:
                                    state.selectedAccount!.homeCurrency,
                                selectedCurrency:
                                    AmountConversionUtils.getSelectedCurrency(
                                      selectedCurrency: state.selectedCurrency,
                                      accountCurrency:
                                          state.selectedAccount!.homeCurrency,
                                      defaultCurrency:
                                          paymentMethod.defaultCurrency,
                                      currencyAmountDetails:
                                          paymentMethod.currencyAmountDetails,
                                    ),
                                currencyMinMaxSuggestedAmountList:
                                    paymentMethod.currencyAmountDetails ?? [],
                                currencies: paymentMethod.currencies ?? [],
                                showSuggestedAmounts: true,
                                externalErrorMessage: state.errorMessage,
                                isStartWithConversionRate:
                                    AmountConversionUtils.getIsStartWithConversionRate(
                                      accountCurrency:
                                          state.selectedAccount!.homeCurrency,
                                      currencyAmountDetails:
                                          paymentMethod.currencyAmountDetails,
                                    ),
                                paymentType: PaymentType.deposit,
                                premierAccountMinAmountForDeposit:
                                    paymentMethod.premierAccountMinAmount,
                                isInputDisabled: state.isButtonLoading,
                              ),
                              callback: ({
                                required String accountCurrencyAmount,
                                required String selectedCurrencyAmount,
                                String? selectedCurrency,
                                required bool isAmountValid,
                                required RatesModel?
                                conversionRateSelectedToAccountCurrency,
                                String? conversionRateString,
                                required ConversionRateModel?
                                conversionRateData,
                              }) {
                                builderContext
                                    .read<DepositAccountsAndAmountBloc>()
                                    .add(
                                      DepositAccountsAndAmountEvent.changeButtonState(
                                        isAmountValid,
                                      ),
                                    );
                                if (isAmountValid) {
                                  builderContext
                                      .read<DepositAccountsAndAmountBloc>()
                                      .add(
                                        DepositAccountsAndAmountEvent.onAmountChange(
                                          accountCurrencyAmount,
                                          selectedCurrencyAmount,
                                          conversionRateSelectedToAccountCurrency,
                                          conversionRateString,
                                          selectedCurrency,
                                        ),
                                      );
                                }
                              },
                            )
                            : Container();
                      },
                    ),
                    const SizedBox(height: DuploSpacing.spacing_3xl_24),
                    BlocBuilder<
                      DepositAccountsAndAmountBloc,
                      DepositAccountsAndAmountState
                    >(
                      buildWhen:
                          (previous, current) =>
                              previous.isButtonEnabled !=
                                  current.isButtonEnabled ||
                              previous.isButtonLoading !=
                                  current.isButtonLoading ||
                              previous.errorMessage != current.errorMessage,
                      builder: (builderContext, state) {
                        return switch (paymentMethod.mop) {
                          DepositMop.apple_pay => DuploButton.applePay(
                            isApplePayDarkMode: true,
                            onTap: () {
                              _getDepositDetails(builderContext);
                            },
                            isLoading: state.isButtonLoading,
                            isDisabled: !state.isButtonEnabled,
                          ),
                          DepositMop.google_pay => DuploButton.googlePay(
                            isGooglePayDarkMode: true,
                            onTap: () {
                              _getDepositDetails(builderContext);
                            },
                            isLoading: state.isButtonLoading,
                            isDisabled: !state.isButtonEnabled,
                          ),
                          (_) => DuploButton.defaultPrimary(
                            title: localization.payments_continue,
                            onTap: () {
                              _getDepositDetails(builderContext);
                            },
                            isDisabled:
                                !state.isButtonEnabled ||
                                state.errorMessage != null,
                            isLoading: state.isButtonLoading,
                            trailingIcon: Assets.images.chevronRight.keyName,

                            useFullWidth: true,
                          ),
                        };
                      },
                    ),
                    const SizedBox(height: 20), // Extra space at the bottom
                  ],
                ),
              ),
            ),
          ),
        ),
        if (isFullScreenLoaderEnabled) LoadingView(),
      ],
    );
  }

  void _getDepositDetails(BuildContext ctx) {
    ctx.read<DepositAccountsAndAmountBloc>().add(
      DepositAccountsAndAmountEvent.getDepositDetails(
        paymentMethod: paymentMethod,
        isDarkTheme: diContainer<ThemeManager>().isDarkMode,
      ),
    );
  }
}
