import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/bloc/deposit_withdraw_amount_conversion_bloc.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/widgets/deposit_withdraw_amount_conversion_content.dart';

typedef DepositWithdrawAmountConversionArgs =
    ({
      TradingAccountModel? account,
      String accountCurrency,
      String? selectedCurrency,
      List<CurrencyAmountDetail> currencyMinMaxSuggestedAmountList,
      List<String> currencies,
      bool showSuggestedAmounts,
      bool isStartWithConversionRate,
      String? externalErrorMessage,
      PaymentType paymentType,
      List<PremierAccountMinimumAmountConfiguration>?
      premierAccountMinAmountForDeposit,
      bool isInputDisabled,
    });

/// Callback for amount changes
typedef DepositWithdrawAmountConversionCallback =
    void Function({
      required String selectedCurrencyAmount,
      required String accountCurrencyAmount,
      String? selectedCurrency,
      required RatesModel? conversionRateSelectedToAccountCurrency,
      String? conversionRateString,
      required ConversionRateModel? conversionRateData,
      required bool isAmountValid,
    });

class DepositWithdrawAmountConversionWidget extends StatefulWidget {
  const DepositWithdrawAmountConversionWidget({
    super.key,
    required this.args,
    required this.callback,
  });
  final DepositWithdrawAmountConversionArgs args;
  final DepositWithdrawAmountConversionCallback callback;

  @override
  State<DepositWithdrawAmountConversionWidget> createState() =>
      _DepositWithdrawAmountConversionWidgetState();
}

class _DepositWithdrawAmountConversionWidgetState
    extends State<DepositWithdrawAmountConversionWidget> {
  late DepositWithdrawAmountConversionBloc _bloc;
  late TextEditingController accountCurrencyController;
  late TextEditingController selectedCurrencyController;

  @override
  void initState() {
    super.initState();
    _bloc = diContainer<DepositWithdrawAmountConversionBloc>();
    accountCurrencyController = TextEditingController();
    selectedCurrencyController = TextEditingController();
    // Initialize currency details
    _bloc.add(
      DepositWithdrawAmountConversionEvent.initialize(args: widget.args),
    );
  }

  @override
  void didUpdateWidget(DepositWithdrawAmountConversionWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check if any of the arguments that affect conversion have changed
    final argsChanged = _hasArgsChanged(oldWidget.args, widget.args);
    if (argsChanged) {
      // Reset the widget to default state
      _resetWidget();
      _bloc.add(
        DepositWithdrawAmountConversionEvent.initialize(args: widget.args),
      );
    }
  }

  /// Checks if any arguments that affect conversion have changed
  bool _hasArgsChanged(
    DepositWithdrawAmountConversionArgs oldArgs,
    DepositWithdrawAmountConversionArgs newArgs,
  ) {
    return oldArgs.account != newArgs.account ||
        oldArgs.accountCurrency != newArgs.accountCurrency ||
        oldArgs.selectedCurrency != newArgs.selectedCurrency ||
        oldArgs.isStartWithConversionRate != newArgs.isStartWithConversionRate;
  }

  @override
  void dispose() {
    accountCurrencyController.dispose();
    selectedCurrencyController.dispose();
    _bloc.close();
    super.dispose();
  }

  /// Resets the widget to its default state
  void _resetWidget() {
    // Clear text controllers
    accountCurrencyController.clear();
    selectedCurrencyController.clear();

    // Reset bloc state
    _bloc.add(const DepositWithdrawAmountConversionEvent.reset());
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: DepositWithdrawAmountConversionContent(
        args: widget.args,
        onAmountChange: widget.callback,
        accountCurrencyController: accountCurrencyController,
        selectedCurrencyController: selectedCurrencyController,
      ),
    );
  }
}
