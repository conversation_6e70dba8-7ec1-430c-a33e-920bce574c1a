import 'dart:async';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/analytics/deposit_analytics_event.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';

import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/domain/usecase/conversion_rate_usecase.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/amount_conversion_utils.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/services/amount_conversion_service.dart';

import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/services/amount_validation_service.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/services/currency_service.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/deposit_withdraw_amount_conversion_widget.dart';

part 'deposit_withdraw_amount_conversion_bloc.freezed.dart';
part 'deposit_withdraw_amount_conversion_event.dart';
part 'deposit_withdraw_amount_conversion_state.dart';

class DepositWithdrawAmountConversionBloc
    extends
        Bloc<
          DepositWithdrawAmountConversionEvent,
          DepositWithdrawAmountConversionState
        > {
  DepositWithdrawAmountConversionBloc({
    required ConversionRateUsecase conversionRateUsecase,
    required DepositAnalyticsEvent depositAnalyticsEvent,
    required WithdrawAnalyticsEvent withdrawAnalyticsEvent,
  }) : _conversionRateUsecase = conversionRateUsecase,
       _depositAnalyticsEvent = depositAnalyticsEvent,
       _withdrawAnalyticsEvent = withdrawAnalyticsEvent,
       super(DepositWithdrawAmountConversionState()) {
    on<_Initialize>(_initialize);
    on<_GetCurrencyDetails>(_getCurrencyDetails);
    on<_GetConversionRate>(_getConversionRate);
    on<_OnAccountCurrencyAmountChanged>(_onAccountCurrencyAmountChanged);
    on<_OnSelectedCurrencyAmountChanged>(_onSelectedCurrencyAmountChanged);
    on<_Reset>(_reset);
  }
  final ConversionRateUsecase _conversionRateUsecase;
  final DepositAnalyticsEvent _depositAnalyticsEvent;
  final WithdrawAnalyticsEvent _withdrawAnalyticsEvent;
  CurrencyService _currencyService = const CurrencyServiceImpl();
  AmountConversionService _conversionService =
      const AmountConversionServiceImpl();
  AmountValidationService _validationService =
      const AmountValidationServiceImpl();

  FutureOr<void> _initialize(
    _Initialize event,
    Emitter<DepositWithdrawAmountConversionState> emit,
  ) {
    emit(state.copyWith(args: event.args));
    add(DepositWithdrawAmountConversionEvent.getCurrencyDetails());
  }

  FutureOr<void> _reset(
    DepositWithdrawAmountConversionEvent event,
    Emitter<DepositWithdrawAmountConversionState> emit,
  ) {
    emit(const DepositWithdrawAmountConversionState());
  }

  FutureOr<void> _getCurrencyDetails(
    _GetCurrencyDetails event,
    Emitter<DepositWithdrawAmountConversionState> emit,
  ) {
    try {
      final currencyDetail = _currencyService.getCurrencyDetails(
        currencyAmountDetails: state.args!.currencyMinMaxSuggestedAmountList,
        accountCurrency: state.args!.accountCurrency,
      );

      if (state.args!.isStartWithConversionRate &&
          state.args!.selectedCurrency != null) {
        emit(
          state.copyWith(
            selectedCurrencyMinMaxSuggestedAmountDetail: currencyDetail,
          ),
        );
        add(
          DepositWithdrawAmountConversionEvent.getConversionRate(
            selectedCurrency: state.args!.selectedCurrency!,
          ),
        );
      } else {
        emit(
          state.copyWith(
            selectedCurrencyMinMaxSuggestedAmountDetail: currencyDetail,
            processState: const DepositWithdrawProcessState.success(),
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          processState: DepositWithdrawProcessState.error(
            errorMessage: e.toString(),
          ),
        ),
      );
    }
  }

  Future<void> _getConversionRate(
    _GetConversionRate event,
    Emitter<DepositWithdrawAmountConversionState> emit,
  ) async {
    emit(state.copyWith(processState: DepositWithdrawProcessState.loading()));
    final conversionRate =
        await _conversionRateUsecase(
          sourceCurrency: state.args!.accountCurrency,
          targetCurrency: event.selectedCurrency,
          paymentType: state.args!.paymentType,
        ).run();

    conversionRate.fold(
      (l) {
        if (!isClosed) {
          emit(
            state.copyWith(
              processState: DepositWithdrawProcessState.error(
                errorMessage: l.toString(),
              ),
              isValidAmount: false,
            ),
          );
        }
        addError(l);
      },
      (ConversionRateModel data) {
        if (!isClosed) {
          log("conversionRate: ${data.toJson()}");
          final doesNeedConversion = _currencyService.doesNeedConversion(
            fromCurrency: state.args!.accountCurrency,
            toCurrency: event.selectedCurrency,
          );

          if (state.args!.paymentType == PaymentType.deposit) {
            _depositAnalyticsEvent.depositCurrencyChanged(
              accountCurrency: state.args!.accountCurrency,
              selectedCurrency: event.selectedCurrency,
              conversionRateString: state.conversionRateString ?? '',
              conversionRate:
                  AmountConversionUtils.getSelectedCurrencyRate(
                    conversionRateData: data,
                    selectedCurrency: event.selectedCurrency,
                  )?.rate ??
                  1.0,
            );
          } else if (state.args!.paymentType == PaymentType.withdrawal) {
            _withdrawAnalyticsEvent.withdrawCurrencyChanged(
              accountCurrency: state.args!.accountCurrency,
              selectedCurrency: event.selectedCurrency,
              conversionRateString: state.conversionRateString ?? '',
              conversionRate:
                  AmountConversionUtils.getSelectedCurrencyRate(
                    conversionRateData: data,
                    selectedCurrency: event.selectedCurrency,
                  )?.rate ??
                  1.0,
            );
          }

          emit(
            state.copyWith(
              conversionRateData: data,
              doesNeedConversion: doesNeedConversion,
              selectedCurrency: event.selectedCurrency,
              processState: const DepositWithdrawProcessState.success(),
            ),
          );
        }
      },
    );
  }

  FutureOr<void> _onAccountCurrencyAmountChanged(
    _OnAccountCurrencyAmountChanged event,
    Emitter<DepositWithdrawAmountConversionState> emit,
  ) {
    final conversionResult = _conversionService
        .getAccountToSelectedCurrencyConvertedAmount(
          amount: event.amount,
          fromCurrency: state.selectedCurrency,
          toCurrency: state.args!.accountCurrency,
          conversionRateData: state.conversionRateData,
        );
    AmountValidationResult validationResult;
    if (event.isEmptyField) {
      // If field is empty, consider it invalid for button state but don't show error
      validationResult = AmountValidationResult.invalid(
        AmountValidationError.none,
      );
    } else {
      validationResult = _validationService.validateMinMaxAmountOfCurrency(
        amount: conversionResult,
        currency: state.selectedCurrency ?? state.args!.accountCurrency,
        currencyAmountDetails: state.args!.currencyMinMaxSuggestedAmountList,
      );
    }
    emit(
      state.copyWith(
        selectedCurrencyAmount: conversionResult,
        isValidAmount: validationResult.isValid,
        validationError: validationResult.error,
        processState: const DepositWithdrawProcessState.success(),
      ),
    );
    event.updatedSelectedCurrencyAmount?.call(conversionResult);
  }

  FutureOr<void> _onSelectedCurrencyAmountChanged(
    _OnSelectedCurrencyAmountChanged event,
    Emitter<DepositWithdrawAmountConversionState> emit,
  ) {
    final conversionResult = _conversionService
        .getSelectedCurrencyToAccountConvertedAmount(
          amount: event.amount,
          fromCurrency: state.selectedCurrency,
          toCurrency: state.args!.accountCurrency,
          conversionRateData: state.conversionRateData,
        );
    AmountValidationResult validationResult;
    if (event.isEmptyField) {
      // If field is empty, consider it invalid for button state but don't show error
      validationResult = AmountValidationResult.invalid(
        AmountValidationError.none,
      );
    } else {
      validationResult = _validationService.validateMinMaxAmountOfCurrency(
        amount: event.amount,
        currency: state.selectedCurrency ?? state.args!.accountCurrency,
        currencyAmountDetails: state.args!.currencyMinMaxSuggestedAmountList,
      );
    }
    emit(
      state.copyWith(
        selectedCurrencyAmount: conversionResult,
        isValidAmount: validationResult.isValid,
        validationError: validationResult.error,
        processState: const DepositWithdrawProcessState.success(),
      ),
    );
    event.updatedAccountCurrencyAmount?.call(conversionResult);
  }
}
