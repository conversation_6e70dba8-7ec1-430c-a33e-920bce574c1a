import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/assets/assets.gen.dart' as payments;
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/services/thousands_separator_input_formatter.dart';

import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/bloc/deposit_withdraw_amount_conversion_bloc.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/deposit_withdraw_amount_conversion_widget.dart';
import 'package:prelude/prelude.dart';

class DepositWithdrawAmountFields extends StatelessWidget {
  DepositWithdrawAmountFields({
    super.key,
    required this.args,
    required this.selectedCurrencyController,
    required this.accountCurrencyController,
  });
  final DepositWithdrawAmountConversionArgs args;
  final TextEditingController selectedCurrencyController;
  final TextEditingController accountCurrencyController;
  String? selectedCurrency;

  Widget _buildAccountAmountField(
    BuildContext builderContext,
    DepositWithdrawAmountConversionState state,
    EquitiLocalization localization,
    String locale,
    BuildContext context,
  ) {
    final bloc = builderContext.read<DepositWithdrawAmountConversionBloc>();

    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        DuploTextField(
          key: const Key('transfer_amount_field'),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          label: _returnAccountCurrencyFieldLabel(
            paymentType: args.paymentType,
            localization: localization,
            doesNeedConversion: state.doesNeedConversion,
          ),
          hint: _returnAccountCurrencyFieldLabel(
            paymentType: args.paymentType,
            localization: localization,
            doesNeedConversion: state.doesNeedConversion,
          ),
          readOnly: args.isInputDisabled,
          controller: accountCurrencyController,
          scrollPadding: const EdgeInsets.only(bottom: 50),
          inputFormatters: const [ThousandsSeparatorInputFormatter()],
          onChanged: (amount) {
            // Remove commas before parsing to get the actual numeric value
            final numericValue = amount.replaceAll(',', '');
            final amountInNum = double.tryParse(numericValue) ?? 0.0;
            final isEmpty = numericValue.trim().isEmpty;
            bloc.add(
              DepositWithdrawAmountConversionEvent.onAccountCurrencyAmountChanged(
                amount: amountInNum,
                isEmptyField: isEmpty,
                updatedSelectedCurrencyAmount: (amountInSelectedCurrency) {
                  selectedCurrencyController
                      .text = EquitiFormatter.formatNumber(
                    value: amountInSelectedCurrency,
                    locale: locale,
                  ).replaceAll(',', "");
                },
              ),
            );
          },
        ),
        state.doesNeedConversion
            ? Padding(
              padding: const EdgeInsets.all(8.0),
              child: Align(
                alignment:
                    Directionality.of(context) == TextDirection.ltr
                        ? Alignment.bottomRight
                        : Alignment.bottomLeft,
                child: DuploTagContainer.md(
                  leading: FlagProvider.getFlagFromCurrencyCode(
                    args.accountCurrency,
                  ),
                  text: args.accountCurrency,
                ),
              ),
            )
            : Padding(
              padding: const EdgeInsets.all(8.0),
              child: Align(
                alignment:
                    Directionality.of(context) == TextDirection.ltr
                        ? Alignment.bottomRight
                        : Alignment.bottomLeft,
                child: DuploTap(
                  key: const Key('transfer_currency_Inkwell'),
                  child: DuploTagContainer.md(
                    leading: FlagProvider.getFlagFromCurrencyCode(
                      args.accountCurrency,
                    ),
                    text: args.accountCurrency,
                    trailing:
                        args.paymentType == PaymentType.transfer
                            ? null
                            : payments.Assets.images.chevronDown.svg(),
                  ),
                  onTap:
                      args.paymentType == PaymentType.transfer
                          ? null
                          : () {
                            DuploDropDown.customBottomSheetSelector(
                              context: builderContext,
                              bottomSheetTitle:
                                  localization.payments_selectCurrency,
                              items:
                                  args.currencies
                                      .map(
                                        (element) => DropDownItemModel(
                                          title: element,
                                          image:
                                              FlagProvider.getFlagFromCurrencyCode(
                                                element,
                                              ),
                                        ),
                                      )
                                      .toList(),
                              selectedIndex: args.currencies.indexWhere(
                                (element) => element == args.accountCurrency,
                              ),
                              onChanged: (index) {
                                selectedCurrency = args.currencies
                                    .elementAtOrNull(index);
                                accountCurrencyController.clear();
                                selectedCurrencyController.clear();
                                assert(
                                  selectedCurrency != null,
                                  'selectedCurrency is null',
                                );
                                builderContext
                                    .read<DepositWithdrawAmountConversionBloc>()
                                    .add(
                                      DepositWithdrawAmountConversionEvent.getConversionRate(
                                        selectedCurrency: selectedCurrency!,
                                      ),
                                    );
                              },
                            );
                          },
                ),
              ),
            ),
      ],
    );
  }

  /// Builds the converted amount field
  Widget _buildSelectedCurrencyAmountField(
    BuildContext builderContext,
    DepositWithdrawAmountConversionState state,
    EquitiLocalization localization,
    String locale,
    BuildContext context,
  ) {
    final bloc = builderContext.read<DepositWithdrawAmountConversionBloc>();
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        DuploTextField(
          key: const Key('converted_amount_field'),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          label: _returnSelectedCurrencyFieldLabel(
            localization: localization,
            paymentType: args.paymentType,
          ),
          hint: _returnSelectedCurrencyFieldLabel(
            localization: localization,
            paymentType: args.paymentType,
          ),
          readOnly: args.isInputDisabled,
          controller: selectedCurrencyController,
          inputFormatters: const [ThousandsSeparatorInputFormatter()],
          scrollPadding: const EdgeInsets.only(bottom: 50),
          onChanged: (amount) {
            final numericValue = amount.replaceAll(',', '');
            final amountInNum = double.tryParse(numericValue) ?? 0.0;
            final isEmpty = numericValue.trim().isEmpty;
            bloc.add(
              DepositWithdrawAmountConversionEvent.onSelectedCurrencyAmountChanged(
                amount: amountInNum,
                isEmptyField: isEmpty,
                updatedAccountCurrencyAmount: (amountInAccountCurrency) {
                  accountCurrencyController.text = EquitiFormatter.formatNumber(
                    value: amountInAccountCurrency,
                    locale: locale,
                  ).replaceAll(',', "");
                },
              ),
            );
          },
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Align(
            alignment:
                Directionality.of(context) == TextDirection.ltr
                    ? Alignment.bottomRight
                    : Alignment.bottomLeft,
            child: InkWell(
              key: const Key('converted_currency_selector'),
              child: DuploTagContainer.md(
                leading: FlagProvider.getFlagFromCurrencyCode(
                  state.selectedCurrency ?? '',
                ),
                text: state.selectedCurrency ?? '',
                trailing:
                    args.paymentType == PaymentType.transfer
                        ? null
                        : payments.Assets.images.chevronDown.svg(),
              ),
              onTap:
                  args.paymentType == PaymentType.transfer
                      ? null
                      : () {
                        DuploDropDown.customBottomSheetSelector(
                          context: builderContext,
                          bottomSheetTitle:
                              localization.payments_selectCurrency,
                          items:
                              args.currencies
                                  .map(
                                    (element) => DropDownItemModel(
                                      title: element,
                                      image:
                                          FlagProvider.getFlagFromCurrencyCode(
                                            element,
                                          ),
                                    ),
                                  )
                                  .toList(),
                          selectedIndex: args.currencies.indexWhere(
                            (element) => element == state.selectedCurrency,
                          ),
                          onChanged: (index) {
                            selectedCurrency = args.currencies.elementAtOrNull(
                              index,
                            );
                            //added because when we prefill info and then change currency it doesn't fire callback so getting old value
                            accountCurrencyController.clear();
                            selectedCurrencyController.clear();
                            assert(
                              selectedCurrency != null,
                              'selectedCurrency is null',
                            );
                            bloc.add(
                              DepositWithdrawAmountConversionEvent.getConversionRate(
                                selectedCurrency: selectedCurrency!,
                              ),
                            );
                          },
                        );
                      },
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context).toString();
    final localization = EquitiLocalization.of(context);
    selectedCurrency ??= args.selectedCurrency;

    return BlocBuilder<
      DepositWithdrawAmountConversionBloc,
      DepositWithdrawAmountConversionState
    >(
      buildWhen: (previous, current) => previous != current,
      builder: (blocCtx, state) {
        return Stack(
          alignment: Alignment.center,
          children: [
            Column(
              children: [
                if (state.doesNeedConversion) ...[
                  _buildSelectedCurrencyAmountField(
                    blocCtx,
                    state,
                    localization,
                    locale,
                    context,
                  ),
                  const SizedBox(height: DuploSpacing.spacing_md_8),
                ],
                _buildAccountAmountField(
                  blocCtx,
                  state,
                  localization,
                  locale,
                  context,
                ),
              ],
            ),
            state.doesNeedConversion
                ? Container(
                  height: 40,
                  width: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      DuploRadius.radius_full_9999,
                    ),
                    color: context.duploTheme.background.bgTertiary,
                  ),
                  child: Center(
                    child: payments.Assets.images.refreshCcw.svg(
                      height: 20,
                      width: 20,
                      colorFilter: ColorFilter.mode(
                        context.duploTheme.icon.iconFeaturedLightFgGray,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                )
                : Container(),
          ],
        );
      },
    );
  }

  String _returnSelectedCurrencyFieldLabel({
    required PaymentType paymentType,
    required EquitiLocalization localization,
  }) {
    switch (paymentType) {
      case PaymentType.deposit:
        return localization.payments_toBeDeposited;
      case PaymentType.withdrawal:
        return localization.payments_withdrawalAmount;
      case PaymentType.transfer:
        return localization.payments_convertedAmount;
    }
  }

  String _returnAccountCurrencyFieldLabel({
    required PaymentType paymentType,
    required EquitiLocalization localization,
    required bool doesNeedConversion,
  }) {
    if (doesNeedConversion) {
      switch (paymentType) {
        case PaymentType.deposit:
          return localization.payments_amount;
        case PaymentType.withdrawal:
          return localization.payments_convertedAmount;
        case PaymentType.transfer:
          return localization.payments_transferAmount;
      }
    } else {
      switch (paymentType) {
        case PaymentType.transfer:
          return localization.payments_transferAmount;
        case PaymentType.deposit:
        case PaymentType.withdrawal:
          return localization.payments_addAmount;
      }
    }
  }
}
