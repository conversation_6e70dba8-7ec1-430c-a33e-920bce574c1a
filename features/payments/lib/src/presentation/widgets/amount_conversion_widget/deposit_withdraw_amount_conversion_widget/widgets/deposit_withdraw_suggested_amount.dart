import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/analytics/deposit_analytics_event.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/amount_conversion_utils.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/bloc/deposit_withdraw_amount_conversion_bloc.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/deposit_withdraw_amount_conversion_widget.dart';
import 'package:prelude/prelude.dart';

class DepositWithdrawSuggestedAmount extends StatelessWidget {
  const DepositWithdrawSuggestedAmount({
    super.key,
    required this.args,
    required this.selectedCurrencyController,
    required this.accountCurrencyController,
    this.selectedCurrency,
  });
  final DepositWithdrawAmountConversionArgs args;
  final TextEditingController selectedCurrencyController;
  final TextEditingController accountCurrencyController;
  final String? selectedCurrency;

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context).toString();
    final selectedCurrencyMinMaxSuggestedAmount =
        AmountConversionUtils.getMinAndMaxAmountOfCurrency(
          currencyMinMaxSuggestedAmountList:
              args.currencyMinMaxSuggestedAmountList,
          currency: selectedCurrency ?? args.accountCurrency,
        );

    return BlocBuilder<
      DepositWithdrawAmountConversionBloc,
      DepositWithdrawAmountConversionState
    >(
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, state) {
        return (selectedCurrencyMinMaxSuggestedAmount
                    ?.suggestedAmounts
                    ?.isNotEmpty ??
                false)
            ? _buildSuggestedAmountsLayout(
              builderContext: builderContext,
              state: state,
              locale: locale,
              selectedCurrencyMinMaxSuggestedAmount:
                  selectedCurrencyMinMaxSuggestedAmount!,
            )
            : Container();
      },
    );
  }

  Widget _buildSuggestedAmountsLayout({
    required BuildContext builderContext,
    required DepositWithdrawAmountConversionState state,
    required String locale,
    required CurrencyAmountDetail selectedCurrencyMinMaxSuggestedAmount,
  }) {
    final suggestedAmounts =
        selectedCurrencyMinMaxSuggestedAmount.suggestedAmounts!;
    final itemCount = suggestedAmounts.length;

    // For 3 or 4 items, use Row with Expanded to fill full width
    if (itemCount == 3) {
      final widgets = <Widget>[];
      for (int i = 0; i < suggestedAmounts.length; i++) {
        widgets.add(
          Expanded(
            child: _buildSuggestedAmountButton(
              context: builderContext,
              state: state,
              index: i,
              locale: locale,
              selectedCurrencyMinMaxSuggestedAmount:
                  selectedCurrencyMinMaxSuggestedAmount,
            ),
          ),
        );
        // Add spacing between items (but not after the last item)
        if (i < suggestedAmounts.length - 1) {
          widgets.add(const SizedBox(width: DuploSpacing.spacing_md_8));
        }
      }
      // Wrap Row with Directionality to force LTR direction
      return Directionality(
        textDirection: TextDirection.ltr,
        child: Row(children: widgets),
      );
    }

    // For other cases, use Wrap with center alignment
    return Center(
      child: Wrap(
        alignment: WrapAlignment.center,
        spacing: DuploSpacing.spacing_xl_16,
        runSpacing: DuploSpacing.spacing_md_8,
        children:
            suggestedAmounts
                .asMap()
                .entries
                .map(
                  (entry) => _buildSuggestedAmountButton(
                    context: builderContext,
                    state: state,
                    index: entry.key,
                    locale: locale,
                    selectedCurrencyMinMaxSuggestedAmount:
                        selectedCurrencyMinMaxSuggestedAmount,
                  ),
                )
                .toList(),
      ),
    );
  }

  Widget _buildSuggestedAmountButton({
    required BuildContext context,
    required DepositWithdrawAmountConversionState state,
    required int index,
    required String locale,
    required CurrencyAmountDetail selectedCurrencyMinMaxSuggestedAmount,
  }) {
    final suggestedAmount = selectedCurrencyMinMaxSuggestedAmount
        .suggestedAmounts
        ?.elementAtOrNull(index);

    if (suggestedAmount == null) {
      return const SizedBox.shrink();
    }

    return FittedBox(
      fit: BoxFit.scaleDown,
      child: DuploTap(
        child: DuploTagContainer.lg(
          text:
              "${EquitiFormatter.formatNumber(value: suggestedAmount, locale: locale)} ${selectedCurrencyMinMaxSuggestedAmount.currency}",
        ),
        onTap: () {
          if (args.isInputDisabled) return;
          if (args.paymentType == PaymentType.deposit) {
            diContainer<DepositAnalyticsEvent>().depositSuggestedAmountSelected(
              amount: suggestedAmount,
              currency: selectedCurrencyMinMaxSuggestedAmount.currency,
            );
          } else if (args.paymentType == PaymentType.withdrawal) {
            diContainer<WithdrawAnalyticsEvent>()
                .withdrawSuggestedAmountSelected(
                  amount: EquitiFormatter.formatToString(
                    value: suggestedAmount,
                  ),
                  currency: selectedCurrencyMinMaxSuggestedAmount.currency,
                );
          }

          final value = EquitiFormatter.formatNumber(
            value: suggestedAmount,
            locale: locale,
          ).replaceAll(',', "");
          final bloc = context.read<DepositWithdrawAmountConversionBloc>();
          final amountInDouble = double.tryParse(value) ?? 0.0;
          if (state.doesNeedConversion) {
            selectedCurrencyController.text = value;
            bloc.add(
              DepositWithdrawAmountConversionEvent.onSelectedCurrencyAmountChanged(
                amount: amountInDouble,
                isEmptyField: false,
                updatedAccountCurrencyAmount: (amountInSelectedCurrency) {
                  accountCurrencyController.text = EquitiFormatter.formatNumber(
                    value: amountInSelectedCurrency,
                    locale: locale,
                  ).replaceAll(',', "");
                },
              ),
            );
          } else {
            accountCurrencyController.text = value;
            bloc.add(
              DepositWithdrawAmountConversionEvent.onAccountCurrencyAmountChanged(
                amount: amountInDouble,
                isEmptyField: false,
                updatedSelectedCurrencyAmount: (amountInSelectedCurrency) {
                  selectedCurrencyController
                      .text = EquitiFormatter.formatNumber(
                    value: amountInSelectedCurrency,
                    locale: locale,
                  ).replaceAll(',', "");
                },
              ),
            );
          }
        },
      ),
    );
  }
}
