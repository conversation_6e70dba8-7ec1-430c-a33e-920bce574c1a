import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/widgets.dart';
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';
import 'package:prelude/prelude.dart';

class DepositWithdrawAmountConversionLoadingView extends StatelessWidget {
  const DepositWithdrawAmountConversionLoadingView({
    super.key,
    required this.accountCurrency,
    required this.txnLimit,
    this.selectedCurrencyMinMaxSuggestedAmountDetail,
  });
  final String accountCurrency;
  final String txnLimit;
  final CurrencyAmountDetail? selectedCurrencyMinMaxSuggestedAmountDetail;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final duploTextStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DuploText(
          text: localization.payments_amount,
          style: duploTextStyles.textLg,
          fontWeight: DuploFontWeight.semiBold,
          color: theme.text.textPrimary,
        ),
        SizedBox(height: DuploSpacing.spacing_md_8),
        DuploText(
          text: txnLimit,
          style: duploTextStyles.textSm,
          fontWeight: DuploFontWeight.regular,
          color: theme.text.textSecondary,
        ),
        SizedBox(height: DuploSpacing.spacing_3xl_24),

        // Transfer Amount Field Shimmer
        _buildTextFieldShimmer(context),

        const SizedBox(height: DuploSpacing.spacing_sm_6),

        // Conversion Rate Text Shimmer
        _buildConversionRateShimmer(context),

        const SizedBox(height: DuploSpacing.spacing_lg_12),

        // Suggested Amounts Shimmer
        _buildSuggestedAmountsShimmer(context),
      ],
    );
  }

  Widget _buildConversionRateShimmer(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: DuploSpacing.spacing_xl_16,
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: _buildShimmerContainer(
          context: context,
          height: 14,
          width: 180,
          borderRadius: DuploRadius.radius_sm_6,
        ),
      ),
    );
  }

  Widget _buildTextFieldShimmer(BuildContext context) {
    final theme = context.duploTheme;

    return Stack(
      children: [
        // Text field container shimmer
        Container(
          height: 70,
          width: double.infinity,
          margin: const EdgeInsets.symmetric(
            horizontal: DuploSpacing.spacing_md_8,
          ),
          decoration: BoxDecoration(
            border: Border.all(color: theme.border.borderSecondary),
            borderRadius: BorderRadius.circular(DuploRadius.radius_md_8),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildShimmerContainer(
                  context: context,
                  height: 24,
                  width: 120,
                  borderRadius: DuploRadius.radius_md_8,
                ),
              ],
            ),
          ),
        ),

        // Currency tag shimmer (positioned like the real tag)
        Positioned(
          right: 20,
          bottom: 6,
          child: _buildShimmerContainer(
            context: context,
            height: 24,
            width: 60,
            borderRadius: DuploRadius.radius_md_8,
          ),
        ),
      ],
    );
  }

  Widget _buildSuggestedAmountsShimmer(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: DuploSpacing.spacing_md_8,
      ),
      child: Row(
        children:
            List.generate(
              3,
              (index) => [
                Expanded(child: _buildSuggestedAmountButton(context)),
                if (index < 2) const SizedBox(width: DuploSpacing.spacing_md_8),
              ],
            ).expand((element) => element).toList(),
      ),
    );
  }

  Widget _buildSuggestedAmountButton(BuildContext context) {
    final theme = context.duploTheme;

    return Container(
      height: 28,
      decoration: BoxDecoration(
        border: Border.all(color: theme.border.borderSecondary),
        borderRadius: BorderRadius.circular(DuploRadius.radius_md_8),
      ),
      padding: const EdgeInsets.symmetric(
        vertical: DuploSpacing.spacing_xs_4,
        horizontal: DuploSpacing.spacing_lg_12,
      ),
      child: DuploShimmer(
        child: Container(
          decoration: BoxDecoration(
            color: theme.background.bgPrimary,
            borderRadius: BorderRadius.circular(DuploRadius.radius_xs_4),
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerContainer({
    required BuildContext context,
    required double height,
    required double width,
    required double borderRadius,
  }) {
    return DuploShimmer(
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          color: context.duploTheme.background.bgPrimary,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
    );
  }
}
