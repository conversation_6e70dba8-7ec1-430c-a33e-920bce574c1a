import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/transfer_amount_conversion_widget/bloc/transfer_amount_conversion_bloc.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/transfer_amount_conversion_widget/widgets/transfer_amount_conversion_content.dart';

typedef TransferAmountConversionArgs =
    ({
      TradingAccountModel? account,
      String sourceCurrency,
      String? destinationCurrency,
      List<CurrencyAmountDetail> currencyMinMaxSuggestedAmountList,
      List<String> currencies,
      bool showSuggestedAmounts,
      bool isStartWithConversionRate,
      String? externalErrorMessage,
      PaymentType paymentType,
      bool isInputDisabled,
    });

/// Callback for amount changes
typedef TransferAmountConversionCallback =
    void Function({
      required String amountInSourceCurrency,
      required String amountInDestinationCurrency,
      String? selectedCurrency,
      required RatesModel? conversionRateToDestinationCurrency,
      String? conversionRateString,
      required ConversionRateModel? conversionRateData,
      required bool isAmountValid,
    });

class TransferAmountConversionWidget extends StatefulWidget {
  const TransferAmountConversionWidget({
    super.key,
    required this.args,
    required this.callback,
  });
  final TransferAmountConversionArgs args;
  final TransferAmountConversionCallback callback;

  @override
  State<TransferAmountConversionWidget> createState() =>
      _TransferAmountConversionWidgetState();
}

class _TransferAmountConversionWidgetState
    extends State<TransferAmountConversionWidget> {
  late TransferAmountConversionBloc _bloc;
  late TextEditingController sourceCurrencyController;
  late TextEditingController destinationCurrencyController;

  @override
  void initState() {
    super.initState();
    _bloc = diContainer<TransferAmountConversionBloc>();
    sourceCurrencyController = TextEditingController();
    destinationCurrencyController = TextEditingController();
    // Initialize currency details
    _bloc.add(TransferAmountConversionEvent.initialize(args: widget.args));
  }

  @override
  void didUpdateWidget(TransferAmountConversionWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check if any of the arguments that affect conversion have changed
    final argsChanged = _hasArgsChanged(oldWidget.args, widget.args);
    if (argsChanged) {
      // Reset the widget to default state
      _resetWidget();
      _bloc.add(TransferAmountConversionEvent.initialize(args: widget.args));
    }
  }

  /// Checks if any arguments that affect conversion have changed
  bool _hasArgsChanged(
    TransferAmountConversionArgs oldArgs,
    TransferAmountConversionArgs newArgs,
  ) {
    return oldArgs.account != newArgs.account ||
        oldArgs.sourceCurrency != newArgs.sourceCurrency ||
        oldArgs.destinationCurrency != newArgs.destinationCurrency ||
        oldArgs.isStartWithConversionRate != newArgs.isStartWithConversionRate;
  }

  @override
  void dispose() {
    sourceCurrencyController.dispose();
    destinationCurrencyController.dispose();
    _bloc.close();
    super.dispose();
  }

  /// Resets the widget to its default state
  void _resetWidget() {
    // Clear text controllers
    sourceCurrencyController.clear();
    destinationCurrencyController.clear();

    // Reset bloc state
    _bloc.add(const TransferAmountConversionEvent.reset());
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: TransferAmountConversionContent(
        args: widget.args,
        onAmountChange: widget.callback,
        sourceCurrencyController: sourceCurrencyController,
        destinationCurrencyController: destinationCurrencyController,
      ),
    );
  }
}
