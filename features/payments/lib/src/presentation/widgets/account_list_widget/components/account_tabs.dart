import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/bloc/account_list_bloc.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/components/tap_bar_style.dart';

/// Tab bar component for switching between accounts and wallets
class AccountTabs extends StatelessWidget {
  const AccountTabs({
    required this.tabController,
    required this.tradingAccountsCount,
    required this.walletAccountsCount,
    required this.isInputDisabled,
    super.key,
  });

  final TabController tabController;
  final int tradingAccountsCount;
  final int walletAccountsCount;
  final bool isInputDisabled;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final localization = EquitiLocalization.of(context);

    return Container(
      height: 55,
      child: Material(
        color: theme.background.bgSecondaryAlt,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(color: theme.border.borderSecondary, width: 1),
        ),
        child: IgnorePointer(
          ignoring: isInputDisabled,
          child: TabBar(
            controller: tabController,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.black87,
            indicatorSize: TabBarIndicatorSize.tab,
            dividerColor: Colors.transparent,
            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 5),
            indicator: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: theme.utility.utilityGray800,
            ),
            tabs: [
              BlocBuilder<AccountListBloc, AccountListState>(
                buildWhen:
                    (previous, current) =>
                        previous.currentTabIndex != current.currentTabIndex,
                builder: (buildeContext, builderState) {
                  return TapBarStyle(
                    count: tradingAccountsCount,
                    title: localization.payments_accounts,
                    isSelected: builderState.currentTabIndex == 0,
                  );
                },
              ),
              BlocBuilder<AccountListBloc, AccountListState>(
                buildWhen:
                    (previous, current) =>
                        previous.currentTabIndex != current.currentTabIndex,
                builder: (buildeContext, builderState) {
                  return TapBarStyle(
                    count: walletAccountsCount,
                    title: localization.payments_wallets,
                    isSelected: builderState.currentTabIndex == 1,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
