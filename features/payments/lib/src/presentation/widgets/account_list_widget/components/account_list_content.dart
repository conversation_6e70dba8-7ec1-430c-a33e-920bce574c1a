import 'package:domain/domain.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/account_list_widget.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/bloc/account_list_bloc.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/components/account_tabs.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/components/accounts_list.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/components/empty_state.dart';

/// Main content component for account list
class AccountListContent extends StatefulWidget {
  const AccountListContent({
    required this.args,
    required this.onAccountSelected,
    required this.tabController,
    super.key,
  });

  final AccountListArgs args;
  final AccountSelectionCallback onAccountSelected;
  final TabController tabController;

  @override
  State<AccountListContent> createState() => _AccountListContentState();
}

class _AccountListContentState extends State<AccountListContent> {
  late VoidCallback _onTabAnimationChanged;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _setupTabListener();
  }

  @override
  void dispose() {
    widget.tabController.animation?.removeListener(_onTabAnimationChanged);
    super.dispose();
  }

  void _setupTabListener() {
    _onTabAnimationChanged = () {
      final index = widget.tabController.animation?.value.round() ?? 0;
      if (_currentIndex != index && !widget.args.isInputDisabled) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          setState(() {
            _currentIndex = index;
          });
        });
        context.read<AccountListBloc>().add(AccountListEvent.changeTab(index));
      }
    };
    widget.tabController.animation?.addListener(_onTabAnimationChanged);
  }

  List<TradingAccountModel> _filterAccounts(
    List<TradingAccountModel> accounts,
  ) {
    if (widget.args.excludeAccountNumber == null) {
      return accounts;
    }
    return accounts
        .where(
          (account) =>
              account.accountNumber != widget.args.excludeAccountNumber,
        )
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountListBloc, AccountListState>(
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, state) {
        if (state.accountsData == null) {
          return const SizedBox.shrink();
        }

        final tradingAccounts = _filterAccounts(state.allTradingAccounts);
        final walletAccounts = _filterAccounts(state.allWalletAccounts);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AccountTabs(
              tabController:
                  widget.tabController..index = state.currentTabIndex,
              tradingAccountsCount: tradingAccounts.length,
              walletAccountsCount: walletAccounts.length,
              isInputDisabled: widget.args.isInputDisabled,
            ),
            const SizedBox(height: 16),
            BlocBuilder<AccountListBloc, AccountListState>(
              buildWhen:
                  (previous, current) =>
                      previous.currentTabIndex != current.currentTabIndex ||
                      previous.selectedAccount != current.selectedAccount,
              builder: (buildeContext, builderState) {
                return AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child:
                      _currentIndex == 0
                          ? (tradingAccounts.isEmpty
                              ? EmptyState(
                                isWallet: false,
                                onCreateAccount: () {
                                  buildeContext.read<AccountListBloc>().add(
                                    const AccountListEvent.navigateToCreateAccount(),
                                  );
                                },
                                onCreateWallet: () {
                                  buildeContext.read<AccountListBloc>().add(
                                    const AccountListEvent.navigateToCreateWallet(),
                                  );
                                },
                              )
                              : AccountsList(
                                key: const ValueKey('trading_accounts'),
                                accounts: tradingAccounts,
                                isWallet: false,
                                selectedAccount: builderState.selectedAccount,
                                onAccountSelected: widget.onAccountSelected,
                                isInputDisabled: widget.args.isInputDisabled,
                              ))
                          : (walletAccounts.isEmpty
                              ? EmptyState(
                                isWallet: true,
                                onCreateAccount: () {
                                  buildeContext.read<AccountListBloc>().add(
                                    const AccountListEvent.navigateToCreateAccount(),
                                  );
                                },
                                onCreateWallet: () {
                                  buildeContext.read<AccountListBloc>().add(
                                    const AccountListEvent.navigateToCreateWallet(),
                                  );
                                },
                              )
                              : AccountsList(
                                key: const ValueKey('wallet_accounts'),
                                accounts: walletAccounts,
                                isWallet: true,
                                selectedAccount: builderState.selectedAccount,
                                onAccountSelected: widget.onAccountSelected,
                                isInputDisabled: widget.args.isInputDisabled,
                              )),
                );
              },
            ),
          ],
        );
      },
    );
  }
}
