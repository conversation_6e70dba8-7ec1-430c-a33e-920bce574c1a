import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/assets/assets.gen.dart' as assets;

/// Empty state component for when no accounts/wallets are found
class EmptyState extends StatelessWidget {
  const EmptyState({
    required this.isWallet,
    required this.onCreateAccount,
    required this.onCreateWallet,
    super.key,
  });

  final bool isWallet;
  final VoidCallback onCreateAccount;
  final VoidCallback onCreateWallet;

  @override
  Widget build(BuildContext context) {
    final style = context.duploTextStyles;
    final theme = context.duploTheme;
    return Container(
      key: ValueKey<bool>(isWallet),
      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 0.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              isWallet
                  ? assets.Assets.images.noWalletsFound.svg()
                  : assets.Assets.images.noAccountsFound.svg(),
              const SizedBox(height: 24),
              DuploText(
                text:
                    isWallet
                        ? EquitiLocalization.of(
                          context,
                        ).payments_no_wallets_found
                        : EquitiLocalization.of(
                          context,
                        ).payments_no_accounts_found,
                style: style.textLg,
                fontWeight: DuploFontWeight.semiBold,
                color: theme.text.textPrimary,
              ),
              const SizedBox(height: 40),
              DuploButton.defaultPrimary(
                useFullWidth: true,
                title:
                    isWallet
                        ? EquitiLocalization.of(context).payments_add_new_wallet
                        : EquitiLocalization.of(
                          context,
                        ).payments_add_new_account,
                onTap: () {
                  if (isWallet) {
                    onCreateWallet();
                    return;
                  }
                  onCreateAccount();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
