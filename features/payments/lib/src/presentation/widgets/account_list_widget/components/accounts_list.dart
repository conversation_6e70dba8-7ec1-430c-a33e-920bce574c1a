import 'package:domain/domain.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/account_list_widget.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/bloc/account_list_bloc.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/components/account_tile.dart';

/// List component for displaying accounts
class AccountsList extends StatelessWidget {
  const AccountsList({
    required this.accounts,
    required this.isWallet,
    required this.selectedAccount,
    required this.onAccountSelected,
    required this.isInputDisabled,
    super.key,
  });

  final List<TradingAccountModel> accounts;
  final bool isWallet;
  final TradingAccountModel? selectedAccount;
  final AccountSelectionCallback onAccountSelected;
  final bool isInputDisabled;

  @override
  Widget build(BuildContext context) {
    return Column(
      children:
          accounts.map((account) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: AccountTile(
                account: account,
                isWallet: isWallet,
                isSelected:
                    account.accountNumber == selectedAccount?.accountNumber,
                onTap: () {
                  if (!isInputDisabled) {
                    context.read<AccountListBloc>().add(
                      AccountListEvent.selectAccount(account),
                    );
                    onAccountSelected(account);
                  }
                },
              ),
            );
          }).toList(),
    );
  }
}
