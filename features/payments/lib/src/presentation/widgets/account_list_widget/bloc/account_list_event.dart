part of 'account_list_bloc.dart';

@freezed
sealed class AccountListEvent with _$AccountListEvent {
  const factory AccountListEvent.loadAccounts() = _LoadAccounts;
  const factory AccountListEvent.selectAccount(TradingAccountModel account) =
      _SelectAccount;
  const factory AccountListEvent.changeTab(int tabIndex) = _ChangeTab;
  const factory AccountListEvent.navigateToCreateAccount() =
      _NavigateToCreateAccount;
  const factory AccountListEvent.navigateToCreateWallet() =
      _NavigateToCreateWallet;
}
