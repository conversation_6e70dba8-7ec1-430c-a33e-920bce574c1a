import 'dart:async';

import 'package:domain/domain.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/domain/usecase/list_of_account_use_case.dart';
import 'package:payment/src/domain/usecase/get_selected_account_id_use_case.dart';
import 'package:payment/src/navigation/payment_navigation.dart';

part 'account_list_bloc.freezed.dart';
part 'account_list_event.dart';
part 'account_list_state.dart';

/// Bloc responsible for managing account list state
class AccountListBloc extends Bloc<AccountListEvent, AccountListState> {
  AccountListBloc({
    required ListOfAccountUseCase listOfAccountUseCase,
    required PaymentNavigation paymentNavigation,
    required GetSelectedAccountIdUseCase getSelectedAccountIdUseCase,
  }) : _listOfAccountUseCase = listOfAccountUseCase,
       _paymentNavigation = paymentNavigation,
       _getSelectedAccountIdUseCase = getSelectedAccountIdUseCase,
       super(const AccountListState()) {
    on<_LoadAccounts>(_loadAccounts);
    on<_SelectAccount>(_selectAccount);
    on<_ChangeTab>(_changeTab);
    on<_NavigateToCreateAccount>(_navigateToCreateAccount);
    on<_NavigateToCreateWallet>(_onNavigateToCreateWallet);
  }

  final ListOfAccountUseCase _listOfAccountUseCase;
  final PaymentNavigation _paymentNavigation;
  final GetSelectedAccountIdUseCase _getSelectedAccountIdUseCase;

  // Constants for tab indices
  static const int _tradingTabIndex = 0;
  static const int _walletTabIndex = 1;

  /// Determines the appropriate tab index based on account type
  int _getTabIndexForAccountType(AccountType? accountType) {
    return switch (accountType) {
      AccountType.landingWallet => _walletTabIndex,
      AccountType.trading || AccountType.unknown || null => _tradingTabIndex,
    };
  }

  /// Determines if auto-selection should happen when switching tabs
  bool _shouldAutoSelectForTab(int tabIndex, AccountType? currentAccountType) {
    return switch (tabIndex) {
      _tradingTabIndex => currentAccountType != AccountType.trading,
      _walletTabIndex => currentAccountType != AccountType.landingWallet,
      _ => true,
    };
  }

  FutureOr<void> _loadAccounts(
    _LoadAccounts event,
    Emitter<AccountListState> emit,
  ) async {
    try {
      emit(
        state.copyWith(processState: const AccountListProcessState.loading()),
      );

      final result = await _listOfAccountUseCase().run();

      result.fold(
        (failure) {
          if (!isClosed) {
            emit(
              state.copyWith(
                processState: AccountListProcessState.error(
                  errorMessage: failure.toString(),
                ),
              ),
            );
          }
        },
        (accountsData) {
          final accounts = List.of(accountsData)..sort((a, b) {
            final aDate =
                DateTime.tryParse(a.dateCreated ?? '') ?? DateTime(1970);
            final bDate =
                DateTime.tryParse(b.dateCreated ?? '') ?? DateTime(1970);
            return bDate.compareTo(aDate);
          });
          if (!isClosed) {
            // Check if there's a previously selected account
            final selectedAccountId = _getSelectedAccountIdUseCase();
            final preSelectedAccount =
                selectedAccountId != null
                    ? accounts
                        .where(
                          (account) => account.recordId == selectedAccountId,
                        )
                        .firstOrNull
                    : null;

            // Determine tab index based on pre-selected account type
            final tabIndex =
                preSelectedAccount != null
                    ? _getTabIndexForAccountType(preSelectedAccount.accountType)
                    : state.currentTabIndex;

            // Determine the account to select
            final accountToSelect = preSelectedAccount ?? accounts.firstOrNull;

            emit(
              state.copyWith(
                processState: const AccountListProcessState.success(),
                accountsData: accounts,
                selectedAccount: accountToSelect,
                currentTabIndex: tabIndex,
                shouldAutoSelect: false,
              ),
            );
          }
        },
      );
    } catch (e) {
      if (!isClosed) {
        emit(
          state.copyWith(
            processState: AccountListProcessState.error(
              errorMessage: e.toString(),
            ),
          ),
        );
      }
    }
  }

  FutureOr<void> _selectAccount(
    _SelectAccount event,
    Emitter<AccountListState> emit,
  ) {
    emit(
      state.copyWith(selectedAccount: event.account, shouldAutoSelect: false),
    );
  }

  FutureOr<void> _changeTab(_ChangeTab event, Emitter<AccountListState> emit) {
    // Determine if auto-selection should happen based on current account type and target tab
    final shouldAutoSelect = _shouldAutoSelectForTab(
      event.tabIndex,
      state.selectedAccount?.accountType,
    );

    emit(
      state.copyWith(
        currentTabIndex: event.tabIndex,
        shouldAutoSelect: shouldAutoSelect,
      ),
    );
  }

  FutureOr<void> _navigateToCreateAccount(
    _NavigateToCreateAccount event,
    Emitter<AccountListState> emit,
  ) {
    // Navigate to create account screen
    _paymentNavigation.navigateToCreateAccountMain(
      createAccountFlow: CreateAccountFlow.additionalLiveAccount,
    );
  }

  FutureOr<void> _onNavigateToCreateWallet(
    _NavigateToCreateWallet event,
    Emitter<AccountListState> emit,
  ) {
    _paymentNavigation.navigateToCreateWallet();
  }
}
