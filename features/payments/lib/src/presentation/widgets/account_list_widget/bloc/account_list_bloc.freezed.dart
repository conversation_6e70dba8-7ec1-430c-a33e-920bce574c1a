// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_list_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AccountListEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountListEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountListEvent()';
}


}

/// @nodoc
class $AccountListEventCopyWith<$Res>  {
$AccountListEventCopyWith(AccountListEvent _, $Res Function(AccountListEvent) __);
}


/// @nodoc


class _LoadAccounts implements AccountListEvent {
  const _LoadAccounts();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadAccounts);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountListEvent.loadAccounts()';
}


}




/// @nodoc


class _SelectAccount implements AccountListEvent {
  const _SelectAccount(this.account);
  

 final  TradingAccountModel account;

/// Create a copy of AccountListEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SelectAccountCopyWith<_SelectAccount> get copyWith => __$SelectAccountCopyWithImpl<_SelectAccount>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SelectAccount&&(identical(other.account, account) || other.account == account));
}


@override
int get hashCode => Object.hash(runtimeType,account);

@override
String toString() {
  return 'AccountListEvent.selectAccount(account: $account)';
}


}

/// @nodoc
abstract mixin class _$SelectAccountCopyWith<$Res> implements $AccountListEventCopyWith<$Res> {
  factory _$SelectAccountCopyWith(_SelectAccount value, $Res Function(_SelectAccount) _then) = __$SelectAccountCopyWithImpl;
@useResult
$Res call({
 TradingAccountModel account
});


$TradingAccountModelCopyWith<$Res> get account;

}
/// @nodoc
class __$SelectAccountCopyWithImpl<$Res>
    implements _$SelectAccountCopyWith<$Res> {
  __$SelectAccountCopyWithImpl(this._self, this._then);

  final _SelectAccount _self;
  final $Res Function(_SelectAccount) _then;

/// Create a copy of AccountListEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? account = null,}) {
  return _then(_SelectAccount(
null == account ? _self.account : account // ignore: cast_nullable_to_non_nullable
as TradingAccountModel,
  ));
}

/// Create a copy of AccountListEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res> get account {
  
  return $TradingAccountModelCopyWith<$Res>(_self.account, (value) {
    return _then(_self.copyWith(account: value));
  });
}
}

/// @nodoc


class _ChangeTab implements AccountListEvent {
  const _ChangeTab(this.tabIndex);
  

 final  int tabIndex;

/// Create a copy of AccountListEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChangeTabCopyWith<_ChangeTab> get copyWith => __$ChangeTabCopyWithImpl<_ChangeTab>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChangeTab&&(identical(other.tabIndex, tabIndex) || other.tabIndex == tabIndex));
}


@override
int get hashCode => Object.hash(runtimeType,tabIndex);

@override
String toString() {
  return 'AccountListEvent.changeTab(tabIndex: $tabIndex)';
}


}

/// @nodoc
abstract mixin class _$ChangeTabCopyWith<$Res> implements $AccountListEventCopyWith<$Res> {
  factory _$ChangeTabCopyWith(_ChangeTab value, $Res Function(_ChangeTab) _then) = __$ChangeTabCopyWithImpl;
@useResult
$Res call({
 int tabIndex
});




}
/// @nodoc
class __$ChangeTabCopyWithImpl<$Res>
    implements _$ChangeTabCopyWith<$Res> {
  __$ChangeTabCopyWithImpl(this._self, this._then);

  final _ChangeTab _self;
  final $Res Function(_ChangeTab) _then;

/// Create a copy of AccountListEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? tabIndex = null,}) {
  return _then(_ChangeTab(
null == tabIndex ? _self.tabIndex : tabIndex // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

/// @nodoc


class _NavigateToCreateAccount implements AccountListEvent {
  const _NavigateToCreateAccount();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NavigateToCreateAccount);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountListEvent.navigateToCreateAccount()';
}


}




/// @nodoc


class _NavigateToCreateWallet implements AccountListEvent {
  const _NavigateToCreateWallet();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NavigateToCreateWallet);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountListEvent.navigateToCreateWallet()';
}


}




/// @nodoc
mixin _$AccountListState {

 AccountListProcessState get processState; List<TradingAccountModel>? get accountsData; TradingAccountModel? get selectedAccount; int get currentTabIndex; bool get shouldAutoSelect; String? get excludeAccountNumber;
/// Create a copy of AccountListState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountListStateCopyWith<AccountListState> get copyWith => _$AccountListStateCopyWithImpl<AccountListState>(this as AccountListState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountListState&&(identical(other.processState, processState) || other.processState == processState)&&const DeepCollectionEquality().equals(other.accountsData, accountsData)&&(identical(other.selectedAccount, selectedAccount) || other.selectedAccount == selectedAccount)&&(identical(other.currentTabIndex, currentTabIndex) || other.currentTabIndex == currentTabIndex)&&(identical(other.shouldAutoSelect, shouldAutoSelect) || other.shouldAutoSelect == shouldAutoSelect)&&(identical(other.excludeAccountNumber, excludeAccountNumber) || other.excludeAccountNumber == excludeAccountNumber));
}


@override
int get hashCode => Object.hash(runtimeType,processState,const DeepCollectionEquality().hash(accountsData),selectedAccount,currentTabIndex,shouldAutoSelect,excludeAccountNumber);

@override
String toString() {
  return 'AccountListState(processState: $processState, accountsData: $accountsData, selectedAccount: $selectedAccount, currentTabIndex: $currentTabIndex, shouldAutoSelect: $shouldAutoSelect, excludeAccountNumber: $excludeAccountNumber)';
}


}

/// @nodoc
abstract mixin class $AccountListStateCopyWith<$Res>  {
  factory $AccountListStateCopyWith(AccountListState value, $Res Function(AccountListState) _then) = _$AccountListStateCopyWithImpl;
@useResult
$Res call({
 AccountListProcessState processState, List<TradingAccountModel>? accountsData, TradingAccountModel? selectedAccount, int currentTabIndex, bool shouldAutoSelect, String? excludeAccountNumber
});


$AccountListProcessStateCopyWith<$Res> get processState;$TradingAccountModelCopyWith<$Res>? get selectedAccount;

}
/// @nodoc
class _$AccountListStateCopyWithImpl<$Res>
    implements $AccountListStateCopyWith<$Res> {
  _$AccountListStateCopyWithImpl(this._self, this._then);

  final AccountListState _self;
  final $Res Function(AccountListState) _then;

/// Create a copy of AccountListState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? processState = null,Object? accountsData = freezed,Object? selectedAccount = freezed,Object? currentTabIndex = null,Object? shouldAutoSelect = null,Object? excludeAccountNumber = freezed,}) {
  return _then(_self.copyWith(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as AccountListProcessState,accountsData: freezed == accountsData ? _self.accountsData : accountsData // ignore: cast_nullable_to_non_nullable
as List<TradingAccountModel>?,selectedAccount: freezed == selectedAccount ? _self.selectedAccount : selectedAccount // ignore: cast_nullable_to_non_nullable
as TradingAccountModel?,currentTabIndex: null == currentTabIndex ? _self.currentTabIndex : currentTabIndex // ignore: cast_nullable_to_non_nullable
as int,shouldAutoSelect: null == shouldAutoSelect ? _self.shouldAutoSelect : shouldAutoSelect // ignore: cast_nullable_to_non_nullable
as bool,excludeAccountNumber: freezed == excludeAccountNumber ? _self.excludeAccountNumber : excludeAccountNumber // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of AccountListState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountListProcessStateCopyWith<$Res> get processState {
  
  return $AccountListProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of AccountListState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res>? get selectedAccount {
    if (_self.selectedAccount == null) {
    return null;
  }

  return $TradingAccountModelCopyWith<$Res>(_self.selectedAccount!, (value) {
    return _then(_self.copyWith(selectedAccount: value));
  });
}
}


/// @nodoc


class _AccountListState extends AccountListState {
  const _AccountListState({this.processState = const AccountListProcessState.loading(), final  List<TradingAccountModel>? accountsData, this.selectedAccount, this.currentTabIndex = 0, this.shouldAutoSelect = true, this.excludeAccountNumber}): _accountsData = accountsData,super._();
  

@override@JsonKey() final  AccountListProcessState processState;
 final  List<TradingAccountModel>? _accountsData;
@override List<TradingAccountModel>? get accountsData {
  final value = _accountsData;
  if (value == null) return null;
  if (_accountsData is EqualUnmodifiableListView) return _accountsData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  TradingAccountModel? selectedAccount;
@override@JsonKey() final  int currentTabIndex;
@override@JsonKey() final  bool shouldAutoSelect;
@override final  String? excludeAccountNumber;

/// Create a copy of AccountListState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountListStateCopyWith<_AccountListState> get copyWith => __$AccountListStateCopyWithImpl<_AccountListState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountListState&&(identical(other.processState, processState) || other.processState == processState)&&const DeepCollectionEquality().equals(other._accountsData, _accountsData)&&(identical(other.selectedAccount, selectedAccount) || other.selectedAccount == selectedAccount)&&(identical(other.currentTabIndex, currentTabIndex) || other.currentTabIndex == currentTabIndex)&&(identical(other.shouldAutoSelect, shouldAutoSelect) || other.shouldAutoSelect == shouldAutoSelect)&&(identical(other.excludeAccountNumber, excludeAccountNumber) || other.excludeAccountNumber == excludeAccountNumber));
}


@override
int get hashCode => Object.hash(runtimeType,processState,const DeepCollectionEquality().hash(_accountsData),selectedAccount,currentTabIndex,shouldAutoSelect,excludeAccountNumber);

@override
String toString() {
  return 'AccountListState(processState: $processState, accountsData: $accountsData, selectedAccount: $selectedAccount, currentTabIndex: $currentTabIndex, shouldAutoSelect: $shouldAutoSelect, excludeAccountNumber: $excludeAccountNumber)';
}


}

/// @nodoc
abstract mixin class _$AccountListStateCopyWith<$Res> implements $AccountListStateCopyWith<$Res> {
  factory _$AccountListStateCopyWith(_AccountListState value, $Res Function(_AccountListState) _then) = __$AccountListStateCopyWithImpl;
@override @useResult
$Res call({
 AccountListProcessState processState, List<TradingAccountModel>? accountsData, TradingAccountModel? selectedAccount, int currentTabIndex, bool shouldAutoSelect, String? excludeAccountNumber
});


@override $AccountListProcessStateCopyWith<$Res> get processState;@override $TradingAccountModelCopyWith<$Res>? get selectedAccount;

}
/// @nodoc
class __$AccountListStateCopyWithImpl<$Res>
    implements _$AccountListStateCopyWith<$Res> {
  __$AccountListStateCopyWithImpl(this._self, this._then);

  final _AccountListState _self;
  final $Res Function(_AccountListState) _then;

/// Create a copy of AccountListState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? processState = null,Object? accountsData = freezed,Object? selectedAccount = freezed,Object? currentTabIndex = null,Object? shouldAutoSelect = null,Object? excludeAccountNumber = freezed,}) {
  return _then(_AccountListState(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as AccountListProcessState,accountsData: freezed == accountsData ? _self._accountsData : accountsData // ignore: cast_nullable_to_non_nullable
as List<TradingAccountModel>?,selectedAccount: freezed == selectedAccount ? _self.selectedAccount : selectedAccount // ignore: cast_nullable_to_non_nullable
as TradingAccountModel?,currentTabIndex: null == currentTabIndex ? _self.currentTabIndex : currentTabIndex // ignore: cast_nullable_to_non_nullable
as int,shouldAutoSelect: null == shouldAutoSelect ? _self.shouldAutoSelect : shouldAutoSelect // ignore: cast_nullable_to_non_nullable
as bool,excludeAccountNumber: freezed == excludeAccountNumber ? _self.excludeAccountNumber : excludeAccountNumber // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of AccountListState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountListProcessStateCopyWith<$Res> get processState {
  
  return $AccountListProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of AccountListState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res>? get selectedAccount {
    if (_self.selectedAccount == null) {
    return null;
  }

  return $TradingAccountModelCopyWith<$Res>(_self.selectedAccount!, (value) {
    return _then(_self.copyWith(selectedAccount: value));
  });
}
}

/// @nodoc
mixin _$AccountListProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountListProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountListProcessState()';
}


}

/// @nodoc
class $AccountListProcessStateCopyWith<$Res>  {
$AccountListProcessStateCopyWith(AccountListProcessState _, $Res Function(AccountListProcessState) __);
}


/// @nodoc


class AccountListLoadingProcessState implements AccountListProcessState {
  const AccountListLoadingProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountListLoadingProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountListProcessState.loading()';
}


}




/// @nodoc


class AccountListSuccessProcessState implements AccountListProcessState {
  const AccountListSuccessProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountListSuccessProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountListProcessState.success()';
}


}




/// @nodoc


class AccountListErrorProcessState implements AccountListProcessState {
  const AccountListErrorProcessState({this.errorMessage});
  

 final  String? errorMessage;

/// Create a copy of AccountListProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountListErrorProcessStateCopyWith<AccountListErrorProcessState> get copyWith => _$AccountListErrorProcessStateCopyWithImpl<AccountListErrorProcessState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountListErrorProcessState&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}


@override
int get hashCode => Object.hash(runtimeType,errorMessage);

@override
String toString() {
  return 'AccountListProcessState.error(errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class $AccountListErrorProcessStateCopyWith<$Res> implements $AccountListProcessStateCopyWith<$Res> {
  factory $AccountListErrorProcessStateCopyWith(AccountListErrorProcessState value, $Res Function(AccountListErrorProcessState) _then) = _$AccountListErrorProcessStateCopyWithImpl;
@useResult
$Res call({
 String? errorMessage
});




}
/// @nodoc
class _$AccountListErrorProcessStateCopyWithImpl<$Res>
    implements $AccountListErrorProcessStateCopyWith<$Res> {
  _$AccountListErrorProcessStateCopyWithImpl(this._self, this._then);

  final AccountListErrorProcessState _self;
  final $Res Function(AccountListErrorProcessState) _then;

/// Create a copy of AccountListProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? errorMessage = freezed,}) {
  return _then(AccountListErrorProcessState(
errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
