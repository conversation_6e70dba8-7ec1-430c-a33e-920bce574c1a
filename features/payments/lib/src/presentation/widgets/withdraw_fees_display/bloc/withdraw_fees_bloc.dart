import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';
import 'package:payment/src/data/withdraw_fees_response_model/withdraw_fees_response_model.dart';
import 'package:payment/src/domain/exceptions/wihtdraw_fees_exception/withdraw_fees_exception.dart';
import 'package:payment/src/domain/model/transfer_type.dart';
import 'package:payment/src/domain/usecase/get_withdrawal_fees_usecase.dart';
import 'package:prelude/prelude.dart';

part 'withdraw_fees_bloc.freezed.dart';
part 'withdraw_fees_event.dart';
part 'withdraw_fees_state.dart';

/// Bloc responsible for managing withdrawal fees calculation state
class WithdrawFeesBloc extends Bloc<WithdrawFeesEvent, WithdrawFeesState> {
  WithdrawFeesBloc({
    required GetWithdrawalFeesUsecase getWithdrawalFeesUsecase,
    required WithdrawAnalyticsEvent withdrawAnalyticsEvent,
  }) : _getWithdrawalFeesUsecase = getWithdrawalFeesUsecase,
       _withdrawAnalyticsEvent = withdrawAnalyticsEvent,
       super(const WithdrawFeesState()) {
    on<_CalculateFees>(
      _onCalculateFees,
      transformer: throttleTransformer(Duration(milliseconds: 500)),
    );
  }

  final GetWithdrawalFeesUsecase _getWithdrawalFeesUsecase;
  final WithdrawAnalyticsEvent _withdrawAnalyticsEvent;

  Future<void> _onCalculateFees(
    _CalculateFees event,
    Emitter<WithdrawFeesState> emit,
  ) async {
    if (event.selectedCurrencyAmount <= 0) {
      emit(const WithdrawFeesState());
      return;
    }

    emit(
      state.copyWith(
        processState: const WithdrawFeesProcessState.loading(),
        selectedCurrencyAmount: event.selectedCurrencyAmount,
      ),
    );

    final result =
        await _getWithdrawalFeesUsecase(
          paymentType: event.paymentType,
          clientId: event.accountId,
          accountCurrency: event.accountCurrency,
          selectedCurrencyAmount: event.selectedCurrencyAmount,
          accountCurrencyAmount: event.accountCurrencyAmount,
          selectedCurrency: event.selectedCurrency,
          transferType: event.transferType,
        ).run();

    result.fold(
      (error) {
        if (error is WithdrawFeesException) {
          switch (error.code) {
            default:
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: WithdrawFeesProcessState.error(
                      errorMessage: error.message,
                    ),
                  ),
                );
              }
          }
        } else {
          addError(error);
          if (!isClosed) {
            emit(
              state.copyWith(
                processState: WithdrawFeesProcessState.error(
                  errorMessage: error.toString(),
                ),
              ),
            );
          }
        }
      },
      (feesData) {
        _withdrawAnalyticsEvent.withdrawFeesChanged(
          fees: feesData.fee.toString(),
          accountCurrency: feesData.currency,
          selectedCurrency: state.selectedCurrency,
        );
        if (feesData.fee <= 0 && !isClosed) {
          emit(
            state.copyWith(
              processState: const WithdrawFeesProcessState.zeroFees(),
            ),
          );
        } else {
          if (!isClosed) {
            emit(
              state.copyWith(
                processState: const WithdrawFeesProcessState.nonZeroFees(),
                fees: feesData.fee.toDouble(),
                total: event.selectedCurrencyAmount + feesData.fee.toDouble(),
                accountCurrency: feesData.currency,
              ),
            );
          }
        }
      },
    );
  }
}
