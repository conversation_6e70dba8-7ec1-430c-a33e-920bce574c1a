import 'dart:async';

import 'package:flutter/cupertino.dart';

import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';
import 'package:payment/src/data/bank_accounts_model.dart';
import 'package:payment/src/domain/model/screen_mode.dart';
import 'package:payment/src/domain/usecase/delete_bank_account_use_case.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/domain/usecase/get_bank_accounts_use_case.dart';
import 'package:payment/src/navigation/arguments/transfer_type_page_arguments.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:prelude/prelude.dart';

part 'withdraw_bank_transfer_bloc.freezed.dart';
part 'withdraw_bank_transfer_event.dart';
part 'withdraw_bank_transfer_state.dart';

class WithdrawBankTransferBloc
    extends Bloc<WithdrawBankTransferEvent, WithdrawBankTransferState> {
  final GetBankAccountsUseCase _getBankAccountsUseCase;
  final DeleteBankAccountUseCase _deleteBankAccountUseCase;
  final PaymentNavigation _paymentNavigation;
  final WithdrawAnalyticsEvent _withdrawAnalyticsEvent;

  WithdrawBankTransferBloc(
    this._getBankAccountsUseCase,
    this._paymentNavigation,
    this._deleteBankAccountUseCase,
    this._withdrawAnalyticsEvent,
  ) : super(WithdrawBankTransferState()) {
    on<_FetchBankAccounts>(_onFetchBankAccounts);
    on<_ChangeSelectedIndex>(_onChangeSelectedIndex);
    on<_OnPressContinue>(_onPressContinue);
    on<_OnChangeMode>(_onChangeMode);
    on<_OnDeleteBankAccount>(_onDeleteBankAccount);
    on<_OnAddNewAccountPressed>(_onAddNewAccountPressed);
    on<_OnNavigateToDeleteAccountSuccessfully>(
      _onNavigateToDeleteAccountSuccessfully,
    );
  }

  FutureOr<void> _onFetchBankAccounts(
    _FetchBankAccounts event,
    Emitter<WithdrawBankTransferState> emit,
  ) async {
    final bankAccountsResponse =
        await _getBankAccountsUseCase(event.tradingAccountId).run();

    bankAccountsResponse.fold(
      (l) {
        if (!isClosed)
          emit(
            state.copyWith(
              withdrawBankTransferProccessState:
                  WithdrawBankTransferProccessState.error(),
            ),
          );
      },
      (r) {
        _withdrawAnalyticsEvent.withdrawBanksLoaded(
          numOfBanks: r.data.banks.length.toString(),
        );
        if (!isClosed) {
          if (r.data.banks.isEmpty) {
            emit(
              state.copyWith(
                withdrawBankTransferProccessState:
                    WithdrawBankTransferProccessState.successEmpty(),
              ),
            );
            return;
          }
          emit(
            state.copyWith(
              bankAccounts: r.data.banks,
              banksLimit: r.data.banksLimit,
              withdrawBankTransferProccessState:
                  WithdrawBankTransferProccessState.success(),
            ),
          );
        }
      },
    );
  }

  FutureOr<void> _onChangeSelectedIndex(
    _ChangeSelectedIndex event,
    Emitter<WithdrawBankTransferState> emit,
  ) {
    _withdrawAnalyticsEvent.withdrawBankSelected(
      bankId: state.bankAccounts.elementAtOrNull(event.index)?.id ?? '',
    );
    emit(state.copyWith(index: event.index));
  }

  FutureOr<void> _onPressContinue(
    _OnPressContinue event,
    Emitter<WithdrawBankTransferState> emit,
  ) {
    //dismisses keyboard if anyhow the keyboard is open.
    FocusManager.instance.primaryFocus?.unfocus();
    SystemChannels.textInput.invokeMethod('TextInput.hide');
    _paymentNavigation.goToTransferTypeScreen(
      TransferTypePageArguments(
        bankTransferAmountModel: event.bankTransferAmountModel,
        bank: state.bankAccounts.elementAtOrNull(state.index ?? 0)!,
        popUntilRoute: event.popUntilRoute,
      ),
    );
  }

  FutureOr<void> _onChangeMode(
    _OnChangeMode event,
    Emitter<WithdrawBankTransferState> emit,
  ) {
    emit(state.copyWith(screenMode: event.mode));
  }

  FutureOr<void> _onDeleteBankAccount(
    _OnDeleteBankAccount event,
    Emitter<WithdrawBankTransferState> emit,
  ) async {
    emit(state.copyWith(isDeleteAccountButtonLoading: true));
    final result =
        await _deleteBankAccountUseCase(event.bankAccountModel.id).run();

    result.fold(
      (l) {
        if (!isClosed) {
          emit(state.copyWith(dismissDeleteConfirmationBottomSheet: true));
          emit(
            state.copyWith(
              isDeleteAccountButtonLoading: false,
              shouldShowDeleteAccountErrorBottomSheet: true,
            ),
          );
          emit(
            state.copyWith(
              shouldShowDeleteAccountErrorBottomSheet: false,
              dismissDeleteConfirmationBottomSheet: false,
            ),
          );
        }
      },
      (r) {
        _withdrawAnalyticsEvent.withdrawBankDeleted(
          bankId: event.bankAccountModel.id,
        );
        if (!isClosed) {
          final isBankAccountEmpty =
              state.bankAccounts
                  .where((element) => element.id != event.bankAccountModel.id)
                  .toList()
                  .isEmpty;
          emit(
            state.copyWith(
              bankAccounts:
                  state.bankAccounts
                      .where(
                        (element) => element.id != event.bankAccountModel.id,
                      )
                      .toList(),
              withdrawBankTransferProccessState:
                  isBankAccountEmpty
                      ? WithdrawBankTransferProccessState.successEmpty()
                      : WithdrawBankTransferProccessState.success(),

              isDeleteAccountButtonLoading: false,
              dismissDeleteConfirmationBottomSheet: true,
            ),
          );

          emit(
            state.copyWith(
              dismissDeleteConfirmationBottomSheet: false,
              shouldShowDeleteAccountSuccessBottomSheet: true,
            ),
          );
          emit(
            state.copyWith(shouldShowDeleteAccountSuccessBottomSheet: false),
          );
        }
      },
    );
  }

  FutureOr<void> _onAddNewAccountPressed(
    _OnAddNewAccountPressed event,
    Emitter<WithdrawBankTransferState> emit,
  ) {
    _withdrawAnalyticsEvent.withdrawAddNewBankStart();
    _paymentNavigation.goToWithdrawAddNewBankPage(
      event.bankTransferAmountModel,
    );
  }

  FutureOr<void> _onNavigateToDeleteAccountSuccessfully(
    _OnNavigateToDeleteAccountSuccessfully event,
    Emitter<WithdrawBankTransferState> emit,
  ) {
    _paymentNavigation.goToDeleteAccountSuccessScreen(
      onAddNewAccount: () {
        _paymentNavigation.goToWithdrawAddNewBankPage(
          event.bankTransferAmountModel,
        );
      },
    );
  }
}
