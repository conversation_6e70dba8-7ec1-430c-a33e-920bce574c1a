import 'dart:async';
import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';
import 'package:payment/src/data/payment_method/withdraw_payment_methods_model/withdraw_payment_methods_model.dart';
import 'package:payment/src/data/withdraw_card_model/withdraw_card_model.dart';
import 'package:payment/src/domain/exceptions/withdraw_card_exception/withdraw_card_exception.dart';
import 'package:payment/src/domain/usecase/get_withdraw_card_usecase.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:payment/src/domain/data/withdraw_status_type.dart';

part 'withdraw_card_bloc.freezed.dart';
part 'withdraw_card_event.dart';
part 'withdraw_card_state.dart';

@injectable
class WithdrawCardBloc extends Bloc<WithdrawCardEvent, WithdrawCardState> {
  WithdrawCardBloc({
    required GetWithdrawCardUseCase getWithdrawCardUseCase,
    required PaymentNavigation paymentNavigation,
    required WithdrawAnalyticsEvent withdrawAnalyticsEvent,
  }) : _getWithdrawCardUseCase = getWithdrawCardUseCase,
       _paymentNavigation = paymentNavigation,
       _withdrawAnalyticsEvent = withdrawAnalyticsEvent,
       super(const WithdrawCardState()) {
    on<_LoadEvent>(_onLoadCard);
    on<_SetPaymentMethodEvent>(_onSetPaymentMethod);
    on<_OnConfirmButtonPressedEvent>(_onConfirmButtonPressed);
    on<_OnChooseAnotherMethodPressedEvent>(_onChooseAnotherMethodPressed);
    on<_OnCardSelectedPressedEvent>(_onCardSelectedPressedEvent);
    on<_OnContinueButtonPressedAfterSuccess>(
      _onContinueButtonPressedAfterSuccess,
    );
    add(const _LoadEvent());
  }
  final GetWithdrawCardUseCase _getWithdrawCardUseCase;
  final PaymentNavigation _paymentNavigation;
  final WithdrawAnalyticsEvent _withdrawAnalyticsEvent;

  Future<void> _onLoadCard(
    _LoadEvent event,
    Emitter<WithdrawCardState> emit,
  ) async {
    emit(state.copyWith(currentState: WithdrawCardProcessState.loading()));
    final result = await _getWithdrawCardUseCase().run();
    try {
      result.fold(
        (failure) {
          if (failure is WithdrawCardException) {
            log(
              'WithdrawCardException: code=${failure.code}, message=${failure.message}',
            );
            // Handle specific error based on error code
            switch (failure.code) {
              default:
                if (!isClosed) {
                  emit(
                    state.copyWith(
                      currentState: WithdrawCardProcessState.error(),
                    ),
                  );
                }
            }
          } else {
            log('error on card options ${failure}');
            if (!isClosed) {
              emit(
                state.copyWith(currentState: WithdrawCardProcessState.error()),
              );
            }
          }
        },
        (WithdrawCardModel withdrawCardModel) {
          _withdrawAnalyticsEvent.withdawCardsLoaded(
            numOfCards: withdrawCardModel.cards.length.toString(),
          );
          if (withdrawCardModel.cards.isNotEmpty) {
            if (!isClosed) {
              emit(
                state.copyWith(
                  currentState: WithdrawCardProcessState.loaded(),
                  cards: withdrawCardModel.cards,
                  selectedCardIndex: 0,
                ),
              );
            }
          } else {
            if (!isClosed) {
              emit(
                state.copyWith(currentState: WithdrawCardProcessState.empty()),
              );
            }
          }
        },
      );
    } catch (e) {
      log(' error on card options $e');
      if (!isClosed) {
        emit(state.copyWith(currentState: WithdrawCardProcessState.error()));
      }
    }
  }

  void _onSetPaymentMethod(
    _SetPaymentMethodEvent event,
    Emitter<WithdrawCardState> emit,
  ) {
    emit(state.copyWith(paymentMethod: event.paymentMethod));
  }

  void _onConfirmButtonPressed(
    _OnConfirmButtonPressedEvent event,
    Emitter<WithdrawCardState> emit,
  ) {
    log('confirm button pressed');
    if (state.paymentMethod != null && state.cards != null) {
      final selectedCard = state.cards!.elementAtOrNull(
        state.selectedCardIndex,
      );
      _paymentNavigation.goToWithdrawSelectAccountAndAmountScreen(
        method: state.paymentMethod!,
        selectedCard: selectedCard,
        popUntilRoute: event.popUntilRoute,
      );
    } else {
      log('error on card options');
    }
  }

  FutureOr<void> _onChooseAnotherMethodPressed(
    _OnChooseAnotherMethodPressedEvent event,
    Emitter<WithdrawCardState> emit,
  ) {
    log('choose another method pressed');
  }

  FutureOr<void> _onCardSelectedPressedEvent(
    _OnCardSelectedPressedEvent event,
    Emitter<WithdrawCardState> emit,
  ) {
    _withdrawAnalyticsEvent.withdrawCardSelected();
    emit(state.copyWith(selectedCardIndex: event.index));
  }

  FutureOr<void> _onContinueButtonPressedAfterSuccess(
    _OnContinueButtonPressedAfterSuccess event,
    Emitter<WithdrawCardState> emit,
  ) {
    log('continue button pressed after success');
  }
}
