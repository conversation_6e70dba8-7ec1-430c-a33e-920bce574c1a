import 'dart:io';
import 'package:dio/dio.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:payment/src/analytics/deposit_analytics_event.dart';

import 'package:payment/src/assets/assets.gen.dart' as paymentAssets;
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:locale_manager/locale_manager.dart';

class DepositBankDetailsScreen extends StatelessWidget {
  const DepositBankDetailsScreen({
    super.key,
    required this.bank,
    required this.currency,
  });

  final Bank bank;
  final String currency;

  void onItemSelected(
    BuildContext ctx,
    String value,
    EquitiLocalization localization,
  ) async {
    if (value == 'copy') {
      diContainer<DepositAnalyticsEvent>().depositBankDetailsCopied(
        bankName: bank.name,
        currency: currency,
      );
      final String content =
          "${localization.payments_swift_bic_code}\n${bank.swiftCode}\n\n"
          "${localization.payments_accountName}\n${bank.accountName}\n\n"
          "${localization.payments_accountAddress}\n${bank.accountAddress}\n\n"
          "${localization.payments_reference}\n${bank.reference}\n\n"
          "${localization.payments_purposeOfPayment}\n${bank.purposeOfPayment}\n\n"
          "${localization.payments_iban}\n${bank.iban}\n\n"
          "${localization.payments_accountNumber}\n${bank.accountNumber}";

      Clipboard.setData(ClipboardData(text: content));
      // calculating position of popup menu
      final box = ctx.findRenderObject() as RenderBox?;
      Offset? position = box?.localToGlobal(Offset.zero);
      final TextDirection currentDirection = Directionality.of(ctx);
      final bool isRTL = currentDirection == TextDirection.rtl;
      DuploOverlay.show(
        ctx,
        localization.duplo_copied,
        position: position,
        offset: Offset(isRTL ? 54 : -54, 0),
      );
      return;
    }

    if (value == 'download') {
      await _downloadPdf(ctx, localization);
      diContainer<DepositAnalyticsEvent>().depositBankPdfDownloaded(
        bankName: bank.name,
        currency: currency,
      );
    }
  }

  Future<void> _downloadPdf(
    BuildContext context,
    EquitiLocalization localization,
  ) async {
    try {
      final locale = diContainer<LocaleManager>().getLanguageCode();
      final String? pdfUrl = bank.pdf?[locale] as String?;

      if (pdfUrl == null || pdfUrl.isEmpty) {
        _showMessage(context, localization.payments_something_went_wrong);
        return;
      }

      final Directory? downloadsDir = await getDownloadsDirectory();
      if (downloadsDir == null) {
        _showMessage(context, localization.payments_something_went_wrong);
        return;
      }

      final filename =
          'bank_details_${bank.name.replaceAll(RegExp(r'[^\w\s-]'), '')}.pdf';
      final filePath = '${downloadsDir.path}/$filename';

      final dio = Dio();
      await dio.download(pdfUrl, filePath);

      OpenFile.open(filePath);

      _showMessage(context, localization.payments_downloaded);
    } catch (e) {
      _showMessage(context, localization.payments_something_went_wrong);
    }
  }

  void _showMessage(BuildContext context, String message) {
    DuploOverlay.show(context, message);
  }

  List<Widget> getActions(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return [
      PopupMenuButton(
        color: theme.background.bgSecondaryAlt,
        icon: paymentAssets.Assets.images.moreIcon.svg(
          colorFilter: ColorFilter.mode(
            theme.foreground.fgSecondary,
            BlendMode.srcIn,
          ),
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        position: PopupMenuPosition.under,
        shadowColor: theme.border.borderSecondary,
        elevation: 4,
        itemBuilder: (ctx) {
          return [
            PopupMenuItem(
              value: 'copy',
              child: DuploTap(
                onTap: () {
                  Navigator.pop(ctx);
                  onItemSelected(ctx, 'copy', localization);
                },
                child: Row(
                  children: [
                    Assets.images.copy.svg(
                      height: 20,
                      colorFilter: ColorFilter.mode(
                        theme.button.buttonTertiaryFg,
                        BlendMode.srcIn,
                      ),
                    ),
                    SizedBox(width: 8),
                    DuploText(
                      text: localization.payments_copyAll,
                      style: textStyles.textMd,
                      fontWeight: DuploFontWeight.semiBold,
                      color: theme.text.textTertiary,
                    ),
                  ],
                ),
              ),
            ),
            PopupMenuItem(
              value: 'download',
              child: DuploTap(
                onTap: () {
                  Navigator.pop(ctx);
                  onItemSelected(ctx, 'download', localization);
                },
                child: Row(
                  children: [
                    paymentAssets.Assets.images.fileDownload.svg(
                      height: 20,
                      colorFilter: ColorFilter.mode(
                        theme.button.buttonTertiaryFg,
                        BlendMode.srcIn,
                      ),
                    ),
                    SizedBox(width: 8),
                    DuploText(
                      text: localization.payments_saveAsPDF,
                      style: textStyles.textMd,
                      fontWeight: DuploFontWeight.semiBold,
                      color: theme.text.textTertiary,
                    ),
                  ],
                ),
              ),
            ),
          ];
        },
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      appBar: DuploAppBar(
        title: localization.payments_depositBankTransfer,
        actions: getActions(context),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DuploText(
              text: localization.payments_accountInformation,
              style: textStyles.textXl,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
            ),
            const SizedBox(height: 24),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DuploAlertMessage.warning(
                      title: localization.payments_ensureNameAndAccountUse,
                      titleFontWeight: DuploFontWeight.regular,
                    ),
                    const SizedBox(height: 24),
                    DuploCopyTile(
                      label: localization.payments_iban,
                      content: bank.iban,
                      backgroundColor: theme.background.bgSecondarySubtle,
                      borderColor: theme.border.borderSecondary,
                    ),
                    const SizedBox(height: 12),
                    DuploCopyTile(
                      label: localization.payments_accountNumber,
                      content: bank.accountNumber,
                      backgroundColor: theme.background.bgSecondarySubtle,
                      borderColor: theme.border.borderSecondary,
                    ),
                    const SizedBox(height: 24),
                    DuploText(
                      text: bank.name,
                      style: textStyles.textXl,
                      fontWeight: DuploFontWeight.semiBold,
                      color: theme.text.textPrimary,
                    ),
                    const SizedBox(height: 24),
                    DuploCopyTile(
                      label: localization.payments_swift_bic_code,
                      content: bank.swiftCode,
                      backgroundColor: theme.background.bgSecondarySubtle,
                      borderColor: theme.border.borderSecondary,
                    ),
                    const SizedBox(height: 12),
                    DuploCopyTile(
                      label: localization.payments_accountName,
                      content: bank.accountName,
                      backgroundColor: theme.background.bgSecondarySubtle,
                      borderColor: theme.border.borderSecondary,
                    ),
                    const SizedBox(height: 12),
                    DuploCopyTile(
                      label: localization.payments_accountAddress,
                      content: bank.accountAddress,
                      backgroundColor: theme.background.bgSecondarySubtle,
                      borderColor: theme.border.borderSecondary,
                    ),
                    const SizedBox(height: 12),
                    DuploCopyTile(
                      label: localization.payments_reference,
                      content: bank.reference,
                      backgroundColor: theme.background.bgSecondarySubtle,
                      borderColor: theme.border.borderSecondary,
                    ),
                    const SizedBox(height: 12),
                    DuploCopyTile(
                      label: localization.payments_purposeOfPayment,
                      content: bank.purposeOfPayment,
                      backgroundColor: theme.background.bgSecondarySubtle,
                      borderColor: theme.border.borderSecondary,
                    ),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            Column(
              children: [
                DuploButton.defaultPrimary(
                  title: localization.payments_depositedViaBankTransfer,
                  onTap: () {
                    diContainer<PaymentNavigation>().goBackToSwitchAccounts();
                  },
                  useFullWidth: true,
                ),
                const SizedBox(height: 16),
                DuploButton.secondary(
                  title: localization.payments_goBack,
                  leadingIcon: Assets.images.chevronLeft.keyName,
                  onTap: () {
                    Navigator.pop(context);
                  },
                  useFullWidth: true,
                ),
              ],
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
