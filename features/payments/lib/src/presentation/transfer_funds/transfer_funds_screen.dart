import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/analytics/transfer_analytics_event.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/account_list_widget.dart';
import 'package:payment/src/presentation/transfer_funds/bloc/transfer_funds_bloc.dart';

class TransferFundsScreen extends StatelessWidget {
  const TransferFundsScreen({super.key, required this.originRoute});
  final String originRoute;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      appBar: DuploAppBar(title: localization.payments_transfer),
      body: BlocProvider(
        create: (blocProviderContext) {
          diContainer<TransferAnalyticsEvent>()
              .transferSourceAccountPageLoaded();
          return diContainer<TransferFundsBloc>();
        },
        child: BlocBuilder<TransferFundsBloc, TransferFundsState>(
          buildWhen: (previous, current) => previous != current,
          builder: (blocBuilderContext, state) {
            return Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          const SizedBox(height: 8),
                          Align(
                            alignment: AlignmentDirectional.centerStart,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                DuploText(
                                  text: localization.payments_source_account,
                                  style: style.textLg,
                                  color: theme.text.textPrimary,
                                  fontWeight: DuploFontWeight.semiBold,
                                ),
                                const SizedBox(height: 8),
                                DuploText(
                                  text:
                                      localization
                                          .payments_select_source_account,
                                  style: style.textSm,
                                  color: theme.text.textSecondary,
                                ),
                                const SizedBox(height: 23),
                              ],
                            ),
                          ),
                          AccountListWidget(
                            args: (
                              selectByHighestBalance: true,
                              excludeAccountNumber: null,
                              onEmptyStateChanged: null,
                              isInputDisabled: false,
                            ),
                            onAccountSelected: (TradingAccountModel account) {
                              diContainer<TransferAnalyticsEvent>()
                                  .transferSourceAccountSelected();
                              blocBuilderContext.read<TransferFundsBloc>()..add(
                                TransferFundsEvent.selectSourceAccount(account),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 24.0,
                    horizontal: 16,
                  ),
                  child: DuploButton.defaultPrimary(
                    isDisabled: state.isButtonStateDisabled,
                    title: localization.payments_continue,
                    useFullWidth: true,
                    trailingIcon: Assets.images.chevronRight.keyName,

                    onTap: () {
                      if (state.selectedAccount == null) {
                        return;
                      }
                      blocBuilderContext.read<TransferFundsBloc>()..add(
                        TransferFundsEvent.goToDestenationPage(
                          state.selectedAccount!,
                          originRoute,
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 20),
              ],
            );
          },
        ),
      ),
    );
  }
}
