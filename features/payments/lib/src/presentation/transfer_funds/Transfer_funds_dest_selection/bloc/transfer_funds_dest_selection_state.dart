part of 'transfer_funds_dest_selection_bloc.dart';

@freezed
sealed class TransferFundsDestSelectionState
    with _$TransferFundsDestSelectionState {
  const factory TransferFundsDestSelectionState({
    @Default(TransferFundsDestSelectionProcessState.startedScreen())
    TransferFundsDestSelectionProcessState processState,
    TradingAccountModel? destinationAccount,
    DepositPaymentMethodsModel? paymentMethodsData,
    @Default(true) bool isButtonDisabled,
    String? amountInSourceCurrency,
    String? amountInDestinationCurrency,
    String? conversionRateString,
    ConversionRateModel? conversionRateModel,
    RatesModel? conversionRateToDestinationCurrency,
    @Default(true) bool hasAccounts,
    @Default(true) bool hasWallets,
    @Default(false) bool isCurrentTabEmpty,
    @Default(0) int currentTabIndex,
    @Default(false) bool isButtonLoading,
    @Default(null) String? errorMessage,
  }) = _TransferFundsDestSelectionState;
}

@freezed
sealed class TransferFundsDestSelectionProcessState
    with _$TransferFundsDestSelectionProcessState {
  const factory TransferFundsDestSelectionProcessState.startedScreen() =
      StartedScreen;

  const factory TransferFundsDestSelectionProcessState.transferStatusScreen(
    TradingAccountModel sourceAccount,
    TradingAccountModel destinationAccount,
    String amount,
    TransferStatus status,
  ) = TransferStatusScreen;

  const factory TransferFundsDestSelectionProcessState.errorTransfer() =
      ErrorTransfer;
}
