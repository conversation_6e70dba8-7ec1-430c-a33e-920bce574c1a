// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_funds_dest_selection_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$TransferFundsDestSelectionEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferFundsDestSelectionEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'TransferFundsDestSelectionEvent()';
}


}

/// @nodoc
class $TransferFundsDestSelectionEventCopyWith<$Res>  {
$TransferFundsDestSelectionEventCopyWith(TransferFundsDestSelectionEvent _, $Res Function(TransferFundsDestSelectionEvent) __);
}


/// @nodoc


class _SelectDestinationAccount implements TransferFundsDestSelectionEvent {
  const _SelectDestinationAccount(this.account);
  

 final  TradingAccountModel account;

/// Create a copy of TransferFundsDestSelectionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SelectDestinationAccountCopyWith<_SelectDestinationAccount> get copyWith => __$SelectDestinationAccountCopyWithImpl<_SelectDestinationAccount>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SelectDestinationAccount&&(identical(other.account, account) || other.account == account));
}


@override
int get hashCode => Object.hash(runtimeType,account);

@override
String toString() {
  return 'TransferFundsDestSelectionEvent.selectDestinationAccount(account: $account)';
}


}

/// @nodoc
abstract mixin class _$SelectDestinationAccountCopyWith<$Res> implements $TransferFundsDestSelectionEventCopyWith<$Res> {
  factory _$SelectDestinationAccountCopyWith(_SelectDestinationAccount value, $Res Function(_SelectDestinationAccount) _then) = __$SelectDestinationAccountCopyWithImpl;
@useResult
$Res call({
 TradingAccountModel account
});


$TradingAccountModelCopyWith<$Res> get account;

}
/// @nodoc
class __$SelectDestinationAccountCopyWithImpl<$Res>
    implements _$SelectDestinationAccountCopyWith<$Res> {
  __$SelectDestinationAccountCopyWithImpl(this._self, this._then);

  final _SelectDestinationAccount _self;
  final $Res Function(_SelectDestinationAccount) _then;

/// Create a copy of TransferFundsDestSelectionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? account = null,}) {
  return _then(_SelectDestinationAccount(
null == account ? _self.account : account // ignore: cast_nullable_to_non_nullable
as TradingAccountModel,
  ));
}

/// Create a copy of TransferFundsDestSelectionEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res> get account {
  
  return $TradingAccountModelCopyWith<$Res>(_self.account, (value) {
    return _then(_self.copyWith(account: value));
  });
}
}

/// @nodoc


class _ChangeButtonStateAndUpdateAmount implements TransferFundsDestSelectionEvent {
  const _ChangeButtonStateAndUpdateAmount({required this.amountInDestinationCurrency, required this.isDisabled, required this.amountInSourceCurrency, required this.conversionRateModel, required this.conversionRateToDestinationCurrency, required this.conversionRateString});
  

 final  String amountInDestinationCurrency;
 final  bool isDisabled;
 final  String amountInSourceCurrency;
 final  ConversionRateModel? conversionRateModel;
 final  RatesModel? conversionRateToDestinationCurrency;
 final  String? conversionRateString;

/// Create a copy of TransferFundsDestSelectionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChangeButtonStateAndUpdateAmountCopyWith<_ChangeButtonStateAndUpdateAmount> get copyWith => __$ChangeButtonStateAndUpdateAmountCopyWithImpl<_ChangeButtonStateAndUpdateAmount>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChangeButtonStateAndUpdateAmount&&(identical(other.amountInDestinationCurrency, amountInDestinationCurrency) || other.amountInDestinationCurrency == amountInDestinationCurrency)&&(identical(other.isDisabled, isDisabled) || other.isDisabled == isDisabled)&&(identical(other.amountInSourceCurrency, amountInSourceCurrency) || other.amountInSourceCurrency == amountInSourceCurrency)&&(identical(other.conversionRateModel, conversionRateModel) || other.conversionRateModel == conversionRateModel)&&(identical(other.conversionRateToDestinationCurrency, conversionRateToDestinationCurrency) || other.conversionRateToDestinationCurrency == conversionRateToDestinationCurrency)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString));
}


@override
int get hashCode => Object.hash(runtimeType,amountInDestinationCurrency,isDisabled,amountInSourceCurrency,conversionRateModel,conversionRateToDestinationCurrency,conversionRateString);

@override
String toString() {
  return 'TransferFundsDestSelectionEvent.changeButtonStateAndUpdateAmount(amountInDestinationCurrency: $amountInDestinationCurrency, isDisabled: $isDisabled, amountInSourceCurrency: $amountInSourceCurrency, conversionRateModel: $conversionRateModel, conversionRateToDestinationCurrency: $conversionRateToDestinationCurrency, conversionRateString: $conversionRateString)';
}


}

/// @nodoc
abstract mixin class _$ChangeButtonStateAndUpdateAmountCopyWith<$Res> implements $TransferFundsDestSelectionEventCopyWith<$Res> {
  factory _$ChangeButtonStateAndUpdateAmountCopyWith(_ChangeButtonStateAndUpdateAmount value, $Res Function(_ChangeButtonStateAndUpdateAmount) _then) = __$ChangeButtonStateAndUpdateAmountCopyWithImpl;
@useResult
$Res call({
 String amountInDestinationCurrency, bool isDisabled, String amountInSourceCurrency, ConversionRateModel? conversionRateModel, RatesModel? conversionRateToDestinationCurrency, String? conversionRateString
});


$ConversionRateModelCopyWith<$Res>? get conversionRateModel;$RatesModelCopyWith<$Res>? get conversionRateToDestinationCurrency;

}
/// @nodoc
class __$ChangeButtonStateAndUpdateAmountCopyWithImpl<$Res>
    implements _$ChangeButtonStateAndUpdateAmountCopyWith<$Res> {
  __$ChangeButtonStateAndUpdateAmountCopyWithImpl(this._self, this._then);

  final _ChangeButtonStateAndUpdateAmount _self;
  final $Res Function(_ChangeButtonStateAndUpdateAmount) _then;

/// Create a copy of TransferFundsDestSelectionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? amountInDestinationCurrency = null,Object? isDisabled = null,Object? amountInSourceCurrency = null,Object? conversionRateModel = freezed,Object? conversionRateToDestinationCurrency = freezed,Object? conversionRateString = freezed,}) {
  return _then(_ChangeButtonStateAndUpdateAmount(
amountInDestinationCurrency: null == amountInDestinationCurrency ? _self.amountInDestinationCurrency : amountInDestinationCurrency // ignore: cast_nullable_to_non_nullable
as String,isDisabled: null == isDisabled ? _self.isDisabled : isDisabled // ignore: cast_nullable_to_non_nullable
as bool,amountInSourceCurrency: null == amountInSourceCurrency ? _self.amountInSourceCurrency : amountInSourceCurrency // ignore: cast_nullable_to_non_nullable
as String,conversionRateModel: freezed == conversionRateModel ? _self.conversionRateModel : conversionRateModel // ignore: cast_nullable_to_non_nullable
as ConversionRateModel?,conversionRateToDestinationCurrency: freezed == conversionRateToDestinationCurrency ? _self.conversionRateToDestinationCurrency : conversionRateToDestinationCurrency // ignore: cast_nullable_to_non_nullable
as RatesModel?,conversionRateString: freezed == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of TransferFundsDestSelectionEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ConversionRateModelCopyWith<$Res>? get conversionRateModel {
    if (_self.conversionRateModel == null) {
    return null;
  }

  return $ConversionRateModelCopyWith<$Res>(_self.conversionRateModel!, (value) {
    return _then(_self.copyWith(conversionRateModel: value));
  });
}/// Create a copy of TransferFundsDestSelectionEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RatesModelCopyWith<$Res>? get conversionRateToDestinationCurrency {
    if (_self.conversionRateToDestinationCurrency == null) {
    return null;
  }

  return $RatesModelCopyWith<$Res>(_self.conversionRateToDestinationCurrency!, (value) {
    return _then(_self.copyWith(conversionRateToDestinationCurrency: value));
  });
}
}

/// @nodoc


class _TransferFunds implements TransferFundsDestSelectionEvent {
  const _TransferFunds({required this.sourceAccount, required this.destinationAccount});
  

 final  TradingAccountModel sourceAccount;
 final  TradingAccountModel destinationAccount;

/// Create a copy of TransferFundsDestSelectionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TransferFundsCopyWith<_TransferFunds> get copyWith => __$TransferFundsCopyWithImpl<_TransferFunds>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TransferFunds&&(identical(other.sourceAccount, sourceAccount) || other.sourceAccount == sourceAccount)&&(identical(other.destinationAccount, destinationAccount) || other.destinationAccount == destinationAccount));
}


@override
int get hashCode => Object.hash(runtimeType,sourceAccount,destinationAccount);

@override
String toString() {
  return 'TransferFundsDestSelectionEvent.transferFunds(sourceAccount: $sourceAccount, destinationAccount: $destinationAccount)';
}


}

/// @nodoc
abstract mixin class _$TransferFundsCopyWith<$Res> implements $TransferFundsDestSelectionEventCopyWith<$Res> {
  factory _$TransferFundsCopyWith(_TransferFunds value, $Res Function(_TransferFunds) _then) = __$TransferFundsCopyWithImpl;
@useResult
$Res call({
 TradingAccountModel sourceAccount, TradingAccountModel destinationAccount
});


$TradingAccountModelCopyWith<$Res> get sourceAccount;$TradingAccountModelCopyWith<$Res> get destinationAccount;

}
/// @nodoc
class __$TransferFundsCopyWithImpl<$Res>
    implements _$TransferFundsCopyWith<$Res> {
  __$TransferFundsCopyWithImpl(this._self, this._then);

  final _TransferFunds _self;
  final $Res Function(_TransferFunds) _then;

/// Create a copy of TransferFundsDestSelectionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? sourceAccount = null,Object? destinationAccount = null,}) {
  return _then(_TransferFunds(
sourceAccount: null == sourceAccount ? _self.sourceAccount : sourceAccount // ignore: cast_nullable_to_non_nullable
as TradingAccountModel,destinationAccount: null == destinationAccount ? _self.destinationAccount : destinationAccount // ignore: cast_nullable_to_non_nullable
as TradingAccountModel,
  ));
}

/// Create a copy of TransferFundsDestSelectionEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res> get sourceAccount {
  
  return $TradingAccountModelCopyWith<$Res>(_self.sourceAccount, (value) {
    return _then(_self.copyWith(sourceAccount: value));
  });
}/// Create a copy of TransferFundsDestSelectionEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res> get destinationAccount {
  
  return $TradingAccountModelCopyWith<$Res>(_self.destinationAccount, (value) {
    return _then(_self.copyWith(destinationAccount: value));
  });
}
}

/// @nodoc


class _UpdateEmptyState implements TransferFundsDestSelectionEvent {
  const _UpdateEmptyState({required this.hasAccounts, required this.hasWallets, required this.isCurrentTabEmpty, required this.currentTabIndex});
  

 final  bool hasAccounts;
 final  bool hasWallets;
 final  bool isCurrentTabEmpty;
 final  int currentTabIndex;

/// Create a copy of TransferFundsDestSelectionEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateEmptyStateCopyWith<_UpdateEmptyState> get copyWith => __$UpdateEmptyStateCopyWithImpl<_UpdateEmptyState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateEmptyState&&(identical(other.hasAccounts, hasAccounts) || other.hasAccounts == hasAccounts)&&(identical(other.hasWallets, hasWallets) || other.hasWallets == hasWallets)&&(identical(other.isCurrentTabEmpty, isCurrentTabEmpty) || other.isCurrentTabEmpty == isCurrentTabEmpty)&&(identical(other.currentTabIndex, currentTabIndex) || other.currentTabIndex == currentTabIndex));
}


@override
int get hashCode => Object.hash(runtimeType,hasAccounts,hasWallets,isCurrentTabEmpty,currentTabIndex);

@override
String toString() {
  return 'TransferFundsDestSelectionEvent.updateEmptyState(hasAccounts: $hasAccounts, hasWallets: $hasWallets, isCurrentTabEmpty: $isCurrentTabEmpty, currentTabIndex: $currentTabIndex)';
}


}

/// @nodoc
abstract mixin class _$UpdateEmptyStateCopyWith<$Res> implements $TransferFundsDestSelectionEventCopyWith<$Res> {
  factory _$UpdateEmptyStateCopyWith(_UpdateEmptyState value, $Res Function(_UpdateEmptyState) _then) = __$UpdateEmptyStateCopyWithImpl;
@useResult
$Res call({
 bool hasAccounts, bool hasWallets, bool isCurrentTabEmpty, int currentTabIndex
});




}
/// @nodoc
class __$UpdateEmptyStateCopyWithImpl<$Res>
    implements _$UpdateEmptyStateCopyWith<$Res> {
  __$UpdateEmptyStateCopyWithImpl(this._self, this._then);

  final _UpdateEmptyState _self;
  final $Res Function(_UpdateEmptyState) _then;

/// Create a copy of TransferFundsDestSelectionEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? hasAccounts = null,Object? hasWallets = null,Object? isCurrentTabEmpty = null,Object? currentTabIndex = null,}) {
  return _then(_UpdateEmptyState(
hasAccounts: null == hasAccounts ? _self.hasAccounts : hasAccounts // ignore: cast_nullable_to_non_nullable
as bool,hasWallets: null == hasWallets ? _self.hasWallets : hasWallets // ignore: cast_nullable_to_non_nullable
as bool,isCurrentTabEmpty: null == isCurrentTabEmpty ? _self.isCurrentTabEmpty : isCurrentTabEmpty // ignore: cast_nullable_to_non_nullable
as bool,currentTabIndex: null == currentTabIndex ? _self.currentTabIndex : currentTabIndex // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

/// @nodoc
mixin _$TransferFundsDestSelectionState {

 TransferFundsDestSelectionProcessState get processState; TradingAccountModel? get destinationAccount; DepositPaymentMethodsModel? get paymentMethodsData; bool get isButtonDisabled; String? get amountInSourceCurrency; String? get amountInDestinationCurrency; String? get conversionRateString; ConversionRateModel? get conversionRateModel; RatesModel? get conversionRateToDestinationCurrency; bool get hasAccounts; bool get hasWallets; bool get isCurrentTabEmpty; int get currentTabIndex; bool get isButtonLoading; String? get errorMessage;
/// Create a copy of TransferFundsDestSelectionState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransferFundsDestSelectionStateCopyWith<TransferFundsDestSelectionState> get copyWith => _$TransferFundsDestSelectionStateCopyWithImpl<TransferFundsDestSelectionState>(this as TransferFundsDestSelectionState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferFundsDestSelectionState&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.destinationAccount, destinationAccount) || other.destinationAccount == destinationAccount)&&(identical(other.paymentMethodsData, paymentMethodsData) || other.paymentMethodsData == paymentMethodsData)&&(identical(other.isButtonDisabled, isButtonDisabled) || other.isButtonDisabled == isButtonDisabled)&&(identical(other.amountInSourceCurrency, amountInSourceCurrency) || other.amountInSourceCurrency == amountInSourceCurrency)&&(identical(other.amountInDestinationCurrency, amountInDestinationCurrency) || other.amountInDestinationCurrency == amountInDestinationCurrency)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString)&&(identical(other.conversionRateModel, conversionRateModel) || other.conversionRateModel == conversionRateModel)&&(identical(other.conversionRateToDestinationCurrency, conversionRateToDestinationCurrency) || other.conversionRateToDestinationCurrency == conversionRateToDestinationCurrency)&&(identical(other.hasAccounts, hasAccounts) || other.hasAccounts == hasAccounts)&&(identical(other.hasWallets, hasWallets) || other.hasWallets == hasWallets)&&(identical(other.isCurrentTabEmpty, isCurrentTabEmpty) || other.isCurrentTabEmpty == isCurrentTabEmpty)&&(identical(other.currentTabIndex, currentTabIndex) || other.currentTabIndex == currentTabIndex)&&(identical(other.isButtonLoading, isButtonLoading) || other.isButtonLoading == isButtonLoading)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}


@override
int get hashCode => Object.hash(runtimeType,processState,destinationAccount,paymentMethodsData,isButtonDisabled,amountInSourceCurrency,amountInDestinationCurrency,conversionRateString,conversionRateModel,conversionRateToDestinationCurrency,hasAccounts,hasWallets,isCurrentTabEmpty,currentTabIndex,isButtonLoading,errorMessage);

@override
String toString() {
  return 'TransferFundsDestSelectionState(processState: $processState, destinationAccount: $destinationAccount, paymentMethodsData: $paymentMethodsData, isButtonDisabled: $isButtonDisabled, amountInSourceCurrency: $amountInSourceCurrency, amountInDestinationCurrency: $amountInDestinationCurrency, conversionRateString: $conversionRateString, conversionRateModel: $conversionRateModel, conversionRateToDestinationCurrency: $conversionRateToDestinationCurrency, hasAccounts: $hasAccounts, hasWallets: $hasWallets, isCurrentTabEmpty: $isCurrentTabEmpty, currentTabIndex: $currentTabIndex, isButtonLoading: $isButtonLoading, errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class $TransferFundsDestSelectionStateCopyWith<$Res>  {
  factory $TransferFundsDestSelectionStateCopyWith(TransferFundsDestSelectionState value, $Res Function(TransferFundsDestSelectionState) _then) = _$TransferFundsDestSelectionStateCopyWithImpl;
@useResult
$Res call({
 TransferFundsDestSelectionProcessState processState, TradingAccountModel? destinationAccount, DepositPaymentMethodsModel? paymentMethodsData, bool isButtonDisabled, String? amountInSourceCurrency, String? amountInDestinationCurrency, String? conversionRateString, ConversionRateModel? conversionRateModel, RatesModel? conversionRateToDestinationCurrency, bool hasAccounts, bool hasWallets, bool isCurrentTabEmpty, int currentTabIndex, bool isButtonLoading, String? errorMessage
});


$TransferFundsDestSelectionProcessStateCopyWith<$Res> get processState;$TradingAccountModelCopyWith<$Res>? get destinationAccount;$DepositPaymentMethodsModelCopyWith<$Res>? get paymentMethodsData;$ConversionRateModelCopyWith<$Res>? get conversionRateModel;$RatesModelCopyWith<$Res>? get conversionRateToDestinationCurrency;

}
/// @nodoc
class _$TransferFundsDestSelectionStateCopyWithImpl<$Res>
    implements $TransferFundsDestSelectionStateCopyWith<$Res> {
  _$TransferFundsDestSelectionStateCopyWithImpl(this._self, this._then);

  final TransferFundsDestSelectionState _self;
  final $Res Function(TransferFundsDestSelectionState) _then;

/// Create a copy of TransferFundsDestSelectionState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? processState = null,Object? destinationAccount = freezed,Object? paymentMethodsData = freezed,Object? isButtonDisabled = null,Object? amountInSourceCurrency = freezed,Object? amountInDestinationCurrency = freezed,Object? conversionRateString = freezed,Object? conversionRateModel = freezed,Object? conversionRateToDestinationCurrency = freezed,Object? hasAccounts = null,Object? hasWallets = null,Object? isCurrentTabEmpty = null,Object? currentTabIndex = null,Object? isButtonLoading = null,Object? errorMessage = freezed,}) {
  return _then(_self.copyWith(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as TransferFundsDestSelectionProcessState,destinationAccount: freezed == destinationAccount ? _self.destinationAccount : destinationAccount // ignore: cast_nullable_to_non_nullable
as TradingAccountModel?,paymentMethodsData: freezed == paymentMethodsData ? _self.paymentMethodsData : paymentMethodsData // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethodsModel?,isButtonDisabled: null == isButtonDisabled ? _self.isButtonDisabled : isButtonDisabled // ignore: cast_nullable_to_non_nullable
as bool,amountInSourceCurrency: freezed == amountInSourceCurrency ? _self.amountInSourceCurrency : amountInSourceCurrency // ignore: cast_nullable_to_non_nullable
as String?,amountInDestinationCurrency: freezed == amountInDestinationCurrency ? _self.amountInDestinationCurrency : amountInDestinationCurrency // ignore: cast_nullable_to_non_nullable
as String?,conversionRateString: freezed == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String?,conversionRateModel: freezed == conversionRateModel ? _self.conversionRateModel : conversionRateModel // ignore: cast_nullable_to_non_nullable
as ConversionRateModel?,conversionRateToDestinationCurrency: freezed == conversionRateToDestinationCurrency ? _self.conversionRateToDestinationCurrency : conversionRateToDestinationCurrency // ignore: cast_nullable_to_non_nullable
as RatesModel?,hasAccounts: null == hasAccounts ? _self.hasAccounts : hasAccounts // ignore: cast_nullable_to_non_nullable
as bool,hasWallets: null == hasWallets ? _self.hasWallets : hasWallets // ignore: cast_nullable_to_non_nullable
as bool,isCurrentTabEmpty: null == isCurrentTabEmpty ? _self.isCurrentTabEmpty : isCurrentTabEmpty // ignore: cast_nullable_to_non_nullable
as bool,currentTabIndex: null == currentTabIndex ? _self.currentTabIndex : currentTabIndex // ignore: cast_nullable_to_non_nullable
as int,isButtonLoading: null == isButtonLoading ? _self.isButtonLoading : isButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of TransferFundsDestSelectionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TransferFundsDestSelectionProcessStateCopyWith<$Res> get processState {
  
  return $TransferFundsDestSelectionProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of TransferFundsDestSelectionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res>? get destinationAccount {
    if (_self.destinationAccount == null) {
    return null;
  }

  return $TradingAccountModelCopyWith<$Res>(_self.destinationAccount!, (value) {
    return _then(_self.copyWith(destinationAccount: value));
  });
}/// Create a copy of TransferFundsDestSelectionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodsModelCopyWith<$Res>? get paymentMethodsData {
    if (_self.paymentMethodsData == null) {
    return null;
  }

  return $DepositPaymentMethodsModelCopyWith<$Res>(_self.paymentMethodsData!, (value) {
    return _then(_self.copyWith(paymentMethodsData: value));
  });
}/// Create a copy of TransferFundsDestSelectionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ConversionRateModelCopyWith<$Res>? get conversionRateModel {
    if (_self.conversionRateModel == null) {
    return null;
  }

  return $ConversionRateModelCopyWith<$Res>(_self.conversionRateModel!, (value) {
    return _then(_self.copyWith(conversionRateModel: value));
  });
}/// Create a copy of TransferFundsDestSelectionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RatesModelCopyWith<$Res>? get conversionRateToDestinationCurrency {
    if (_self.conversionRateToDestinationCurrency == null) {
    return null;
  }

  return $RatesModelCopyWith<$Res>(_self.conversionRateToDestinationCurrency!, (value) {
    return _then(_self.copyWith(conversionRateToDestinationCurrency: value));
  });
}
}


/// @nodoc


class _TransferFundsDestSelectionState implements TransferFundsDestSelectionState {
  const _TransferFundsDestSelectionState({this.processState = const TransferFundsDestSelectionProcessState.startedScreen(), this.destinationAccount, this.paymentMethodsData, this.isButtonDisabled = true, this.amountInSourceCurrency, this.amountInDestinationCurrency, this.conversionRateString, this.conversionRateModel, this.conversionRateToDestinationCurrency, this.hasAccounts = true, this.hasWallets = true, this.isCurrentTabEmpty = false, this.currentTabIndex = 0, this.isButtonLoading = false, this.errorMessage = null});
  

@override@JsonKey() final  TransferFundsDestSelectionProcessState processState;
@override final  TradingAccountModel? destinationAccount;
@override final  DepositPaymentMethodsModel? paymentMethodsData;
@override@JsonKey() final  bool isButtonDisabled;
@override final  String? amountInSourceCurrency;
@override final  String? amountInDestinationCurrency;
@override final  String? conversionRateString;
@override final  ConversionRateModel? conversionRateModel;
@override final  RatesModel? conversionRateToDestinationCurrency;
@override@JsonKey() final  bool hasAccounts;
@override@JsonKey() final  bool hasWallets;
@override@JsonKey() final  bool isCurrentTabEmpty;
@override@JsonKey() final  int currentTabIndex;
@override@JsonKey() final  bool isButtonLoading;
@override@JsonKey() final  String? errorMessage;

/// Create a copy of TransferFundsDestSelectionState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TransferFundsDestSelectionStateCopyWith<_TransferFundsDestSelectionState> get copyWith => __$TransferFundsDestSelectionStateCopyWithImpl<_TransferFundsDestSelectionState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TransferFundsDestSelectionState&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.destinationAccount, destinationAccount) || other.destinationAccount == destinationAccount)&&(identical(other.paymentMethodsData, paymentMethodsData) || other.paymentMethodsData == paymentMethodsData)&&(identical(other.isButtonDisabled, isButtonDisabled) || other.isButtonDisabled == isButtonDisabled)&&(identical(other.amountInSourceCurrency, amountInSourceCurrency) || other.amountInSourceCurrency == amountInSourceCurrency)&&(identical(other.amountInDestinationCurrency, amountInDestinationCurrency) || other.amountInDestinationCurrency == amountInDestinationCurrency)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString)&&(identical(other.conversionRateModel, conversionRateModel) || other.conversionRateModel == conversionRateModel)&&(identical(other.conversionRateToDestinationCurrency, conversionRateToDestinationCurrency) || other.conversionRateToDestinationCurrency == conversionRateToDestinationCurrency)&&(identical(other.hasAccounts, hasAccounts) || other.hasAccounts == hasAccounts)&&(identical(other.hasWallets, hasWallets) || other.hasWallets == hasWallets)&&(identical(other.isCurrentTabEmpty, isCurrentTabEmpty) || other.isCurrentTabEmpty == isCurrentTabEmpty)&&(identical(other.currentTabIndex, currentTabIndex) || other.currentTabIndex == currentTabIndex)&&(identical(other.isButtonLoading, isButtonLoading) || other.isButtonLoading == isButtonLoading)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}


@override
int get hashCode => Object.hash(runtimeType,processState,destinationAccount,paymentMethodsData,isButtonDisabled,amountInSourceCurrency,amountInDestinationCurrency,conversionRateString,conversionRateModel,conversionRateToDestinationCurrency,hasAccounts,hasWallets,isCurrentTabEmpty,currentTabIndex,isButtonLoading,errorMessage);

@override
String toString() {
  return 'TransferFundsDestSelectionState(processState: $processState, destinationAccount: $destinationAccount, paymentMethodsData: $paymentMethodsData, isButtonDisabled: $isButtonDisabled, amountInSourceCurrency: $amountInSourceCurrency, amountInDestinationCurrency: $amountInDestinationCurrency, conversionRateString: $conversionRateString, conversionRateModel: $conversionRateModel, conversionRateToDestinationCurrency: $conversionRateToDestinationCurrency, hasAccounts: $hasAccounts, hasWallets: $hasWallets, isCurrentTabEmpty: $isCurrentTabEmpty, currentTabIndex: $currentTabIndex, isButtonLoading: $isButtonLoading, errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class _$TransferFundsDestSelectionStateCopyWith<$Res> implements $TransferFundsDestSelectionStateCopyWith<$Res> {
  factory _$TransferFundsDestSelectionStateCopyWith(_TransferFundsDestSelectionState value, $Res Function(_TransferFundsDestSelectionState) _then) = __$TransferFundsDestSelectionStateCopyWithImpl;
@override @useResult
$Res call({
 TransferFundsDestSelectionProcessState processState, TradingAccountModel? destinationAccount, DepositPaymentMethodsModel? paymentMethodsData, bool isButtonDisabled, String? amountInSourceCurrency, String? amountInDestinationCurrency, String? conversionRateString, ConversionRateModel? conversionRateModel, RatesModel? conversionRateToDestinationCurrency, bool hasAccounts, bool hasWallets, bool isCurrentTabEmpty, int currentTabIndex, bool isButtonLoading, String? errorMessage
});


@override $TransferFundsDestSelectionProcessStateCopyWith<$Res> get processState;@override $TradingAccountModelCopyWith<$Res>? get destinationAccount;@override $DepositPaymentMethodsModelCopyWith<$Res>? get paymentMethodsData;@override $ConversionRateModelCopyWith<$Res>? get conversionRateModel;@override $RatesModelCopyWith<$Res>? get conversionRateToDestinationCurrency;

}
/// @nodoc
class __$TransferFundsDestSelectionStateCopyWithImpl<$Res>
    implements _$TransferFundsDestSelectionStateCopyWith<$Res> {
  __$TransferFundsDestSelectionStateCopyWithImpl(this._self, this._then);

  final _TransferFundsDestSelectionState _self;
  final $Res Function(_TransferFundsDestSelectionState) _then;

/// Create a copy of TransferFundsDestSelectionState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? processState = null,Object? destinationAccount = freezed,Object? paymentMethodsData = freezed,Object? isButtonDisabled = null,Object? amountInSourceCurrency = freezed,Object? amountInDestinationCurrency = freezed,Object? conversionRateString = freezed,Object? conversionRateModel = freezed,Object? conversionRateToDestinationCurrency = freezed,Object? hasAccounts = null,Object? hasWallets = null,Object? isCurrentTabEmpty = null,Object? currentTabIndex = null,Object? isButtonLoading = null,Object? errorMessage = freezed,}) {
  return _then(_TransferFundsDestSelectionState(
processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as TransferFundsDestSelectionProcessState,destinationAccount: freezed == destinationAccount ? _self.destinationAccount : destinationAccount // ignore: cast_nullable_to_non_nullable
as TradingAccountModel?,paymentMethodsData: freezed == paymentMethodsData ? _self.paymentMethodsData : paymentMethodsData // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethodsModel?,isButtonDisabled: null == isButtonDisabled ? _self.isButtonDisabled : isButtonDisabled // ignore: cast_nullable_to_non_nullable
as bool,amountInSourceCurrency: freezed == amountInSourceCurrency ? _self.amountInSourceCurrency : amountInSourceCurrency // ignore: cast_nullable_to_non_nullable
as String?,amountInDestinationCurrency: freezed == amountInDestinationCurrency ? _self.amountInDestinationCurrency : amountInDestinationCurrency // ignore: cast_nullable_to_non_nullable
as String?,conversionRateString: freezed == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String?,conversionRateModel: freezed == conversionRateModel ? _self.conversionRateModel : conversionRateModel // ignore: cast_nullable_to_non_nullable
as ConversionRateModel?,conversionRateToDestinationCurrency: freezed == conversionRateToDestinationCurrency ? _self.conversionRateToDestinationCurrency : conversionRateToDestinationCurrency // ignore: cast_nullable_to_non_nullable
as RatesModel?,hasAccounts: null == hasAccounts ? _self.hasAccounts : hasAccounts // ignore: cast_nullable_to_non_nullable
as bool,hasWallets: null == hasWallets ? _self.hasWallets : hasWallets // ignore: cast_nullable_to_non_nullable
as bool,isCurrentTabEmpty: null == isCurrentTabEmpty ? _self.isCurrentTabEmpty : isCurrentTabEmpty // ignore: cast_nullable_to_non_nullable
as bool,currentTabIndex: null == currentTabIndex ? _self.currentTabIndex : currentTabIndex // ignore: cast_nullable_to_non_nullable
as int,isButtonLoading: null == isButtonLoading ? _self.isButtonLoading : isButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of TransferFundsDestSelectionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TransferFundsDestSelectionProcessStateCopyWith<$Res> get processState {
  
  return $TransferFundsDestSelectionProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of TransferFundsDestSelectionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res>? get destinationAccount {
    if (_self.destinationAccount == null) {
    return null;
  }

  return $TradingAccountModelCopyWith<$Res>(_self.destinationAccount!, (value) {
    return _then(_self.copyWith(destinationAccount: value));
  });
}/// Create a copy of TransferFundsDestSelectionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodsModelCopyWith<$Res>? get paymentMethodsData {
    if (_self.paymentMethodsData == null) {
    return null;
  }

  return $DepositPaymentMethodsModelCopyWith<$Res>(_self.paymentMethodsData!, (value) {
    return _then(_self.copyWith(paymentMethodsData: value));
  });
}/// Create a copy of TransferFundsDestSelectionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ConversionRateModelCopyWith<$Res>? get conversionRateModel {
    if (_self.conversionRateModel == null) {
    return null;
  }

  return $ConversionRateModelCopyWith<$Res>(_self.conversionRateModel!, (value) {
    return _then(_self.copyWith(conversionRateModel: value));
  });
}/// Create a copy of TransferFundsDestSelectionState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RatesModelCopyWith<$Res>? get conversionRateToDestinationCurrency {
    if (_self.conversionRateToDestinationCurrency == null) {
    return null;
  }

  return $RatesModelCopyWith<$Res>(_self.conversionRateToDestinationCurrency!, (value) {
    return _then(_self.copyWith(conversionRateToDestinationCurrency: value));
  });
}
}

/// @nodoc
mixin _$TransferFundsDestSelectionProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferFundsDestSelectionProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'TransferFundsDestSelectionProcessState()';
}


}

/// @nodoc
class $TransferFundsDestSelectionProcessStateCopyWith<$Res>  {
$TransferFundsDestSelectionProcessStateCopyWith(TransferFundsDestSelectionProcessState _, $Res Function(TransferFundsDestSelectionProcessState) __);
}


/// @nodoc


class StartedScreen implements TransferFundsDestSelectionProcessState {
  const StartedScreen();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StartedScreen);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'TransferFundsDestSelectionProcessState.startedScreen()';
}


}




/// @nodoc


class TransferStatusScreen implements TransferFundsDestSelectionProcessState {
  const TransferStatusScreen(this.sourceAccount, this.destinationAccount, this.amount, this.status);
  

 final  TradingAccountModel sourceAccount;
 final  TradingAccountModel destinationAccount;
 final  String amount;
 final  TransferStatus status;

/// Create a copy of TransferFundsDestSelectionProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransferStatusScreenCopyWith<TransferStatusScreen> get copyWith => _$TransferStatusScreenCopyWithImpl<TransferStatusScreen>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferStatusScreen&&(identical(other.sourceAccount, sourceAccount) || other.sourceAccount == sourceAccount)&&(identical(other.destinationAccount, destinationAccount) || other.destinationAccount == destinationAccount)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.status, status) || other.status == status));
}


@override
int get hashCode => Object.hash(runtimeType,sourceAccount,destinationAccount,amount,status);

@override
String toString() {
  return 'TransferFundsDestSelectionProcessState.transferStatusScreen(sourceAccount: $sourceAccount, destinationAccount: $destinationAccount, amount: $amount, status: $status)';
}


}

/// @nodoc
abstract mixin class $TransferStatusScreenCopyWith<$Res> implements $TransferFundsDestSelectionProcessStateCopyWith<$Res> {
  factory $TransferStatusScreenCopyWith(TransferStatusScreen value, $Res Function(TransferStatusScreen) _then) = _$TransferStatusScreenCopyWithImpl;
@useResult
$Res call({
 TradingAccountModel sourceAccount, TradingAccountModel destinationAccount, String amount, TransferStatus status
});


$TradingAccountModelCopyWith<$Res> get sourceAccount;$TradingAccountModelCopyWith<$Res> get destinationAccount;

}
/// @nodoc
class _$TransferStatusScreenCopyWithImpl<$Res>
    implements $TransferStatusScreenCopyWith<$Res> {
  _$TransferStatusScreenCopyWithImpl(this._self, this._then);

  final TransferStatusScreen _self;
  final $Res Function(TransferStatusScreen) _then;

/// Create a copy of TransferFundsDestSelectionProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? sourceAccount = null,Object? destinationAccount = null,Object? amount = null,Object? status = null,}) {
  return _then(TransferStatusScreen(
null == sourceAccount ? _self.sourceAccount : sourceAccount // ignore: cast_nullable_to_non_nullable
as TradingAccountModel,null == destinationAccount ? _self.destinationAccount : destinationAccount // ignore: cast_nullable_to_non_nullable
as TradingAccountModel,null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as String,null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as TransferStatus,
  ));
}

/// Create a copy of TransferFundsDestSelectionProcessState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res> get sourceAccount {
  
  return $TradingAccountModelCopyWith<$Res>(_self.sourceAccount, (value) {
    return _then(_self.copyWith(sourceAccount: value));
  });
}/// Create a copy of TransferFundsDestSelectionProcessState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res> get destinationAccount {
  
  return $TradingAccountModelCopyWith<$Res>(_self.destinationAccount, (value) {
    return _then(_self.copyWith(destinationAccount: value));
  });
}
}

/// @nodoc


class ErrorTransfer implements TransferFundsDestSelectionProcessState {
  const ErrorTransfer();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ErrorTransfer);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'TransferFundsDestSelectionProcessState.errorTransfer()';
}


}




// dart format on
