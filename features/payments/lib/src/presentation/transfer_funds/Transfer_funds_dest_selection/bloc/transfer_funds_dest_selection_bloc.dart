import 'dart:async';
import 'dart:developer';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/analytics/transfer_analytics_event.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';

import 'package:payment/src/data/transfer_funds/transfer_funds_request_model.dart';
import 'package:payment/src/domain/data/transfer_status.dart';
import 'package:payment/src/domain/exceptions/transfer_exception/transfer_exception.dart';
import 'package:payment/src/domain/usecase/submit_transfer_use_case.dart';

part 'transfer_funds_dest_selection_event.dart';
part 'transfer_funds_dest_selection_state.dart';
part 'transfer_funds_dest_selection_bloc.freezed.dart';

class TransferFundsDestSelectionBloc
    extends
        Bloc<TransferFundsDestSelectionEvent, TransferFundsDestSelectionState> {
  final SubmitTransferUseCase _submitTransferUseCase;
  final TransferAnalyticsEvent _transferAnalyticsEvent;
  TransferFundsDestSelectionBloc({
    required SubmitTransferUseCase submitTransferUseCase,
    required TransferAnalyticsEvent transferAnalyticsEvent,
  }) : _submitTransferUseCase = submitTransferUseCase,
       _transferAnalyticsEvent = transferAnalyticsEvent,
       super(TransferFundsDestSelectionState()) {
    on<_SelectDestinationAccount>(_selectDestinationAccount);
    on<_TransferFunds>(_transferFunds);
    on<_ChangeButtonStateAndUpdateAmount>(_changeButtonStateAndUpdateAmount);
    on<_UpdateEmptyState>(_updateEmptyState);
  }

  void _selectDestinationAccount(
    _SelectDestinationAccount event,
    Emitter<TransferFundsDestSelectionState> emit,
  ) {
    emit(state.copyWith(destinationAccount: event.account));
  }

  FutureOr<void> _transferFunds(
    _TransferFunds event,
    Emitter<TransferFundsDestSelectionState> emit,
  ) async {
    final amountInSourceCurrency = double.parse(state.amountInSourceCurrency!);
    final amountInDestinationCurrency = double.parse(
      state.amountInDestinationCurrency ?? state.amountInSourceCurrency!,
    );

    final transferModel = TransferFundsRequestModel(
      accountInfo: AccountInfo(
        sourceTradingAccountId: event.sourceAccount.recordId,
        sourceAccountCurrency: event.sourceAccount.homeCurrency,
        destinationTradingAccountId: event.destinationAccount.recordId,
        destinationAccountCurrency: event.destinationAccount.homeCurrency,
      ),
      amountDetails: AmountInfo(
        conversionRateString:
            state.conversionRateString ??
            '1 ${event.sourceAccount.homeCurrency} = 1 ${event.sourceAccount.homeCurrency}',
        amountInSourceCurrency: amountInSourceCurrency,
        amountInDestinationCurrency: amountInDestinationCurrency,
        conversionRateToDestinationCurrency:
            state.conversionRateToDestinationCurrency?.rate ?? 1,
      ),
    );
    log('tempModel: $transferModel');

    _transferAnalyticsEvent.transferInititated(
      amount: state.amountInSourceCurrency!,
      convertedAmount: state.amountInDestinationCurrency!,
      accountCurrency: event.sourceAccount.homeCurrency,
      selectedCurrency: event.destinationAccount.homeCurrency,
      conversionRate: state.conversionRateString!,
    );

    emit(state.copyWith(isButtonLoading: true));
    final result = await _submitTransferUseCase(model: transferModel).run();
    if (!isClosed) {
      emit(state.copyWith(isButtonLoading: false));
    }
    result.fold(
      (exception) {
        if (exception is TransferException) {
          switch (exception) {
            default:
              log('Transfer Error: ${exception.toString()}');
              if (!isClosed) {
                emit(
                  state.copyWith(
                    isButtonLoading: false,
                    errorMessage: exception.message,
                  ),
                );
              }
          }
        } else {
          log('Unexpected Transfer Error: ${exception.toString()}');
          if (!isClosed) {
            emit(
              state.copyWith(
                isButtonLoading: false,
                errorMessage: 'Something went wrong! Please try again later.',
              ),
            );
          }
        }
        addError(exception);
      },
      (r) {
        if (r.success) {
          if (!isClosed) {
            switch (r.data?.status) {
              case TransferStatus.New:
              case TransferStatus.PendingApproval:
              case TransferStatus.Successful:
              case TransferStatus.Rejected:
                _transferAnalyticsEvent.transferCompleted(
                  status: r.data!.status!.name,
                );
                emit(
                  state.copyWith(
                    processState:
                        TransferFundsDestSelectionProcessState.transferStatusScreen(
                          event.sourceAccount,
                          event.destinationAccount,
                          state.amountInDestinationCurrency!,
                          r.data!.status!,
                        ),
                  ),
                );
                break;
              default:
                emit(
                  state.copyWith(
                    processState:
                        TransferFundsDestSelectionProcessState.errorTransfer(),
                  ),
                );
            }
          }
        } else {
          if (!isClosed) {
            emit(
              state.copyWith(
                processState:
                    TransferFundsDestSelectionProcessState.errorTransfer(),
              ),
            );
          }
        }
      },
    );
  }

  FutureOr<void> _changeButtonStateAndUpdateAmount(
    _ChangeButtonStateAndUpdateAmount event,
    Emitter<TransferFundsDestSelectionState> emit,
  ) {
    emit(
      state.copyWith(
        amountInDestinationCurrency: event.amountInDestinationCurrency,
        amountInSourceCurrency: event.amountInSourceCurrency,
        isButtonDisabled: event.isDisabled,
        conversionRateModel: event.conversionRateModel,
        conversionRateString: event.conversionRateString,
        conversionRateToDestinationCurrency:
            event.conversionRateToDestinationCurrency,
        errorMessage: null,
      ),
    );
  }

  FutureOr<void> _updateEmptyState(
    _UpdateEmptyState event,
    Emitter<TransferFundsDestSelectionState> emit,
  ) {
    emit(
      state.copyWith(
        hasAccounts: event.hasAccounts,
        hasWallets: event.hasWallets,
        isCurrentTabEmpty: event.isCurrentTabEmpty,
        currentTabIndex: event.currentTabIndex,
      ),
    );
  }
}
