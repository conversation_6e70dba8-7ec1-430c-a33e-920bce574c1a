import 'dart:async';
import 'dart:developer';

import 'package:domain/domain.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';
import 'package:payment/src/data/withdraw_request_model/withdraw_request_model.dart';
import 'package:payment/src/data/withdraw_response_model/withdraw_response_model.dart';
import 'package:payment/src/data/withdraw_transfer_type_response_model/withdraw_transfer_type_response_model.dart';
import 'package:payment/src/domain/model/withdraw_add_new_account_param/withdraw_add_new_account_param.dart';
import 'package:payment/src/domain/model/withdraw_flow_params/withdraw_flow_params.dart';
import 'package:payment/src/domain/usecase/get_transfer_type_usecase.dart';
import 'package:payment/src/domain/usecase/withdrawal_usecase.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:payment/src/domain/data/withdraw_status_type.dart';

@injectable
part 'withdraw_add_new_bank_bloc.freezed.dart';
part 'withdraw_add_new_bank_event.dart';
part 'withdraw_add_new_bank_state.dart';

class WithdrawAddNewBankBloc
    extends Bloc<WithdrawAddNewBankEvent, WithdrawAddNewBankState> {
  final GetCountryUseCase _getCountryUseCase;
  final GetTransferTypeUseCase _getTransferTypeUseCase;
  final PaymentNavigation _paymentNavigation;
  final WithdrawalUseCase _withdrawalUseCase;
  final WithdrawAnalyticsEvent _withdrawAnalyticsEvent;

  WithdrawAddNewBankBloc({
    required GetCountryUseCase getCountryUseCase,
    required GetTransferTypeUseCase getTransferTypeUseCase,
    required PaymentNavigation paymentNavigation,
    required WithdrawalUseCase withdrawalUseCase,
    required WithdrawAnalyticsEvent withdrawAnalyticsEvent,
  }) : _getCountryUseCase = getCountryUseCase,
       _getTransferTypeUseCase = getTransferTypeUseCase,
       _paymentNavigation = paymentNavigation,
       _withdrawalUseCase = withdrawalUseCase,
       _withdrawAnalyticsEvent = withdrawAnalyticsEvent,
       super(const WithdrawAddNewBankState()) {
    on<OnLocationOfBankChangedEvent>(_onLocationOfBankChanged);
    on<GetCountriesForLocationOfBankEvent>(_getCountriesForLocationOfBank);
    on<OnRequestWithdrawButtonPressedEvent>(_onRequestWithdrawButtonPressed);
    on<OnWithdrawalDetailsPressedEvent>(_onWithdrawalDetailsPressed);
    on<GetTransferTypeEvent>(_getTransferType);
    on<OnTransferTypeChangedEvent>(_onTransferTypeChanged);
    on<OnFormChangedEvent>(_onFormChanged);
    on<OnAccountManuallyEnteredEvent>(_onAccountManuallyEntered);
    on<ResetProcessStateEvent>(_resetProcessState);
    on<OnFeesChangedEvent>(_onFeesChanged);
    add(GetCountriesForLocationOfBankEvent());
  }

  Future<void> _getCountriesForLocationOfBank(
    GetCountriesForLocationOfBankEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) async {
    emit(
      state.copyWith(isBankLocationLoading: true, processState: LoadedState()),
    );
    try {
      final result = await _getCountryUseCase().run();
      if (isClosed) return;
      result.fold(
        (l) {
          if (l is GetCountriesException) {
            switch (l) {
              case GetCountriesUnknownError(:final message):
                emit(
                  state.copyWith(
                    processState: ErrorState(message: message),
                    isBankLocationLoading: false,
                  ),
                );
            }
          } else {
            emit(
              state.copyWith(
                processState: ErrorState(
                  message: "Something went wrong! Please try again letter",
                ),
                isBankLocationLoading: false,
              ),
            );
          }
        },
        (r) {
          final countries = r.data?.countries ?? [];
          emit(
            state.copyWith(
              countries: countries,
              processState: LoadedState(),
              isBankLocationLoading: false,
            ),
          );
        },
      );
    } catch (e, stacktrace) {
      if (!isClosed) {
        log('Exception in _onLocationOfBankChanged: $e\n$stacktrace');
        emit(
          state.copyWith(
            isBankLocationLoading: false,
            processState: ErrorState(
              message: "Something went wrong! Please try again letter",
            ),
          ),
        );
      }
    }
  }

  void _onLocationOfBankChanged(
    OnLocationOfBankChangedEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) {
    emit(
      state.copyWith(
        selectedBankLocationIndex: event.index,
        transferType: [],
        selectedTransferTypeIndex: -1,
      ),
    );
    final selectedCountry = state.countries.elementAtOrNull(event.index);
    if (selectedCountry?.brokerId == null) {
      log('brokerId is null can\'t get transfer type');
      return;
    }
    add(
      GetTransferTypeEvent(
        brokerId: selectedCountry!.brokerId!,
        countryCodeInThreeCharacter: selectedCountry.code,
      ),
    );
  }

  FutureOr<void> _onTransferTypeChanged(
    OnTransferTypeChangedEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) {
    log('onTransferTypeChanged: ${event.index}');

    _withdrawAnalyticsEvent.withdrawBankTransferTypeSelected(
      selectedType: state.transferType.elementAtOrNull(event.index) ?? '',
    );

    emit(
      state.copyWith(
        selectedTransferTypeIndex: event.index,
        isButtonDisabled: true,
      ),
    );
  }

  Future<void> _onRequestWithdrawButtonPressed(
    OnRequestWithdrawButtonPressedEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) async {
    FocusManager.instance.primaryFocus?.unfocus();
    SystemChannels.textInput.invokeMethod('TextInput.hide');
    log('onRequestWithdrawButtonPressed');
    final request = WithdrawRequestModel(
      paymentType: event.withdrawFlowParams.paymentType.name,
      accountInfo: AccountInfo(
        tradingAccountId: event.withdrawFlowParams.tradingAccountId,
        accountCurrency: event.withdrawFlowParams.accountCurrency,
      ),
      amountDetails: Amount(
        selectedCurrency: event.withdrawFlowParams.currency,
        conversionRateString: event.withdrawFlowParams.conversionRateString,
        selectedCurrencyAmount: event.withdrawFlowParams.amount,
        accountCurrencyAmount: event.withdrawFlowParams.convertedAmount,
        conversionRateToAccountCurrency:
            event.withdrawFlowParams.conversionRate,
      ),
      metadata: Metadata.bank(
        transferType: event.withdrawAddNewAccountParam.transferType!,
        bankAccount: BankAccount(
          country: event.withdrawAddNewAccountParam.country!.code,
          accountHolder: event.withdrawAddNewAccountParam.nameOfAccountHolder!,
          bankName: event.withdrawAddNewAccountParam.bankName!,
          accountNickname: event.withdrawAddNewAccountParam.accountNickname!,
          branchName: event.withdrawAddNewAccountParam.branchName!,
          swiftBic: event.withdrawAddNewAccountParam.swiftBic!,
          iban: event.withdrawAddNewAccountParam.accountNumber!,
        ),
      ),
    );

    _withdrawAnalyticsEvent.withdrawAddNewBankInititate();

    try {
      emit(state.copyWith(isRequestWithdrawButtonLoading: true));
      log('request for withdraw : $request');
      final result = await _withdrawalUseCase(request: request).run();
      result.fold(
        (failure) {
          log('error on withdraw ${failure}');
          if (!isClosed) {
            emit(
              state.copyWith(
                processState: ErrorState(message: failure.toString()),
                isRequestWithdrawButtonLoading: false,
              ),
            );
          }
        },
        (WithdrawResponseModel withdrawResponseModel) {
          switch (withdrawResponseModel) {
            case final WithdrawSuccessResponse _success:
              log('success on withdraw ${_success.data}');
              if (!isClosed) {
                _withdrawAnalyticsEvent.withdrawAddNewBankComplete(
                  status: _success.data.status?.name ?? '',
                );
                emit(
                  state.copyWith(
                    processState: SuccessState(),
                    isRequestWithdrawButtonLoading: false,
                    withdrawStatus: _success.data.status,
                  ),
                );
              }
              // todo(sambhav): handle based on status rather than navigating to bank upload screen
              _paymentNavigation.goToWithdrawNewBankUploadDocScreen(
                operationId: _success.data.operationId!,
                tradingAccountId: event.withdrawFlowParams.tradingAccountId,
                replace: true,
              );
              break;
            case final WithdrawInternalServerErrorResponse internalServerError:
              log(
                'internal server error on withdraw ${internalServerError.error}',
              );
              if (!isClosed) {
                emit(
                  state.copyWith(
                    processState: ErrorState(
                      message: internalServerError.error,
                    ),
                    isRequestWithdrawButtonLoading: false,
                  ),
                );
              }
              break;
          }
        },
      );
    } catch (e) {
      log(' error on withdrawal options $e');
      if (!isClosed) {
        emit(
          state.copyWith(
            processState: ErrorState(message: e.toString()),
            isRequestWithdrawButtonLoading: false,
          ),
        );
      }
    }
  }

  FutureOr<void> _onWithdrawalDetailsPressed(
    OnWithdrawalDetailsPressedEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) {
    log('onWithdrawalDetailsPressed');
  }

  Future<void> _getTransferType(
    GetTransferTypeEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) async {
    emit(
      state.copyWith(isTransferTypeLoading: true, processState: LoadedState()),
    );
    try {
      final result =
          await _getTransferTypeUseCase(
            brokerId: event.brokerId,
            //todo (sambhav): get 3 character country code from selected country but its not coming for now
            countryCodeInThreeCharacter: event.countryCodeInThreeCharacter,
          ).run();
      if (isClosed) return;
      result.fold(
        (l) {
          // todo(sambhav) : handle error message
          emit(
            state.copyWith(
              processState: ErrorState(
                message: "Something went wrong! Please try again letter",
              ),
              isTransferTypeLoading: false,
            ),
          );
        },
        (r) {
          switch (r) {
            case WithdrawTransferTypeResponseSuccessModel(:final data):
              _withdrawAnalyticsEvent.withdrawBankTransferTypesLoaded(
                types: data.transferTypes.join(','),
              );
              emit(
                state.copyWith(
                  processState: LoadedState(),
                  isTransferTypeLoading: false,
                  transferType: data.transferTypes,
                ),
              );
              break;
            case WithdrawTransferTypeResponseModelInternalServerError(
              :final error,
            ):
              emit(
                state.copyWith(
                  processState: ErrorState(message: error),
                  isTransferTypeLoading: false,
                ),
              );
          }
        },
      );
    } catch (e, stacktrace) {
      if (!isClosed) {
        log('Exception in _onLocationOfBankChanged: $e\n$stacktrace');
        emit(
          state.copyWith(
            isTransferTypeLoading: false,
            processState: ErrorState(
              message: "Something went wrong! Please try again letter",
            ),
          ),
        );
      }
    }
  }

  FutureOr<void> _onFormChanged(
    OnFormChangedEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) {
    final isValid = _isDataValid(event.withdrawAddNewAccountParam);
    log('onFormChanged: $isValid');
    final bankName = (event.withdrawAddNewAccountParam.bankName ?? "")
        .replaceAll(" ", "");
    final fullAccountNumber =
        event.withdrawAddNewAccountParam.accountNumber ?? "";
    final accountNumber = (fullAccountNumber.length > 4
            ? fullAccountNumber.substring(fullAccountNumber.length - 4)
            : fullAccountNumber)
        .replaceAll(" ", "");

    if (bankName.isNotEmpty &&
        accountNumber.isNotEmpty &&
        !state.isAccountNameManuallyEntered) {
      if (!isClosed) {
        final autoCreatedAccountName = '${bankName}${accountNumber}';
        emit(
          state.copyWith(autoCreatedAccountNickname: autoCreatedAccountName),
        );
      }
    } else {
      emit(state.copyWith(autoCreatedAccountNickname: ""));
    }

    if (!isClosed) {
      emit(
        state.copyWith(
          isButtonDisabled: !isValid,
          formData: event.withdrawAddNewAccountParam,
        ),
      );
    }
  }

  FutureOr<void> _onAccountManuallyEntered(
    OnAccountManuallyEnteredEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) {
    emit(state.copyWith(isAccountNameManuallyEntered: true));
  }

  FutureOr<void> _resetProcessState(
    ResetProcessStateEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) {
    emit(state.copyWith(processState: LoadedState()));
  }

  FutureOr<void> _onFeesChanged(
    OnFeesChangedEvent event,
    Emitter<WithdrawAddNewBankState> emit,
  ) {
    _withdrawAnalyticsEvent.withdrawBankFeesChanged(
      fees: event.fees.toString(),
      transferType: event.withdrawAddNewAccountParam.transferType ?? '',
    );

    // Validate withdrawal amount with new fees
    final validationResult = _validateWithdrawalAmount(
      event.withdrawFlowParams.amount,
      event.withdrawFlowParams.accountBalance,
      event.fees,
      event.withdrawFlowParams.accountCurrency,
    );

    emit(
      state.copyWith(
        withdrawalFees: event.fees,
        hasInsufficientFunds: validationResult.hasInsufficientFunds,
        isButtonDisabled:
            !_isDataValid(event.withdrawAddNewAccountParam) ||
            validationResult.hasInsufficientFunds,
        insufficientFundsMessage:
            '', // Will be generated in UI with localization
        // Store validation data for UI to generate localized message
        validationWithdrawalAmount: event.withdrawFlowParams.amount,
        validationAccountBalance: event.withdrawFlowParams.accountBalance,
        validationWithdrawalFees: event.fees,
        validationCurrency: event.withdrawFlowParams.accountCurrency,
      ),
    );
  }

  /// Validates if the withdrawal amount plus fees exceeds the account balance
  ({
    bool hasInsufficientFunds,
    String Function(BuildContext) errorMessageBuilder,
  })
  _validateWithdrawalAmount(
    num withdrawalAmount,
    num accountBalance,
    num withdrawalFees,
    String currency,
  ) {
    // Skip validation if amount is 0 or empty
    if (withdrawalAmount <= 0) {
      return (hasInsufficientFunds: false, errorMessageBuilder: (_) => '');
    }

    // Calculate total required amount (withdrawal amount + fees)
    final totalRequired = withdrawalAmount + withdrawalFees;

    // Check if total required exceeds account balance
    if (totalRequired > accountBalance) {
      final formattedBalance = accountBalance.toStringAsFixed(2);
      final formattedTotal = totalRequired.toStringAsFixed(2);

      String Function(BuildContext) errorMessageBuilder;
      if (withdrawalFees > 0) {
        final formattedFees = withdrawalFees.toStringAsFixed(2);
        errorMessageBuilder = (context) {
          final localization = EquitiLocalization.of(context);
          return localization.payments_insufficient_funds_with_fees(
            formattedBalance,
            formattedTotal,
            formattedFees,
            currency,
          );
        };
      } else {
        errorMessageBuilder = (context) {
          final localization = EquitiLocalization.of(context);
          return localization.payments_insufficient_funds_without_fees(
            formattedBalance,
            formattedTotal,
            currency,
          );
        };
      }

      return (
        hasInsufficientFunds: true,
        errorMessageBuilder: errorMessageBuilder,
      );
    }

    return (hasInsufficientFunds: false, errorMessageBuilder: (_) => '');
  }

  bool _isDataValid(WithdrawAddNewAccountParam withdrawAddNewAccountParam) {
    final selectedCountry =
        state.countries.isNotEmpty && state.selectedBankLocationIndex != -1
            ? state.countries.elementAtOrNull(state.selectedBankLocationIndex)
            : null;
    final selectedTransferType =
        state.transferType.isNotEmpty && state.selectedTransferTypeIndex != -1
            ? state.transferType.elementAtOrNull(
              state.selectedTransferTypeIndex,
            )
            : null;
    final isValid =
        withdrawAddNewAccountParam
            .copyWith(
              country: selectedCountry,
              transferType: selectedTransferType,
            )
            .isValid;
    return isValid;
  }
}
