import 'dart:async';
import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';
import 'package:payment/src/data/upload_document_request_model/upload_document_request_model.dart';
import 'package:payment/src/domain/usecase/upload_document_usecase.dart';

@injectable
part 'withdraw_new_bank_upload_doc_bloc.freezed.dart';
part 'withdraw_new_bank_upload_doc_event.dart';
part 'withdraw_new_bank_upload_doc_state.dart';

class WithdrawNewBankUploadDocBloc
    extends Bloc<WithdrawNewBankUploadDocEvent, WithdrawNewBankUploadDocState> {
  final UploadDocumentUseCase _uploadDocumentUseCase;
  final WithdrawAnalyticsEvent _withdrawAnalyticsEvent;

  WithdrawNewBankUploadDocBloc({
    required UploadDocumentUseCase uploadDocumentUseCase,
    required WithdrawAnalyticsEvent withdrawAnalyticsEvent,
  }) : _uploadDocumentUseCase = uploadDocumentUseCase,
       _withdrawAnalyticsEvent = withdrawAnalyticsEvent,
       super(const WithdrawNewBankUploadDocState()) {
    on<OnUploadDocumentPressed>(_onUploadDocumentPressed);
    on<OnSubmitPressed>(_onSubmitPressed);
    on<OnRemoveFilePressed>(_onRemoveFilePressed);
    on<OnClearRejectedFiles>(_onClearRejectedFiles);
    on<ResetMaxFilesReached>(_onResetMaxFilesReached);
  }

  Future<void> _onUploadDocumentPressed(
    OnUploadDocumentPressed event,
    Emitter<WithdrawNewBankUploadDocState> emit,
  ) async {
    if (state.files.length >= 5) {
      emit(state.copyWith(isMaxFilesReached: true));
      return;
    }
    final result = await FilePicker.platform.pickFiles(
      allowMultiple: false,
      type: FileType.custom,
      allowedExtensions: ["JPG", "JPEG", "PNG", "PDF", "TIFF"],
    );
    if (result != null && result.files.isNotEmpty) {
      // Return list of selected file paths
      if (!isClosed) {
        const maxFileSizeInBytes = 10 * 1024 * 1024; // 10MB in bytes
        final validFiles = <PlatformFile>[];
        final rejectedFiles = <PlatformFile>[];
        int lastFileSize = 0;
        String lastFileExtension = '';

        for (final file in result.files) {
          if (file.size > maxFileSizeInBytes) {
            log(
              'File "${file.name}" (${(file.size / (1024 * 1024)).toStringAsFixed(2)} MB) exceeds 10MB limit and was removed',
            );
            rejectedFiles.add(file);
          } else {
            lastFileExtension = file.extension ?? '';
            lastFileSize = file.size;
            validFiles.add(file);
          }
        }

        _withdrawAnalyticsEvent.withdrawBankDocUploadFileSelected(
          size: lastFileSize.toString(),
          fileType: lastFileExtension,
        );
        emit(
          state.copyWith(
            files: [...state.files, ...validFiles],
            rejectedFiles: rejectedFiles,
          ),
        );
      }
    } else {
      log('User canceled the picker');
    }
  }

  Future<void> _onSubmitPressed(
    OnSubmitPressed event,
    Emitter<WithdrawNewBankUploadDocState> emit,
  ) async {
    log('onSubmitPressed');
    emit(
      state.copyWith(
        status: DuploFileUploadStatus.uploading,
        isButtonLoading: true,
      ),
    );
    final result =
        await _uploadDocumentUseCase(
          filesPath: state.files.map((file) => file.path!).toList(),
          request: UploadDocumentRequestModel(
            documentType: "BankStatement_PaymentVerification",
            operationId: event.operationId,
            tradingAccountId: event.tradingAccountId,
          ),
        ).run();
    result.fold(
      (l) {
        log('Error while uploading document: ${l.toString()}');
        if (!isClosed) {
          emit(
            state.copyWith(
              status: DuploFileUploadStatus.failure,
              isButtonLoading: false,
            ),
          );
        }
      },
      (r) {
        if (!isClosed) {
          log('Success while uploading document: ${r.toString()}');
          emit(
            state.copyWith(
              status: DuploFileUploadStatus.success,
              isButtonLoading: false,
            ),
          );
        }
      },
    );
  }

  FutureOr<void> _onRemoveFilePressed(
    OnRemoveFilePressed event,
    Emitter<WithdrawNewBankUploadDocState> emit,
  ) {
    final existingFiles = [...state.files];
    existingFiles.removeAt(event.index);
    emit(
      state.copyWith(
        files: existingFiles,
        isMaxFilesReached: existingFiles.length >= 5,
      ),
    );
  }

  FutureOr<void> _onClearRejectedFiles(
    OnClearRejectedFiles event,
    Emitter<WithdrawNewBankUploadDocState> emit,
  ) {
    emit(state.copyWith(rejectedFiles: []));
  }

  FutureOr<void> _onResetMaxFilesReached(
    ResetMaxFilesReached event,
    Emitter<WithdrawNewBankUploadDocState> emit,
  ) {
    emit(state.copyWith(isMaxFilesReached: false));
  }
}
