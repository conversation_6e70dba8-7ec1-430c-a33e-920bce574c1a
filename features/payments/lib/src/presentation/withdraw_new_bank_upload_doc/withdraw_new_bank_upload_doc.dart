import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/presentation/widgets/withdraw_status_screen.dart';
import 'package:payment/src/presentation/withdraw_new_bank_upload_doc/bloc/withdraw_new_bank_upload_doc_bloc.dart';
import 'package:payment/src/presentation/withdraw_new_bank_upload_doc/widgets/withdraw_new_bank_upload_doc_initial_state.dart';

class WithdrawNewBankUploadDoc extends StatelessWidget {
  const WithdrawNewBankUploadDoc({
    super.key,
    required this.operationId,
    required this.tradingAccountId,
  });
  final String operationId;
  final String tradingAccountId;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (ctx) {
        diContainer<WithdrawAnalyticsEvent>().withdrawBankDocUploadPageLoaded();
        return diContainer<WithdrawNewBankUploadDocBloc>();
      },
      child: Bloc<PERSON>uilder<
        WithdrawNewBankUploadDocBloc,
        WithdrawNewBankUploadDocState
      >(
        buildWhen: (previous, current) => previous != current,
        builder: (ctx, state) {
          return switch (state.status) {
            DuploFileUploadStatus.initial ||
            DuploFileUploadStatus.uploading ||
            DuploFileUploadStatus
                .failure => WithdrawNewBankUploadDocInitialState(
              operationId: operationId,
              tradingAccountId: tradingAccountId,
            ),
            DuploFileUploadStatus.success => WithdrawStatusScreen.submitted(),
          };
        },
      ),
    );
  }
}
