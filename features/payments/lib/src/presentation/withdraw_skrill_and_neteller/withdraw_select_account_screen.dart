import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart'
    show EquitiLocalization;
import 'package:flutter/material.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';
import 'package:payment/src/data/payment_method/withdraw_payment_methods_model/withdraw_payment_methods_model.dart';
import 'package:payment/src/assets/assets.gen.dart' as paymentAssets;
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/domain/data/withdrawal_mop.dart';
import 'package:payment/src/navigation/payment_navigation.dart';

class WithdrawSelectAccountScreen extends StatefulWidget {
  const WithdrawSelectAccountScreen({
    super.key,
    required this.method,
    required this.accounts,
    required this.popUntilRoute,
  });

  final WithdrawalPaymentMethod method;
  final List<String> accounts;
  final String popUntilRoute;

  @override
  State<WithdrawSelectAccountScreen> createState() =>
      _WithdrawSelectAccountScreenState();
}

class _WithdrawSelectAccountScreenState
    extends State<WithdrawSelectAccountScreen> {
  late String selectedAccount = widget.accounts.firstOrNull ?? '';

  Widget getLeadingIcon() {
    switch (widget.method.name) {
      case 'Skrill':
        return paymentAssets.Assets.images.skrill.svg();
      case 'Neteller':
        return paymentAssets.Assets.images.neteller.svg();
    }
    throw Exception('Unsupported payment method: ${widget.method.name}');
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final localization = EquitiLocalization.of(context);
    return Scaffold(
      backgroundColor: theme.background.bgPrimary,
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DuploText(
              text: localization.payments_paymentMethodAccounts(
                widget.method.name,
              ),
              style: textStyles.textXl,
              fontWeight: DuploFontWeight.semiBold,
              color: theme.text.textPrimary,
            ),
            SizedBox(height: 8),
            DuploText(
              text: localization.payments_selectAccountToWithdrawTo(
                widget.method.name,
              ),
              style: textStyles.textSm,
              color: theme.text.textSecondary,
            ),
            SizedBox(height: 24),
            for (int i = 0; i < widget.accounts.length; i++) ...[
              DuploTap(
                onTap: () {
                  setState(() {
                    selectedAccount = widget.accounts.elementAtOrNull(i) ?? '';
                  });
                },
                child: DuploSelectionContainer(
                  borderRadius: 6,
                  borderWidth:
                      selectedAccount == widget.accounts.elementAtOrNull(i)
                          ? 2
                          : 1,
                  selectedIconAlignment: CrossAxisAlignment.center,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  leadingGap: 8,
                  backgroundColor: theme.background.bgActive,
                  isSelected:
                      selectedAccount == widget.accounts.elementAtOrNull(i),
                  leading: SizedBox(height: 48, child: getLeadingIcon()),
                  title: DuploText(
                    text: widget.accounts.elementAtOrNull(i),
                    style: textStyles.textMd,
                    fontWeight: DuploFontWeight.semiBold,
                    color: theme.text.textSecondary,
                  ),
                ),
              ),
              if (i < widget.accounts.length - 1) const SizedBox(height: 12),
            ],
            Spacer(),
            DuploButton.defaultPrimary(
              title: localization.payments_continue,
              trailingIcon: Assets.images.chevronRight.keyName,

              onTap: () {
                widget.method.mop == WithdrawalMop.skrill
                    ? diContainer<WithdrawAnalyticsEvent>()
                        .withdrawSkrillAccountSelected(
                          accountId: selectedAccount,
                        )
                    : diContainer<WithdrawAnalyticsEvent>()
                        .withdrawNetellerAccountSelected(
                          accountId: selectedAccount,
                        );
                diContainer<PaymentNavigation>()
                    .goToWithdrawSelectAccountAndAmountScreen(
                      method: widget.method,
                      account: selectedAccount,
                      popUntilRoute: widget.popUntilRoute,
                    );
              },
              useFullWidth: true,
            ),
            SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}
