import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/payment_method/withdraw_payment_methods_model/withdraw_payment_methods_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/presentation/withdraw_skrill_and_neteller/bloc/withdraw_skrill_and_neteller_bloc.dart';
import 'package:payment/src/presentation/withdraw_skrill_and_neteller/withdraw_method_not_allowed_screen.dart';
import 'package:payment/src/presentation/withdraw_skrill_and_neteller/withdraw_select_account_screen.dart';

class WithdrawSkrillAndNetellerScreen extends StatelessWidget {
  const WithdrawSkrillAndNetellerScreen({
    super.key,
    required this.method,
    required this.popUntilRoute,
  });
  final String popUntilRoute;
  final WithdrawalPaymentMethod method;

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final localization = EquitiLocalization.of(context);
    return BlocProvider(
      create:
          (_) =>
              diContainer<WithdrawSkrillAndNetellerBloc>()
                ..add(CheckPaymentMethod(method.mop)),
      child: Scaffold(
        backgroundColor: theme.background.bgPrimary,
        appBar: DuploAppBar(
          title: '${localization.payments_withdraw_with} ${method.name}',
          leading: IconButton(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: const Icon(Icons.arrow_back_outlined),
          ),
        ),
        body: BlocBuilder<
          WithdrawSkrillAndNetellerBloc,
          WithdrawSkrillAndNetellerState
        >(
          buildWhen: (previous, current) {
            return current.withdrawSkrillAndNetellerProgressState !=
                previous.withdrawSkrillAndNetellerProgressState;
          },
          builder: (ctx, state) {
            return switch (state.withdrawSkrillAndNetellerProgressState) {
              WithdrawSkrillAndNetellerProgressStateLoading() => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: LoadingView(
                  title: localization.payments_verifyWithdrawalMethod,
                  subtitle: localization.payments_checkingPreviousDepositMethod,
                ),
              ),
              WithdrawSkrillAndNetellerProgressStateEmpty() ||
              WithdrawSkrillAndNetellerProgressStateError() =>
                WithdrawMethodNotAllowedScreen(
                  onChangeMethod: () {
                    Navigator.pop(context);
                  },
                ),

              WithdrawSkrillAndNetellerProgressStateSuccess(
                :final isWithdrawalAllowed,
              ) =>
                isWithdrawalAllowed
                    ? WithdrawSelectAccountScreen(
                      method: method,
                      popUntilRoute: popUntilRoute,
                      accounts:
                          state.withdrawalAllowedResponseModel!.data.accounts,
                    )
                    : WithdrawMethodNotAllowedScreen(
                      onChangeMethod: () {
                        Navigator.pop(context);
                      },
                    ),
            };
          },
        ),
      ),
    );
  }
}
