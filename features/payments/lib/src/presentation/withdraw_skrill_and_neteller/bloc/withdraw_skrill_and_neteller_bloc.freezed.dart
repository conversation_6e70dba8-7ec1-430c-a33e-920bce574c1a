// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'withdraw_skrill_and_neteller_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$WithdrawSkrillAndNetellerEvent {

 WithdrawalMop get paymentMethod;
/// Create a copy of WithdrawSkrillAndNetellerEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawSkrillAndNetellerEventCopyWith<WithdrawSkrillAndNetellerEvent> get copyWith => _$WithdrawSkrillAndNetellerEventCopyWithImpl<WithdrawSkrillAndNetellerEvent>(this as WithdrawSkrillAndNetellerEvent, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawSkrillAndNetellerEvent&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod));
}


@override
int get hashCode => Object.hash(runtimeType,paymentMethod);

@override
String toString() {
  return 'WithdrawSkrillAndNetellerEvent(paymentMethod: $paymentMethod)';
}


}

/// @nodoc
abstract mixin class $WithdrawSkrillAndNetellerEventCopyWith<$Res>  {
  factory $WithdrawSkrillAndNetellerEventCopyWith(WithdrawSkrillAndNetellerEvent value, $Res Function(WithdrawSkrillAndNetellerEvent) _then) = _$WithdrawSkrillAndNetellerEventCopyWithImpl;
@useResult
$Res call({
 WithdrawalMop paymentMethod
});




}
/// @nodoc
class _$WithdrawSkrillAndNetellerEventCopyWithImpl<$Res>
    implements $WithdrawSkrillAndNetellerEventCopyWith<$Res> {
  _$WithdrawSkrillAndNetellerEventCopyWithImpl(this._self, this._then);

  final WithdrawSkrillAndNetellerEvent _self;
  final $Res Function(WithdrawSkrillAndNetellerEvent) _then;

/// Create a copy of WithdrawSkrillAndNetellerEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? paymentMethod = null,}) {
  return _then(_self.copyWith(
paymentMethod: null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as WithdrawalMop,
  ));
}

}


/// @nodoc


class CheckPaymentMethod implements WithdrawSkrillAndNetellerEvent {
  const CheckPaymentMethod(this.paymentMethod);
  

@override final  WithdrawalMop paymentMethod;

/// Create a copy of WithdrawSkrillAndNetellerEvent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CheckPaymentMethodCopyWith<CheckPaymentMethod> get copyWith => _$CheckPaymentMethodCopyWithImpl<CheckPaymentMethod>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CheckPaymentMethod&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod));
}


@override
int get hashCode => Object.hash(runtimeType,paymentMethod);

@override
String toString() {
  return 'WithdrawSkrillAndNetellerEvent.checkPaymentMethod(paymentMethod: $paymentMethod)';
}


}

/// @nodoc
abstract mixin class $CheckPaymentMethodCopyWith<$Res> implements $WithdrawSkrillAndNetellerEventCopyWith<$Res> {
  factory $CheckPaymentMethodCopyWith(CheckPaymentMethod value, $Res Function(CheckPaymentMethod) _then) = _$CheckPaymentMethodCopyWithImpl;
@override @useResult
$Res call({
 WithdrawalMop paymentMethod
});




}
/// @nodoc
class _$CheckPaymentMethodCopyWithImpl<$Res>
    implements $CheckPaymentMethodCopyWith<$Res> {
  _$CheckPaymentMethodCopyWithImpl(this._self, this._then);

  final CheckPaymentMethod _self;
  final $Res Function(CheckPaymentMethod) _then;

/// Create a copy of WithdrawSkrillAndNetellerEvent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? paymentMethod = null,}) {
  return _then(CheckPaymentMethod(
null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as WithdrawalMop,
  ));
}


}

/// @nodoc
mixin _$WithdrawSkrillAndNetellerState {

 WithdrawalAllowedResponseModel? get withdrawalAllowedResponseModel; WithdrawSkrillAndNetellerProgressState get withdrawSkrillAndNetellerProgressState;
/// Create a copy of WithdrawSkrillAndNetellerState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawSkrillAndNetellerStateCopyWith<WithdrawSkrillAndNetellerState> get copyWith => _$WithdrawSkrillAndNetellerStateCopyWithImpl<WithdrawSkrillAndNetellerState>(this as WithdrawSkrillAndNetellerState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawSkrillAndNetellerState&&(identical(other.withdrawalAllowedResponseModel, withdrawalAllowedResponseModel) || other.withdrawalAllowedResponseModel == withdrawalAllowedResponseModel)&&(identical(other.withdrawSkrillAndNetellerProgressState, withdrawSkrillAndNetellerProgressState) || other.withdrawSkrillAndNetellerProgressState == withdrawSkrillAndNetellerProgressState));
}


@override
int get hashCode => Object.hash(runtimeType,withdrawalAllowedResponseModel,withdrawSkrillAndNetellerProgressState);

@override
String toString() {
  return 'WithdrawSkrillAndNetellerState(withdrawalAllowedResponseModel: $withdrawalAllowedResponseModel, withdrawSkrillAndNetellerProgressState: $withdrawSkrillAndNetellerProgressState)';
}


}

/// @nodoc
abstract mixin class $WithdrawSkrillAndNetellerStateCopyWith<$Res>  {
  factory $WithdrawSkrillAndNetellerStateCopyWith(WithdrawSkrillAndNetellerState value, $Res Function(WithdrawSkrillAndNetellerState) _then) = _$WithdrawSkrillAndNetellerStateCopyWithImpl;
@useResult
$Res call({
 WithdrawalAllowedResponseModel? withdrawalAllowedResponseModel, WithdrawSkrillAndNetellerProgressState withdrawSkrillAndNetellerProgressState
});


$WithdrawalAllowedResponseModelCopyWith<$Res>? get withdrawalAllowedResponseModel;$WithdrawSkrillAndNetellerProgressStateCopyWith<$Res> get withdrawSkrillAndNetellerProgressState;

}
/// @nodoc
class _$WithdrawSkrillAndNetellerStateCopyWithImpl<$Res>
    implements $WithdrawSkrillAndNetellerStateCopyWith<$Res> {
  _$WithdrawSkrillAndNetellerStateCopyWithImpl(this._self, this._then);

  final WithdrawSkrillAndNetellerState _self;
  final $Res Function(WithdrawSkrillAndNetellerState) _then;

/// Create a copy of WithdrawSkrillAndNetellerState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? withdrawalAllowedResponseModel = freezed,Object? withdrawSkrillAndNetellerProgressState = null,}) {
  return _then(_self.copyWith(
withdrawalAllowedResponseModel: freezed == withdrawalAllowedResponseModel ? _self.withdrawalAllowedResponseModel : withdrawalAllowedResponseModel // ignore: cast_nullable_to_non_nullable
as WithdrawalAllowedResponseModel?,withdrawSkrillAndNetellerProgressState: null == withdrawSkrillAndNetellerProgressState ? _self.withdrawSkrillAndNetellerProgressState : withdrawSkrillAndNetellerProgressState // ignore: cast_nullable_to_non_nullable
as WithdrawSkrillAndNetellerProgressState,
  ));
}
/// Create a copy of WithdrawSkrillAndNetellerState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawalAllowedResponseModelCopyWith<$Res>? get withdrawalAllowedResponseModel {
    if (_self.withdrawalAllowedResponseModel == null) {
    return null;
  }

  return $WithdrawalAllowedResponseModelCopyWith<$Res>(_self.withdrawalAllowedResponseModel!, (value) {
    return _then(_self.copyWith(withdrawalAllowedResponseModel: value));
  });
}/// Create a copy of WithdrawSkrillAndNetellerState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawSkrillAndNetellerProgressStateCopyWith<$Res> get withdrawSkrillAndNetellerProgressState {
  
  return $WithdrawSkrillAndNetellerProgressStateCopyWith<$Res>(_self.withdrawSkrillAndNetellerProgressState, (value) {
    return _then(_self.copyWith(withdrawSkrillAndNetellerProgressState: value));
  });
}
}


/// @nodoc


class _WithdrawSkrillAndNetellerState implements WithdrawSkrillAndNetellerState {
  const _WithdrawSkrillAndNetellerState({this.withdrawalAllowedResponseModel, this.withdrawSkrillAndNetellerProgressState = const WithdrawSkrillAndNetellerProgressState.loading()});
  

@override final  WithdrawalAllowedResponseModel? withdrawalAllowedResponseModel;
@override@JsonKey() final  WithdrawSkrillAndNetellerProgressState withdrawSkrillAndNetellerProgressState;

/// Create a copy of WithdrawSkrillAndNetellerState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$WithdrawSkrillAndNetellerStateCopyWith<_WithdrawSkrillAndNetellerState> get copyWith => __$WithdrawSkrillAndNetellerStateCopyWithImpl<_WithdrawSkrillAndNetellerState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _WithdrawSkrillAndNetellerState&&(identical(other.withdrawalAllowedResponseModel, withdrawalAllowedResponseModel) || other.withdrawalAllowedResponseModel == withdrawalAllowedResponseModel)&&(identical(other.withdrawSkrillAndNetellerProgressState, withdrawSkrillAndNetellerProgressState) || other.withdrawSkrillAndNetellerProgressState == withdrawSkrillAndNetellerProgressState));
}


@override
int get hashCode => Object.hash(runtimeType,withdrawalAllowedResponseModel,withdrawSkrillAndNetellerProgressState);

@override
String toString() {
  return 'WithdrawSkrillAndNetellerState(withdrawalAllowedResponseModel: $withdrawalAllowedResponseModel, withdrawSkrillAndNetellerProgressState: $withdrawSkrillAndNetellerProgressState)';
}


}

/// @nodoc
abstract mixin class _$WithdrawSkrillAndNetellerStateCopyWith<$Res> implements $WithdrawSkrillAndNetellerStateCopyWith<$Res> {
  factory _$WithdrawSkrillAndNetellerStateCopyWith(_WithdrawSkrillAndNetellerState value, $Res Function(_WithdrawSkrillAndNetellerState) _then) = __$WithdrawSkrillAndNetellerStateCopyWithImpl;
@override @useResult
$Res call({
 WithdrawalAllowedResponseModel? withdrawalAllowedResponseModel, WithdrawSkrillAndNetellerProgressState withdrawSkrillAndNetellerProgressState
});


@override $WithdrawalAllowedResponseModelCopyWith<$Res>? get withdrawalAllowedResponseModel;@override $WithdrawSkrillAndNetellerProgressStateCopyWith<$Res> get withdrawSkrillAndNetellerProgressState;

}
/// @nodoc
class __$WithdrawSkrillAndNetellerStateCopyWithImpl<$Res>
    implements _$WithdrawSkrillAndNetellerStateCopyWith<$Res> {
  __$WithdrawSkrillAndNetellerStateCopyWithImpl(this._self, this._then);

  final _WithdrawSkrillAndNetellerState _self;
  final $Res Function(_WithdrawSkrillAndNetellerState) _then;

/// Create a copy of WithdrawSkrillAndNetellerState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? withdrawalAllowedResponseModel = freezed,Object? withdrawSkrillAndNetellerProgressState = null,}) {
  return _then(_WithdrawSkrillAndNetellerState(
withdrawalAllowedResponseModel: freezed == withdrawalAllowedResponseModel ? _self.withdrawalAllowedResponseModel : withdrawalAllowedResponseModel // ignore: cast_nullable_to_non_nullable
as WithdrawalAllowedResponseModel?,withdrawSkrillAndNetellerProgressState: null == withdrawSkrillAndNetellerProgressState ? _self.withdrawSkrillAndNetellerProgressState : withdrawSkrillAndNetellerProgressState // ignore: cast_nullable_to_non_nullable
as WithdrawSkrillAndNetellerProgressState,
  ));
}

/// Create a copy of WithdrawSkrillAndNetellerState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawalAllowedResponseModelCopyWith<$Res>? get withdrawalAllowedResponseModel {
    if (_self.withdrawalAllowedResponseModel == null) {
    return null;
  }

  return $WithdrawalAllowedResponseModelCopyWith<$Res>(_self.withdrawalAllowedResponseModel!, (value) {
    return _then(_self.copyWith(withdrawalAllowedResponseModel: value));
  });
}/// Create a copy of WithdrawSkrillAndNetellerState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$WithdrawSkrillAndNetellerProgressStateCopyWith<$Res> get withdrawSkrillAndNetellerProgressState {
  
  return $WithdrawSkrillAndNetellerProgressStateCopyWith<$Res>(_self.withdrawSkrillAndNetellerProgressState, (value) {
    return _then(_self.copyWith(withdrawSkrillAndNetellerProgressState: value));
  });
}
}

/// @nodoc
mixin _$WithdrawSkrillAndNetellerProgressState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawSkrillAndNetellerProgressState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawSkrillAndNetellerProgressState()';
}


}

/// @nodoc
class $WithdrawSkrillAndNetellerProgressStateCopyWith<$Res>  {
$WithdrawSkrillAndNetellerProgressStateCopyWith(WithdrawSkrillAndNetellerProgressState _, $Res Function(WithdrawSkrillAndNetellerProgressState) __);
}


/// @nodoc


class WithdrawSkrillAndNetellerProgressStateLoading implements WithdrawSkrillAndNetellerProgressState {
  const WithdrawSkrillAndNetellerProgressStateLoading();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawSkrillAndNetellerProgressStateLoading);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawSkrillAndNetellerProgressState.loading()';
}


}




/// @nodoc


class WithdrawSkrillAndNetellerProgressStateSuccess implements WithdrawSkrillAndNetellerProgressState {
  const WithdrawSkrillAndNetellerProgressStateSuccess({required this.isWithdrawalAllowed});
  

 final  bool isWithdrawalAllowed;

/// Create a copy of WithdrawSkrillAndNetellerProgressState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawSkrillAndNetellerProgressStateSuccessCopyWith<WithdrawSkrillAndNetellerProgressStateSuccess> get copyWith => _$WithdrawSkrillAndNetellerProgressStateSuccessCopyWithImpl<WithdrawSkrillAndNetellerProgressStateSuccess>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawSkrillAndNetellerProgressStateSuccess&&(identical(other.isWithdrawalAllowed, isWithdrawalAllowed) || other.isWithdrawalAllowed == isWithdrawalAllowed));
}


@override
int get hashCode => Object.hash(runtimeType,isWithdrawalAllowed);

@override
String toString() {
  return 'WithdrawSkrillAndNetellerProgressState.success(isWithdrawalAllowed: $isWithdrawalAllowed)';
}


}

/// @nodoc
abstract mixin class $WithdrawSkrillAndNetellerProgressStateSuccessCopyWith<$Res> implements $WithdrawSkrillAndNetellerProgressStateCopyWith<$Res> {
  factory $WithdrawSkrillAndNetellerProgressStateSuccessCopyWith(WithdrawSkrillAndNetellerProgressStateSuccess value, $Res Function(WithdrawSkrillAndNetellerProgressStateSuccess) _then) = _$WithdrawSkrillAndNetellerProgressStateSuccessCopyWithImpl;
@useResult
$Res call({
 bool isWithdrawalAllowed
});




}
/// @nodoc
class _$WithdrawSkrillAndNetellerProgressStateSuccessCopyWithImpl<$Res>
    implements $WithdrawSkrillAndNetellerProgressStateSuccessCopyWith<$Res> {
  _$WithdrawSkrillAndNetellerProgressStateSuccessCopyWithImpl(this._self, this._then);

  final WithdrawSkrillAndNetellerProgressStateSuccess _self;
  final $Res Function(WithdrawSkrillAndNetellerProgressStateSuccess) _then;

/// Create a copy of WithdrawSkrillAndNetellerProgressState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? isWithdrawalAllowed = null,}) {
  return _then(WithdrawSkrillAndNetellerProgressStateSuccess(
isWithdrawalAllowed: null == isWithdrawalAllowed ? _self.isWithdrawalAllowed : isWithdrawalAllowed // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc


class WithdrawSkrillAndNetellerProgressStateEmpty implements WithdrawSkrillAndNetellerProgressState {
  const WithdrawSkrillAndNetellerProgressStateEmpty();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawSkrillAndNetellerProgressStateEmpty);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'WithdrawSkrillAndNetellerProgressState.empty()';
}


}




/// @nodoc


class WithdrawSkrillAndNetellerProgressStateError implements WithdrawSkrillAndNetellerProgressState {
  const WithdrawSkrillAndNetellerProgressStateError({this.errorMessage});
  

 final  String? errorMessage;

/// Create a copy of WithdrawSkrillAndNetellerProgressState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$WithdrawSkrillAndNetellerProgressStateErrorCopyWith<WithdrawSkrillAndNetellerProgressStateError> get copyWith => _$WithdrawSkrillAndNetellerProgressStateErrorCopyWithImpl<WithdrawSkrillAndNetellerProgressStateError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is WithdrawSkrillAndNetellerProgressStateError&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}


@override
int get hashCode => Object.hash(runtimeType,errorMessage);

@override
String toString() {
  return 'WithdrawSkrillAndNetellerProgressState.error(errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class $WithdrawSkrillAndNetellerProgressStateErrorCopyWith<$Res> implements $WithdrawSkrillAndNetellerProgressStateCopyWith<$Res> {
  factory $WithdrawSkrillAndNetellerProgressStateErrorCopyWith(WithdrawSkrillAndNetellerProgressStateError value, $Res Function(WithdrawSkrillAndNetellerProgressStateError) _then) = _$WithdrawSkrillAndNetellerProgressStateErrorCopyWithImpl;
@useResult
$Res call({
 String? errorMessage
});




}
/// @nodoc
class _$WithdrawSkrillAndNetellerProgressStateErrorCopyWithImpl<$Res>
    implements $WithdrawSkrillAndNetellerProgressStateErrorCopyWith<$Res> {
  _$WithdrawSkrillAndNetellerProgressStateErrorCopyWithImpl(this._self, this._then);

  final WithdrawSkrillAndNetellerProgressStateError _self;
  final $Res Function(WithdrawSkrillAndNetellerProgressStateError) _then;

/// Create a copy of WithdrawSkrillAndNetellerProgressState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? errorMessage = freezed,}) {
  return _then(WithdrawSkrillAndNetellerProgressStateError(
errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
