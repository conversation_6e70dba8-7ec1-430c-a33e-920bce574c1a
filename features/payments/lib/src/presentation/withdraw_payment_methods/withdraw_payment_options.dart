import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';
import 'package:payment/src/data/payment_method/withdraw_payment_methods_model/withdraw_payment_methods_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/presentation/widgets/payment_methods_shimmer.dart';

import 'bloc/withdraw_payment_methods_bloc.dart';
import 'widgets/payment_method_tile.dart';

class WithdrawPaymentOptions extends StatelessWidget {
  const WithdrawPaymentOptions({super.key, required this.popUntilRoute});
  final String popUntilRoute;

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    final theme = DuploTheme.of(context);
    final textStyle = context.duploTextStyles;
    return BlocProvider(
      create: (providerContext) {
        diContainer<WithdrawAnalyticsEvent>().withdrawStart();
        return diContainer<WithdrawPaymentMethodsBloc>()
          ..add(const WithdrawPaymentMethodsEvent.getPaymentOptions());
      },
      child: BlocBuilder<
        WithdrawPaymentMethodsBloc,
        WithdrawPaymentMethodsState
      >(
        buildWhen:
            (previous, current) =>
                previous.paymentMethodsProccessState !=
                current.paymentMethodsProccessState,
        builder: (builderContext, state) {
          final paymentMethodsGroup =
              state.paymentMethodsData?.data.paymentsMethods;
          return Scaffold(
            appBar: DuploAppBar(title: localization.payments_withdraw),
            backgroundColor: theme.background.bgPrimary,
            body: switch (state.paymentMethodsProccessState) {
              PaymentMethodsProccessStateLoading() => PaymentMethodsShimmer(),
              PaymentMethodsProccessStateSuccess() => Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 16.0,
                    ),
                    child: DuploAlertMessage.warning(
                      title: localization.payments_withdraw_policy_first_text,
                      children: [
                        DuploText(
                          text:
                              localization.payments_withdraw_policy_second_text,
                          style: textStyle.textSm,
                          color: theme.text.textTertiary,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: paymentMethodsGroup?.length ?? 0,
                      itemBuilder: (itemBuilderContext, mainIndex) {
                        final paymentMethods =
                            paymentMethodsGroup
                                ?.elementAtOrNull(mainIndex)
                                ?.methods;

                        final description =
                            paymentMethodsGroup
                                ?.elementAtOrNull(mainIndex)
                                ?.description;

                        return Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 10,
                            horizontal: 15,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  SizedBox(width: 15),
                                  DuploText(
                                    text:
                                        paymentMethodsGroup
                                            ?.elementAtOrNull(mainIndex)
                                            ?.label,
                                    color: theme.text.textPrimary,
                                    style: DuploTextStyles.of(context).textMd,
                                    fontWeight: DuploFontWeight.semiBold,
                                  ),
                                ],
                              ),
                              if (description != null) ...[
                                SizedBox(height: 8),
                                Padding(
                                  padding: const EdgeInsets.only(left: 15.0),
                                  child: DuploText(
                                    text:
                                        paymentMethodsGroup
                                            ?.elementAtOrNull(mainIndex)
                                            ?.description ??
                                        "",
                                    color: theme.text.textTertiary,
                                    style: DuploTextStyles.of(context).textXs,
                                  ),
                                ),
                              ],

                              for (WithdrawalPaymentMethod paymentMethod
                                  in paymentMethods ?? [])
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 8,
                                  ),
                                  child: DuploTap(
                                    radius: DuploRadius.radius_md_8,
                                    onTap: () {
                                      diContainer<WithdrawAnalyticsEvent>()
                                          .withdrawMopSelected(
                                            mop: paymentMethod.mop.name,
                                          );
                                      builderContext
                                          .read<WithdrawPaymentMethodsBloc>()
                                          .add(
                                            WithdrawPaymentMethodsEvent.navigateToSelectAccountAndAmount(
                                              paymentMethod: paymentMethod,
                                              popUntilRoute: popUntilRoute,
                                            ),
                                          );
                                    },
                                    child: PaymentMethodTile(
                                      currencies:
                                          paymentMethod.currencies ?? [],
                                      imageUrl: paymentMethod.imageUrl ?? "",
                                      tags: paymentMethod.tag ?? [],
                                      name: paymentMethod.name,
                                      fee: paymentMethod.fee,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
              PaymentMethodsProccessStateError() => const Center(
                child: Text('Error'),
              ),
            },
          );
        },
      ),
    );
  }
}
