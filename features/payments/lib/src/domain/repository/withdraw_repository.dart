import 'package:api_client/api_client.dart';
import 'package:dio/dio.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';
import 'package:payment/src/data/bank_accounts_model.dart';
import 'package:payment/src/data/upload_document_request_model/upload_document_request_model.dart';
import 'package:payment/src/data/upload_document_response_model/upload_document_response_model.dart';
import 'package:payment/src/data/withdraw_card_model/withdraw_card_model.dart';
import 'package:payment/src/data/withdraw_request_model/withdraw_request_model.dart';
import 'package:payment/src/data/withdraw_response_model/withdraw_response_model.dart';
import 'package:payment/src/data/withdraw_transfer_type_response_model/withdraw_transfer_type_response_model.dart';
import 'package:payment/src/data/withdrawal_allowed_response_model/withdrawal_allowed_response_model.dart';
import 'package:payment/src/domain/exceptions/withdraw_card_exception/withdraw_card_exception.dart';
import 'package:prelude/prelude.dart';

class WithdrawRepository {
  final ApiClientBase apiClient;
  final AuthService Function() authService;
  final WithdrawAnalyticsEvent analyticsEvent;

  const WithdrawRepository({
    required this.apiClient,
    required this.authService,
    required this.analyticsEvent,
  });
  TaskEither<Exception, BankAccountsModel> fetchBankAccounts(
    String tradingAccountId,
  ) {
    return apiClient
        .get<Map<String, dynamic>>(
          "api/v1/client/bankAccounts",
          queryParams: {"tradingAccountId": tradingAccountId},
        )
        .flatMap((response) {
          return TaskEither.tryCatch(
            () async {
              return BankAccountsModel.fromJson(response.data!);
            },
            (error, stackTrace) {
              return Exception(error);
            },
          );
        });
  }

  TaskEither<Exception, void> deleteBankAccount(String bankAccountId) {
    return apiClient
        .delete<Map<String, dynamic>>(
          "api/v1/client/bankAccount/$bankAccountId",
        )
        .flatMap((response) {
          return TaskEither.tryCatch(
            () async {
              print("Successfully deleted bank account");
            },
            (error, stackTrace) {
              return Exception(error);
            },
          );
        });
  }

  TaskEither<Exception, WithdrawCardModel> getWithdrawCard() {
    return apiClient
        .get<Map<String, dynamic>>('/api/v1/client/cards')
        .mapLeft((error) {
          if (error is ClientException) {
            final dioError = error.cause as DioException;
            final responseData =
                dioError.response?.data as Map<String, dynamic>;
            return WithdrawCardException.fromJson(responseData);
          }
          return error;
        })
        .flatMap((response) {
          return TaskEither.tryCatch(
            () async => WithdrawCardModel.fromJson(response.data ?? {}),
            (error, stackTrace) => Exception(error),
          );
        });
  }

  TaskEither<Exception, WithdrawResponseModel> withdraw({
    required WithdrawRequestModel request,
    required String correlationId,
    required String language,
    required String email,
  }) {
    return TaskEither.Do(($) async {
      analyticsEvent.withdrawMFAInitiated();
      // Authenticate first and extract the token
      final authResult = await $(
        authService().login(email: email, authFlow: AuthFlow.withdraw),
      );
      analyticsEvent.withdrawMFACompleted();
      // Make the withdrawal API call with the authentication token
      final response = await $(
        apiClient.post<Map<String, dynamic>>(
          'api/v1/payment/withdrawal',
          data: request.toJson(),
          headers: {
            "X-Correlation-Id": correlationId,
            "language": language,
            "Authorization": "Bearer ${authResult.accessToken}",
          },
        ),
      );

      // Parse the response
      return WithdrawResponseModel.fromJson(response.data ?? {});
    });
  }

  TaskEither<Exception, WithdrawalAllowedResponseModel> checkWithdrawalAllowed({
    required String paymentMethod,
  }) {
    return apiClient
        .get<Map<String, dynamic>>(
          "/api/v1/withdrawal/account",
          queryParams: {"paymentType": paymentMethod},
        )
        .flatMap((response) {
          return TaskEither.tryCatch(
            () async {
              final data = WithdrawalAllowedResponseModel.fromJson(
                response.data!,
              );
              return data;
            },
            (error, stackTrace) {
              return Exception(error);
            },
          );
        });
  }

  TaskEither<Exception, WithdrawTransferTypeResponseModel> getTransferType({
    required String brokerId,
    required String countryCodeInThreeCharacter,
  }) {
    return apiClient
        .get<Map<String, dynamic>>(
          "api/v1/country/$countryCodeInThreeCharacter/info",
          queryParams: {"brokerId": brokerId},
        )
        .flatMap((response) {
          return TaskEither.tryCatch(
            () async {
              final data = WithdrawTransferTypeResponseModel.fromJson(
                response.data!,
              );
              return data;
            },
            (error, stackTrace) {
              return Exception(error);
            },
          );
        });
  }

  TaskEither<Exception, UploadDocumentResponseModel> uploadDocument({
    required UploadDocumentRequestModel request,
    required List<String> filesPath,
  }) {
    return apiClient
        .postFile<Map<String, dynamic>>(
          '/api/v1/client/document',
          data: request.toJson(),
          filesPath: filesPath,
        )
        .flatMap((response) {
          return TaskEither.tryCatch(
            () async =>
                UploadDocumentResponseModel.fromJson(response.data ?? {}),
            (error, stackTrace) => Exception(error),
          );
        });
  }
}
