import 'package:api_client/api_client.dart';

import 'package:payment/src/data/transfer_funds/transfer_funds_request_model.dart';
import 'package:payment/src/data/transfer_funds/transfer_funds_response.dart';
import 'package:payment/src/domain/exceptions/transfer_exception/transfer_exception.dart';
import 'package:prelude/prelude.dart';

class TransferRepository {
  final ApiClientBase apiClient;

  const TransferRepository(this.apiClient);

  TaskEither<Exception, TransferFundsResponse> submitTransferFunds(
    TransferFundsRequestModel _transferFundsRequestModel,
    String correlationId,
  ) {
    return apiClient
        .post<Map<String, dynamic>>(
          "api/v1/payment/transfer",
          data: _transferFundsRequestModel.toJson(),
          headers: {"X-Correlation-Id": correlationId},
        )
        .mapLeft((error) {
          if (error is ClientException) {
            return TransferException.fromMobileBffBaseError(
              error.mobileBffBaseError,
            );
          }
          return error;
        })
        .flatMap((response) {
          return TaskEither.tryCatch(
            () async {
              final transferResponse = TransferFundsResponse.fromJson(
                response.data ?? {},
              );
              if (!transferResponse.success) {
                throw TransferException.unknown(
                  code: 0,
                  message:
                      "Failed to submit transfer funds ${transferResponse}",
                );
              }
              return transferResponse;
            },
            (error, stackTrace) {
              if (error is TransferException) {
                return error;
              }
              return Exception(error);
            },
          );
        });
  }
}
