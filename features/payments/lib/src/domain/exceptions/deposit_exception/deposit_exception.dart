import 'package:api_client/api_client.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'deposit_exception.freezed.dart';

@freezed
sealed class DepositException with _$DepositException implements Exception {
  const factory DepositException.unknown({
    required int code,
    required String message,
  }) = DepositUnknownError;

  const factory DepositException.lowerThanMinimumDepositAmount({
    required int code,
    required String message,
  }) = DepositLowerThanMinimumDepositAmountError;

  const factory DepositException.currencyConversionRateInvalid({
    required int code,
    required String message,
  }) = DepositCurrencyConversionRateInvalidError;

  factory DepositException.fromMobileBffBaseError(
    MobileBffBaseError? mobileBffBaseError,
  ) {
    if (mobileBffBaseError != null) {
      final errorCode = mobileBffBaseError.errorCode;
      final errorMessage = mobileBffBaseError.description;

      switch (errorCode) {
        case 2001:
          return DepositException.lowerThanMinimumDepositAmount(
            code: errorCode,
            message: errorMessage,
          );
        case 2002:
          return DepositException.currencyConversionRateInvalid(
            code: errorCode,
            message: errorMessage,
          );
        default:
          return DepositException.unknown(
            code: errorCode,
            message: errorMessage,
          );
      }
    } else {
      return DepositException.unknown(code: 0, message: 'Unknown error');
    }
  }
}
