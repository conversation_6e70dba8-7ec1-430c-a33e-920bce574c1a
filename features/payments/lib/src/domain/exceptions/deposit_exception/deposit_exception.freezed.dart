// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deposit_exception.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$DepositException {

 int get code; String get message;
/// Create a copy of DepositException
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositExceptionCopyWith<DepositException> get copyWith => _$DepositExceptionCopyWithImpl<DepositException>(this as DepositException, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositException&&(identical(other.code, code) || other.code == code)&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,code,message);

@override
String toString() {
  return 'DepositException(code: $code, message: $message)';
}


}

/// @nodoc
abstract mixin class $DepositExceptionCopyWith<$Res>  {
  factory $DepositExceptionCopyWith(DepositException value, $Res Function(DepositException) _then) = _$DepositExceptionCopyWithImpl;
@useResult
$Res call({
 int code, String message
});




}
/// @nodoc
class _$DepositExceptionCopyWithImpl<$Res>
    implements $DepositExceptionCopyWith<$Res> {
  _$DepositExceptionCopyWithImpl(this._self, this._then);

  final DepositException _self;
  final $Res Function(DepositException) _then;

/// Create a copy of DepositException
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? code = null,Object? message = null,}) {
  return _then(_self.copyWith(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc


class DepositUnknownError implements DepositException {
  const DepositUnknownError({required this.code, required this.message});
  

@override final  int code;
@override final  String message;

/// Create a copy of DepositException
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositUnknownErrorCopyWith<DepositUnknownError> get copyWith => _$DepositUnknownErrorCopyWithImpl<DepositUnknownError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositUnknownError&&(identical(other.code, code) || other.code == code)&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,code,message);

@override
String toString() {
  return 'DepositException.unknown(code: $code, message: $message)';
}


}

/// @nodoc
abstract mixin class $DepositUnknownErrorCopyWith<$Res> implements $DepositExceptionCopyWith<$Res> {
  factory $DepositUnknownErrorCopyWith(DepositUnknownError value, $Res Function(DepositUnknownError) _then) = _$DepositUnknownErrorCopyWithImpl;
@override @useResult
$Res call({
 int code, String message
});




}
/// @nodoc
class _$DepositUnknownErrorCopyWithImpl<$Res>
    implements $DepositUnknownErrorCopyWith<$Res> {
  _$DepositUnknownErrorCopyWithImpl(this._self, this._then);

  final DepositUnknownError _self;
  final $Res Function(DepositUnknownError) _then;

/// Create a copy of DepositException
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? code = null,Object? message = null,}) {
  return _then(DepositUnknownError(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class DepositLowerThanMinimumDepositAmountError implements DepositException {
  const DepositLowerThanMinimumDepositAmountError({required this.code, required this.message});
  

@override final  int code;
@override final  String message;

/// Create a copy of DepositException
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositLowerThanMinimumDepositAmountErrorCopyWith<DepositLowerThanMinimumDepositAmountError> get copyWith => _$DepositLowerThanMinimumDepositAmountErrorCopyWithImpl<DepositLowerThanMinimumDepositAmountError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositLowerThanMinimumDepositAmountError&&(identical(other.code, code) || other.code == code)&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,code,message);

@override
String toString() {
  return 'DepositException.lowerThanMinimumDepositAmount(code: $code, message: $message)';
}


}

/// @nodoc
abstract mixin class $DepositLowerThanMinimumDepositAmountErrorCopyWith<$Res> implements $DepositExceptionCopyWith<$Res> {
  factory $DepositLowerThanMinimumDepositAmountErrorCopyWith(DepositLowerThanMinimumDepositAmountError value, $Res Function(DepositLowerThanMinimumDepositAmountError) _then) = _$DepositLowerThanMinimumDepositAmountErrorCopyWithImpl;
@override @useResult
$Res call({
 int code, String message
});




}
/// @nodoc
class _$DepositLowerThanMinimumDepositAmountErrorCopyWithImpl<$Res>
    implements $DepositLowerThanMinimumDepositAmountErrorCopyWith<$Res> {
  _$DepositLowerThanMinimumDepositAmountErrorCopyWithImpl(this._self, this._then);

  final DepositLowerThanMinimumDepositAmountError _self;
  final $Res Function(DepositLowerThanMinimumDepositAmountError) _then;

/// Create a copy of DepositException
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? code = null,Object? message = null,}) {
  return _then(DepositLowerThanMinimumDepositAmountError(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class DepositCurrencyConversionRateInvalidError implements DepositException {
  const DepositCurrencyConversionRateInvalidError({required this.code, required this.message});
  

@override final  int code;
@override final  String message;

/// Create a copy of DepositException
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositCurrencyConversionRateInvalidErrorCopyWith<DepositCurrencyConversionRateInvalidError> get copyWith => _$DepositCurrencyConversionRateInvalidErrorCopyWithImpl<DepositCurrencyConversionRateInvalidError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositCurrencyConversionRateInvalidError&&(identical(other.code, code) || other.code == code)&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,code,message);

@override
String toString() {
  return 'DepositException.currencyConversionRateInvalid(code: $code, message: $message)';
}


}

/// @nodoc
abstract mixin class $DepositCurrencyConversionRateInvalidErrorCopyWith<$Res> implements $DepositExceptionCopyWith<$Res> {
  factory $DepositCurrencyConversionRateInvalidErrorCopyWith(DepositCurrencyConversionRateInvalidError value, $Res Function(DepositCurrencyConversionRateInvalidError) _then) = _$DepositCurrencyConversionRateInvalidErrorCopyWithImpl;
@override @useResult
$Res call({
 int code, String message
});




}
/// @nodoc
class _$DepositCurrencyConversionRateInvalidErrorCopyWithImpl<$Res>
    implements $DepositCurrencyConversionRateInvalidErrorCopyWith<$Res> {
  _$DepositCurrencyConversionRateInvalidErrorCopyWithImpl(this._self, this._then);

  final DepositCurrencyConversionRateInvalidError _self;
  final $Res Function(DepositCurrencyConversionRateInvalidError) _then;

/// Create a copy of DepositException
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? code = null,Object? message = null,}) {
  return _then(DepositCurrencyConversionRateInvalidError(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
