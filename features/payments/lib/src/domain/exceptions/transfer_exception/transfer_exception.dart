import 'package:api_client/api_client.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'transfer_exception.freezed.dart';

@freezed
sealed class TransferException with _$TransferException implements Exception {
  const factory TransferException.unknown({
    required int code,
    required String message,
  }) = TransferUnknownError;

  const factory TransferException.dontHaveEnoughMargin({
    required int code,
    required String message,
  }) = TransferDontHaveEnoughMarginError;

  const factory TransferException.transferAmountLowerThanMinimumSetting({
    required int code,
    required String message,
  }) = TransferAmountLowerThanMinimumSettingError;

  factory TransferException.fromMobileBffBaseError(
    MobileBffBaseError? mobileBffBaseError,
  ) {
    if (mobileBffBaseError != null) {
      final errorCode = mobileBffBaseError.errorCode;
      final errorMessage = mobileBffBaseError.description;

      switch (errorCode) {
        case 2101:
          return TransferException.dontHaveEnoughMargin(
            code: errorCode,
            message: errorMessage,
          );
        case 2102:
          return TransferException.transferAmountLowerThanMinimumSetting(
            code: errorCode,
            message: errorMessage,
          );
        default:
          return TransferException.unknown(
            code: errorCode,
            message: errorMessage,
          );
      }
    } else {
      return TransferException.unknown(code: 0, message: 'Unknown error');
    }
  }
}
