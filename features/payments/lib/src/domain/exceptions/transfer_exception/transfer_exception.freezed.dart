// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_exception.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$TransferException {

 int get code; String get message;
/// Create a copy of TransferException
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransferExceptionCopyWith<TransferException> get copyWith => _$TransferExceptionCopyWithImpl<TransferException>(this as TransferException, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferException&&(identical(other.code, code) || other.code == code)&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,code,message);

@override
String toString() {
  return 'TransferException(code: $code, message: $message)';
}


}

/// @nodoc
abstract mixin class $TransferExceptionCopyWith<$Res>  {
  factory $TransferExceptionCopyWith(TransferException value, $Res Function(TransferException) _then) = _$TransferExceptionCopyWithImpl;
@useResult
$Res call({
 int code, String message
});




}
/// @nodoc
class _$TransferExceptionCopyWithImpl<$Res>
    implements $TransferExceptionCopyWith<$Res> {
  _$TransferExceptionCopyWithImpl(this._self, this._then);

  final TransferException _self;
  final $Res Function(TransferException) _then;

/// Create a copy of TransferException
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? code = null,Object? message = null,}) {
  return _then(_self.copyWith(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc


class TransferUnknownError implements TransferException {
  const TransferUnknownError({required this.code, required this.message});
  

@override final  int code;
@override final  String message;

/// Create a copy of TransferException
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransferUnknownErrorCopyWith<TransferUnknownError> get copyWith => _$TransferUnknownErrorCopyWithImpl<TransferUnknownError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferUnknownError&&(identical(other.code, code) || other.code == code)&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,code,message);

@override
String toString() {
  return 'TransferException.unknown(code: $code, message: $message)';
}


}

/// @nodoc
abstract mixin class $TransferUnknownErrorCopyWith<$Res> implements $TransferExceptionCopyWith<$Res> {
  factory $TransferUnknownErrorCopyWith(TransferUnknownError value, $Res Function(TransferUnknownError) _then) = _$TransferUnknownErrorCopyWithImpl;
@override @useResult
$Res call({
 int code, String message
});




}
/// @nodoc
class _$TransferUnknownErrorCopyWithImpl<$Res>
    implements $TransferUnknownErrorCopyWith<$Res> {
  _$TransferUnknownErrorCopyWithImpl(this._self, this._then);

  final TransferUnknownError _self;
  final $Res Function(TransferUnknownError) _then;

/// Create a copy of TransferException
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? code = null,Object? message = null,}) {
  return _then(TransferUnknownError(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class TransferDontHaveEnoughMarginError implements TransferException {
  const TransferDontHaveEnoughMarginError({required this.code, required this.message});
  

@override final  int code;
@override final  String message;

/// Create a copy of TransferException
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransferDontHaveEnoughMarginErrorCopyWith<TransferDontHaveEnoughMarginError> get copyWith => _$TransferDontHaveEnoughMarginErrorCopyWithImpl<TransferDontHaveEnoughMarginError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferDontHaveEnoughMarginError&&(identical(other.code, code) || other.code == code)&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,code,message);

@override
String toString() {
  return 'TransferException.dontHaveEnoughMargin(code: $code, message: $message)';
}


}

/// @nodoc
abstract mixin class $TransferDontHaveEnoughMarginErrorCopyWith<$Res> implements $TransferExceptionCopyWith<$Res> {
  factory $TransferDontHaveEnoughMarginErrorCopyWith(TransferDontHaveEnoughMarginError value, $Res Function(TransferDontHaveEnoughMarginError) _then) = _$TransferDontHaveEnoughMarginErrorCopyWithImpl;
@override @useResult
$Res call({
 int code, String message
});




}
/// @nodoc
class _$TransferDontHaveEnoughMarginErrorCopyWithImpl<$Res>
    implements $TransferDontHaveEnoughMarginErrorCopyWith<$Res> {
  _$TransferDontHaveEnoughMarginErrorCopyWithImpl(this._self, this._then);

  final TransferDontHaveEnoughMarginError _self;
  final $Res Function(TransferDontHaveEnoughMarginError) _then;

/// Create a copy of TransferException
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? code = null,Object? message = null,}) {
  return _then(TransferDontHaveEnoughMarginError(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class TransferAmountLowerThanMinimumSettingError implements TransferException {
  const TransferAmountLowerThanMinimumSettingError({required this.code, required this.message});
  

@override final  int code;
@override final  String message;

/// Create a copy of TransferException
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransferAmountLowerThanMinimumSettingErrorCopyWith<TransferAmountLowerThanMinimumSettingError> get copyWith => _$TransferAmountLowerThanMinimumSettingErrorCopyWithImpl<TransferAmountLowerThanMinimumSettingError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransferAmountLowerThanMinimumSettingError&&(identical(other.code, code) || other.code == code)&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,code,message);

@override
String toString() {
  return 'TransferException.transferAmountLowerThanMinimumSetting(code: $code, message: $message)';
}


}

/// @nodoc
abstract mixin class $TransferAmountLowerThanMinimumSettingErrorCopyWith<$Res> implements $TransferExceptionCopyWith<$Res> {
  factory $TransferAmountLowerThanMinimumSettingErrorCopyWith(TransferAmountLowerThanMinimumSettingError value, $Res Function(TransferAmountLowerThanMinimumSettingError) _then) = _$TransferAmountLowerThanMinimumSettingErrorCopyWithImpl;
@override @useResult
$Res call({
 int code, String message
});




}
/// @nodoc
class _$TransferAmountLowerThanMinimumSettingErrorCopyWithImpl<$Res>
    implements $TransferAmountLowerThanMinimumSettingErrorCopyWith<$Res> {
  _$TransferAmountLowerThanMinimumSettingErrorCopyWithImpl(this._self, this._then);

  final TransferAmountLowerThanMinimumSettingError _self;
  final $Res Function(TransferAmountLowerThanMinimumSettingError) _then;

/// Create a copy of TransferException
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? code = null,Object? message = null,}) {
  return _then(TransferAmountLowerThanMinimumSettingError(
code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
