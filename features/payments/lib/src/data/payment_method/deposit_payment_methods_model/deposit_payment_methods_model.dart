import 'package:domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';
import 'package:payment/src/domain/data/deposit_mop.dart';
import 'package:prelude/prelude.dart';

part 'deposit_payment_methods_model.freezed.dart';
part 'deposit_payment_methods_model.g.dart';

@freezed
sealed class DepositPaymentMethodsModel with _$DepositPaymentMethodsModel {
  const factory DepositPaymentMethodsModel({
    required bool success,
    required DepositPaymentMethodsData data,
  }) = _DepositPaymentMethodsModel;

  factory DepositPaymentMethodsModel.fromJson(Map<String, dynamic> json) =>
      _$DepositPaymentMethodsModelFromJson(json);
}

@freezed
sealed class DepositPaymentMethodsData with _$DepositPaymentMethodsData {
  const factory DepositPaymentMethodsData({
    required List<DepositPaymentMethodGroup> paymentsMethods,
    required List<String> noCentsCurrencies,
    num? maxPollingTime,
    num? pollingFrequency,
  }) = _DepositPaymentMethodsData;

  factory DepositPaymentMethodsData.fromJson(Map<String, dynamic> json) =>
      _$DepositPaymentMethodsDataFromJson(json);
}

@freezed
sealed class DepositPaymentMethodGroup with _$DepositPaymentMethodGroup {
  const factory DepositPaymentMethodGroup({
    required String label,
    List<DepositPaymentMethod>? methods,
    List<BankDetail>? bankDetails,
  }) = _DepositPaymentMethodGroup;

  factory DepositPaymentMethodGroup.fromJson(Map<String, dynamic> json) =>
      _$DepositPaymentMethodGroupFromJson(json);
}

@freezed
sealed class DepositPaymentMethod with _$DepositPaymentMethod {
  const factory DepositPaymentMethod({
    required String name,
    List<String>? currencies,
    List<String>? tag,
    String? time,
    String? imageUrl,
    @JsonKey(name: 'mop', unknownEnumValue: DepositMop.unknown)
    @Default(DepositMop.unknown)
    DepositMop mop,
    String? fee,
    Map<String, dynamic>? additionalData,
    bool? enabled,
    String? defaultCurrency,
    List<CurrencyAmountDetail>? currencyAmountDetails,
    List<CliqDetail>? cliq,
    List<PremierAccountMinimumAmountConfiguration>? premierAccountMinAmount,
  }) = _DepositPaymentMethod;

  factory DepositPaymentMethod.fromJson(Map<String, dynamic> json) =>
      _$DepositPaymentMethodFromJson(json);
}

@freezed
sealed class PremierAccountMinimumAmountConfiguration
    with _$PremierAccountMinimumAmountConfiguration {
  const factory PremierAccountMinimumAmountConfiguration({
    @JsonKey(unknownEnumValue: PlatformType.unknown)
    required PlatformType platform,
    required String currency,
    required num minAmount,
    required String minAmountMessage,
  }) = _PremierAccountMinimumAmountConfiguration;

  factory PremierAccountMinimumAmountConfiguration.fromJson(
    Map<String, dynamic> json,
  ) => _$PremierAccountMinimumAmountConfigurationFromJson(json);
}

extension PremierAccountMinimumAmountConfigurationExtensions
    on PremierAccountMinimumAmountConfiguration {
  /// Formats the minimum amount using EquitiFormatter with the provided locale
  String getFormattedMinAmount(String locale) {
    return EquitiFormatter.formatNumber(value: minAmount, locale: locale);
  }
}

extension PremierAccountMinimumAmountExtensions
    on List<PremierAccountMinimumAmountConfiguration>? {
  /// Retrieves the premier account minimum amount configuration for a specific platform type.
  ///
  /// Searches through the available premier account minimum amount configurations
  /// to find the one that matches the given platform type.
  ///
  /// [platformType] The platform type to search for (e.g., mt4, mt5, dulcimer)
  ///
  /// Returns: The matching [PremierAccountMinimumAmountConfiguration] configuration or null if not found.
  PremierAccountMinimumAmountConfiguration?
  getPremierAccountMinAmountByAccountPlatformType(PlatformType? platformType) {
    return this?.firstOrNullWhere(
      (PremierAccountMinimumAmountConfiguration element) =>
          element.platform == platformType,
    );
  }
}
