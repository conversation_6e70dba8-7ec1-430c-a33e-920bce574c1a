// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deposit_payment_methods_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DepositPaymentMethodsModel {

 bool get success; DepositPaymentMethodsData get data;
/// Create a copy of DepositPaymentMethodsModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositPaymentMethodsModelCopyWith<DepositPaymentMethodsModel> get copyWith => _$DepositPaymentMethodsModelCopyWithImpl<DepositPaymentMethodsModel>(this as DepositPaymentMethodsModel, _$identity);

  /// Serializes this DepositPaymentMethodsModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositPaymentMethodsModel&&(identical(other.success, success) || other.success == success)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,data);

@override
String toString() {
  return 'DepositPaymentMethodsModel(success: $success, data: $data)';
}


}

/// @nodoc
abstract mixin class $DepositPaymentMethodsModelCopyWith<$Res>  {
  factory $DepositPaymentMethodsModelCopyWith(DepositPaymentMethodsModel value, $Res Function(DepositPaymentMethodsModel) _then) = _$DepositPaymentMethodsModelCopyWithImpl;
@useResult
$Res call({
 bool success, DepositPaymentMethodsData data
});


$DepositPaymentMethodsDataCopyWith<$Res> get data;

}
/// @nodoc
class _$DepositPaymentMethodsModelCopyWithImpl<$Res>
    implements $DepositPaymentMethodsModelCopyWith<$Res> {
  _$DepositPaymentMethodsModelCopyWithImpl(this._self, this._then);

  final DepositPaymentMethodsModel _self;
  final $Res Function(DepositPaymentMethodsModel) _then;

/// Create a copy of DepositPaymentMethodsModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? success = null,Object? data = null,}) {
  return _then(_self.copyWith(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethodsData,
  ));
}
/// Create a copy of DepositPaymentMethodsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodsDataCopyWith<$Res> get data {
  
  return $DepositPaymentMethodsDataCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _DepositPaymentMethodsModel implements DepositPaymentMethodsModel {
  const _DepositPaymentMethodsModel({required this.success, required this.data});
  factory _DepositPaymentMethodsModel.fromJson(Map<String, dynamic> json) => _$DepositPaymentMethodsModelFromJson(json);

@override final  bool success;
@override final  DepositPaymentMethodsData data;

/// Create a copy of DepositPaymentMethodsModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DepositPaymentMethodsModelCopyWith<_DepositPaymentMethodsModel> get copyWith => __$DepositPaymentMethodsModelCopyWithImpl<_DepositPaymentMethodsModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DepositPaymentMethodsModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DepositPaymentMethodsModel&&(identical(other.success, success) || other.success == success)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,data);

@override
String toString() {
  return 'DepositPaymentMethodsModel(success: $success, data: $data)';
}


}

/// @nodoc
abstract mixin class _$DepositPaymentMethodsModelCopyWith<$Res> implements $DepositPaymentMethodsModelCopyWith<$Res> {
  factory _$DepositPaymentMethodsModelCopyWith(_DepositPaymentMethodsModel value, $Res Function(_DepositPaymentMethodsModel) _then) = __$DepositPaymentMethodsModelCopyWithImpl;
@override @useResult
$Res call({
 bool success, DepositPaymentMethodsData data
});


@override $DepositPaymentMethodsDataCopyWith<$Res> get data;

}
/// @nodoc
class __$DepositPaymentMethodsModelCopyWithImpl<$Res>
    implements _$DepositPaymentMethodsModelCopyWith<$Res> {
  __$DepositPaymentMethodsModelCopyWithImpl(this._self, this._then);

  final _DepositPaymentMethodsModel _self;
  final $Res Function(_DepositPaymentMethodsModel) _then;

/// Create a copy of DepositPaymentMethodsModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? success = null,Object? data = null,}) {
  return _then(_DepositPaymentMethodsModel(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethodsData,
  ));
}

/// Create a copy of DepositPaymentMethodsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodsDataCopyWith<$Res> get data {
  
  return $DepositPaymentMethodsDataCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
mixin _$DepositPaymentMethodsData {

 List<DepositPaymentMethodGroup> get paymentsMethods; List<String> get noCentsCurrencies; num? get maxPollingTime; num? get pollingFrequency;
/// Create a copy of DepositPaymentMethodsData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositPaymentMethodsDataCopyWith<DepositPaymentMethodsData> get copyWith => _$DepositPaymentMethodsDataCopyWithImpl<DepositPaymentMethodsData>(this as DepositPaymentMethodsData, _$identity);

  /// Serializes this DepositPaymentMethodsData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositPaymentMethodsData&&const DeepCollectionEquality().equals(other.paymentsMethods, paymentsMethods)&&const DeepCollectionEquality().equals(other.noCentsCurrencies, noCentsCurrencies)&&(identical(other.maxPollingTime, maxPollingTime) || other.maxPollingTime == maxPollingTime)&&(identical(other.pollingFrequency, pollingFrequency) || other.pollingFrequency == pollingFrequency));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(paymentsMethods),const DeepCollectionEquality().hash(noCentsCurrencies),maxPollingTime,pollingFrequency);

@override
String toString() {
  return 'DepositPaymentMethodsData(paymentsMethods: $paymentsMethods, noCentsCurrencies: $noCentsCurrencies, maxPollingTime: $maxPollingTime, pollingFrequency: $pollingFrequency)';
}


}

/// @nodoc
abstract mixin class $DepositPaymentMethodsDataCopyWith<$Res>  {
  factory $DepositPaymentMethodsDataCopyWith(DepositPaymentMethodsData value, $Res Function(DepositPaymentMethodsData) _then) = _$DepositPaymentMethodsDataCopyWithImpl;
@useResult
$Res call({
 List<DepositPaymentMethodGroup> paymentsMethods, List<String> noCentsCurrencies, num? maxPollingTime, num? pollingFrequency
});




}
/// @nodoc
class _$DepositPaymentMethodsDataCopyWithImpl<$Res>
    implements $DepositPaymentMethodsDataCopyWith<$Res> {
  _$DepositPaymentMethodsDataCopyWithImpl(this._self, this._then);

  final DepositPaymentMethodsData _self;
  final $Res Function(DepositPaymentMethodsData) _then;

/// Create a copy of DepositPaymentMethodsData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? paymentsMethods = null,Object? noCentsCurrencies = null,Object? maxPollingTime = freezed,Object? pollingFrequency = freezed,}) {
  return _then(_self.copyWith(
paymentsMethods: null == paymentsMethods ? _self.paymentsMethods : paymentsMethods // ignore: cast_nullable_to_non_nullable
as List<DepositPaymentMethodGroup>,noCentsCurrencies: null == noCentsCurrencies ? _self.noCentsCurrencies : noCentsCurrencies // ignore: cast_nullable_to_non_nullable
as List<String>,maxPollingTime: freezed == maxPollingTime ? _self.maxPollingTime : maxPollingTime // ignore: cast_nullable_to_non_nullable
as num?,pollingFrequency: freezed == pollingFrequency ? _self.pollingFrequency : pollingFrequency // ignore: cast_nullable_to_non_nullable
as num?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _DepositPaymentMethodsData implements DepositPaymentMethodsData {
  const _DepositPaymentMethodsData({required final  List<DepositPaymentMethodGroup> paymentsMethods, required final  List<String> noCentsCurrencies, this.maxPollingTime, this.pollingFrequency}): _paymentsMethods = paymentsMethods,_noCentsCurrencies = noCentsCurrencies;
  factory _DepositPaymentMethodsData.fromJson(Map<String, dynamic> json) => _$DepositPaymentMethodsDataFromJson(json);

 final  List<DepositPaymentMethodGroup> _paymentsMethods;
@override List<DepositPaymentMethodGroup> get paymentsMethods {
  if (_paymentsMethods is EqualUnmodifiableListView) return _paymentsMethods;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_paymentsMethods);
}

 final  List<String> _noCentsCurrencies;
@override List<String> get noCentsCurrencies {
  if (_noCentsCurrencies is EqualUnmodifiableListView) return _noCentsCurrencies;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_noCentsCurrencies);
}

@override final  num? maxPollingTime;
@override final  num? pollingFrequency;

/// Create a copy of DepositPaymentMethodsData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DepositPaymentMethodsDataCopyWith<_DepositPaymentMethodsData> get copyWith => __$DepositPaymentMethodsDataCopyWithImpl<_DepositPaymentMethodsData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DepositPaymentMethodsDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DepositPaymentMethodsData&&const DeepCollectionEquality().equals(other._paymentsMethods, _paymentsMethods)&&const DeepCollectionEquality().equals(other._noCentsCurrencies, _noCentsCurrencies)&&(identical(other.maxPollingTime, maxPollingTime) || other.maxPollingTime == maxPollingTime)&&(identical(other.pollingFrequency, pollingFrequency) || other.pollingFrequency == pollingFrequency));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_paymentsMethods),const DeepCollectionEquality().hash(_noCentsCurrencies),maxPollingTime,pollingFrequency);

@override
String toString() {
  return 'DepositPaymentMethodsData(paymentsMethods: $paymentsMethods, noCentsCurrencies: $noCentsCurrencies, maxPollingTime: $maxPollingTime, pollingFrequency: $pollingFrequency)';
}


}

/// @nodoc
abstract mixin class _$DepositPaymentMethodsDataCopyWith<$Res> implements $DepositPaymentMethodsDataCopyWith<$Res> {
  factory _$DepositPaymentMethodsDataCopyWith(_DepositPaymentMethodsData value, $Res Function(_DepositPaymentMethodsData) _then) = __$DepositPaymentMethodsDataCopyWithImpl;
@override @useResult
$Res call({
 List<DepositPaymentMethodGroup> paymentsMethods, List<String> noCentsCurrencies, num? maxPollingTime, num? pollingFrequency
});




}
/// @nodoc
class __$DepositPaymentMethodsDataCopyWithImpl<$Res>
    implements _$DepositPaymentMethodsDataCopyWith<$Res> {
  __$DepositPaymentMethodsDataCopyWithImpl(this._self, this._then);

  final _DepositPaymentMethodsData _self;
  final $Res Function(_DepositPaymentMethodsData) _then;

/// Create a copy of DepositPaymentMethodsData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? paymentsMethods = null,Object? noCentsCurrencies = null,Object? maxPollingTime = freezed,Object? pollingFrequency = freezed,}) {
  return _then(_DepositPaymentMethodsData(
paymentsMethods: null == paymentsMethods ? _self._paymentsMethods : paymentsMethods // ignore: cast_nullable_to_non_nullable
as List<DepositPaymentMethodGroup>,noCentsCurrencies: null == noCentsCurrencies ? _self._noCentsCurrencies : noCentsCurrencies // ignore: cast_nullable_to_non_nullable
as List<String>,maxPollingTime: freezed == maxPollingTime ? _self.maxPollingTime : maxPollingTime // ignore: cast_nullable_to_non_nullable
as num?,pollingFrequency: freezed == pollingFrequency ? _self.pollingFrequency : pollingFrequency // ignore: cast_nullable_to_non_nullable
as num?,
  ));
}


}


/// @nodoc
mixin _$DepositPaymentMethodGroup {

 String get label; List<DepositPaymentMethod>? get methods; List<BankDetail>? get bankDetails;
/// Create a copy of DepositPaymentMethodGroup
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositPaymentMethodGroupCopyWith<DepositPaymentMethodGroup> get copyWith => _$DepositPaymentMethodGroupCopyWithImpl<DepositPaymentMethodGroup>(this as DepositPaymentMethodGroup, _$identity);

  /// Serializes this DepositPaymentMethodGroup to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositPaymentMethodGroup&&(identical(other.label, label) || other.label == label)&&const DeepCollectionEquality().equals(other.methods, methods)&&const DeepCollectionEquality().equals(other.bankDetails, bankDetails));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,label,const DeepCollectionEquality().hash(methods),const DeepCollectionEquality().hash(bankDetails));

@override
String toString() {
  return 'DepositPaymentMethodGroup(label: $label, methods: $methods, bankDetails: $bankDetails)';
}


}

/// @nodoc
abstract mixin class $DepositPaymentMethodGroupCopyWith<$Res>  {
  factory $DepositPaymentMethodGroupCopyWith(DepositPaymentMethodGroup value, $Res Function(DepositPaymentMethodGroup) _then) = _$DepositPaymentMethodGroupCopyWithImpl;
@useResult
$Res call({
 String label, List<DepositPaymentMethod>? methods, List<BankDetail>? bankDetails
});




}
/// @nodoc
class _$DepositPaymentMethodGroupCopyWithImpl<$Res>
    implements $DepositPaymentMethodGroupCopyWith<$Res> {
  _$DepositPaymentMethodGroupCopyWithImpl(this._self, this._then);

  final DepositPaymentMethodGroup _self;
  final $Res Function(DepositPaymentMethodGroup) _then;

/// Create a copy of DepositPaymentMethodGroup
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? label = null,Object? methods = freezed,Object? bankDetails = freezed,}) {
  return _then(_self.copyWith(
label: null == label ? _self.label : label // ignore: cast_nullable_to_non_nullable
as String,methods: freezed == methods ? _self.methods : methods // ignore: cast_nullable_to_non_nullable
as List<DepositPaymentMethod>?,bankDetails: freezed == bankDetails ? _self.bankDetails : bankDetails // ignore: cast_nullable_to_non_nullable
as List<BankDetail>?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _DepositPaymentMethodGroup implements DepositPaymentMethodGroup {
  const _DepositPaymentMethodGroup({required this.label, final  List<DepositPaymentMethod>? methods, final  List<BankDetail>? bankDetails}): _methods = methods,_bankDetails = bankDetails;
  factory _DepositPaymentMethodGroup.fromJson(Map<String, dynamic> json) => _$DepositPaymentMethodGroupFromJson(json);

@override final  String label;
 final  List<DepositPaymentMethod>? _methods;
@override List<DepositPaymentMethod>? get methods {
  final value = _methods;
  if (value == null) return null;
  if (_methods is EqualUnmodifiableListView) return _methods;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<BankDetail>? _bankDetails;
@override List<BankDetail>? get bankDetails {
  final value = _bankDetails;
  if (value == null) return null;
  if (_bankDetails is EqualUnmodifiableListView) return _bankDetails;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of DepositPaymentMethodGroup
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DepositPaymentMethodGroupCopyWith<_DepositPaymentMethodGroup> get copyWith => __$DepositPaymentMethodGroupCopyWithImpl<_DepositPaymentMethodGroup>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DepositPaymentMethodGroupToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DepositPaymentMethodGroup&&(identical(other.label, label) || other.label == label)&&const DeepCollectionEquality().equals(other._methods, _methods)&&const DeepCollectionEquality().equals(other._bankDetails, _bankDetails));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,label,const DeepCollectionEquality().hash(_methods),const DeepCollectionEquality().hash(_bankDetails));

@override
String toString() {
  return 'DepositPaymentMethodGroup(label: $label, methods: $methods, bankDetails: $bankDetails)';
}


}

/// @nodoc
abstract mixin class _$DepositPaymentMethodGroupCopyWith<$Res> implements $DepositPaymentMethodGroupCopyWith<$Res> {
  factory _$DepositPaymentMethodGroupCopyWith(_DepositPaymentMethodGroup value, $Res Function(_DepositPaymentMethodGroup) _then) = __$DepositPaymentMethodGroupCopyWithImpl;
@override @useResult
$Res call({
 String label, List<DepositPaymentMethod>? methods, List<BankDetail>? bankDetails
});




}
/// @nodoc
class __$DepositPaymentMethodGroupCopyWithImpl<$Res>
    implements _$DepositPaymentMethodGroupCopyWith<$Res> {
  __$DepositPaymentMethodGroupCopyWithImpl(this._self, this._then);

  final _DepositPaymentMethodGroup _self;
  final $Res Function(_DepositPaymentMethodGroup) _then;

/// Create a copy of DepositPaymentMethodGroup
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? label = null,Object? methods = freezed,Object? bankDetails = freezed,}) {
  return _then(_DepositPaymentMethodGroup(
label: null == label ? _self.label : label // ignore: cast_nullable_to_non_nullable
as String,methods: freezed == methods ? _self._methods : methods // ignore: cast_nullable_to_non_nullable
as List<DepositPaymentMethod>?,bankDetails: freezed == bankDetails ? _self._bankDetails : bankDetails // ignore: cast_nullable_to_non_nullable
as List<BankDetail>?,
  ));
}


}


/// @nodoc
mixin _$DepositPaymentMethod {

 String get name; List<String>? get currencies; List<String>? get tag; String? get time; String? get imageUrl;@JsonKey(name: 'mop', unknownEnumValue: DepositMop.unknown) DepositMop get mop; String? get fee; Map<String, dynamic>? get additionalData; bool? get enabled; String? get defaultCurrency; List<CurrencyAmountDetail>? get currencyAmountDetails; List<CliqDetail>? get cliq; List<PremierAccountMinimumAmountConfiguration>? get premierAccountMinAmount;
/// Create a copy of DepositPaymentMethod
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositPaymentMethodCopyWith<DepositPaymentMethod> get copyWith => _$DepositPaymentMethodCopyWithImpl<DepositPaymentMethod>(this as DepositPaymentMethod, _$identity);

  /// Serializes this DepositPaymentMethod to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositPaymentMethod&&(identical(other.name, name) || other.name == name)&&const DeepCollectionEquality().equals(other.currencies, currencies)&&const DeepCollectionEquality().equals(other.tag, tag)&&(identical(other.time, time) || other.time == time)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.mop, mop) || other.mop == mop)&&(identical(other.fee, fee) || other.fee == fee)&&const DeepCollectionEquality().equals(other.additionalData, additionalData)&&(identical(other.enabled, enabled) || other.enabled == enabled)&&(identical(other.defaultCurrency, defaultCurrency) || other.defaultCurrency == defaultCurrency)&&const DeepCollectionEquality().equals(other.currencyAmountDetails, currencyAmountDetails)&&const DeepCollectionEquality().equals(other.cliq, cliq)&&const DeepCollectionEquality().equals(other.premierAccountMinAmount, premierAccountMinAmount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,const DeepCollectionEquality().hash(currencies),const DeepCollectionEquality().hash(tag),time,imageUrl,mop,fee,const DeepCollectionEquality().hash(additionalData),enabled,defaultCurrency,const DeepCollectionEquality().hash(currencyAmountDetails),const DeepCollectionEquality().hash(cliq),const DeepCollectionEquality().hash(premierAccountMinAmount));

@override
String toString() {
  return 'DepositPaymentMethod(name: $name, currencies: $currencies, tag: $tag, time: $time, imageUrl: $imageUrl, mop: $mop, fee: $fee, additionalData: $additionalData, enabled: $enabled, defaultCurrency: $defaultCurrency, currencyAmountDetails: $currencyAmountDetails, cliq: $cliq, premierAccountMinAmount: $premierAccountMinAmount)';
}


}

/// @nodoc
abstract mixin class $DepositPaymentMethodCopyWith<$Res>  {
  factory $DepositPaymentMethodCopyWith(DepositPaymentMethod value, $Res Function(DepositPaymentMethod) _then) = _$DepositPaymentMethodCopyWithImpl;
@useResult
$Res call({
 String name, List<String>? currencies, List<String>? tag, String? time, String? imageUrl,@JsonKey(name: 'mop', unknownEnumValue: DepositMop.unknown) DepositMop mop, String? fee, Map<String, dynamic>? additionalData, bool? enabled, String? defaultCurrency, List<CurrencyAmountDetail>? currencyAmountDetails, List<CliqDetail>? cliq, List<PremierAccountMinimumAmountConfiguration>? premierAccountMinAmount
});




}
/// @nodoc
class _$DepositPaymentMethodCopyWithImpl<$Res>
    implements $DepositPaymentMethodCopyWith<$Res> {
  _$DepositPaymentMethodCopyWithImpl(this._self, this._then);

  final DepositPaymentMethod _self;
  final $Res Function(DepositPaymentMethod) _then;

/// Create a copy of DepositPaymentMethod
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? currencies = freezed,Object? tag = freezed,Object? time = freezed,Object? imageUrl = freezed,Object? mop = null,Object? fee = freezed,Object? additionalData = freezed,Object? enabled = freezed,Object? defaultCurrency = freezed,Object? currencyAmountDetails = freezed,Object? cliq = freezed,Object? premierAccountMinAmount = freezed,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,currencies: freezed == currencies ? _self.currencies : currencies // ignore: cast_nullable_to_non_nullable
as List<String>?,tag: freezed == tag ? _self.tag : tag // ignore: cast_nullable_to_non_nullable
as List<String>?,time: freezed == time ? _self.time : time // ignore: cast_nullable_to_non_nullable
as String?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,mop: null == mop ? _self.mop : mop // ignore: cast_nullable_to_non_nullable
as DepositMop,fee: freezed == fee ? _self.fee : fee // ignore: cast_nullable_to_non_nullable
as String?,additionalData: freezed == additionalData ? _self.additionalData : additionalData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,enabled: freezed == enabled ? _self.enabled : enabled // ignore: cast_nullable_to_non_nullable
as bool?,defaultCurrency: freezed == defaultCurrency ? _self.defaultCurrency : defaultCurrency // ignore: cast_nullable_to_non_nullable
as String?,currencyAmountDetails: freezed == currencyAmountDetails ? _self.currencyAmountDetails : currencyAmountDetails // ignore: cast_nullable_to_non_nullable
as List<CurrencyAmountDetail>?,cliq: freezed == cliq ? _self.cliq : cliq // ignore: cast_nullable_to_non_nullable
as List<CliqDetail>?,premierAccountMinAmount: freezed == premierAccountMinAmount ? _self.premierAccountMinAmount : premierAccountMinAmount // ignore: cast_nullable_to_non_nullable
as List<PremierAccountMinimumAmountConfiguration>?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _DepositPaymentMethod implements DepositPaymentMethod {
  const _DepositPaymentMethod({required this.name, final  List<String>? currencies, final  List<String>? tag, this.time, this.imageUrl, @JsonKey(name: 'mop', unknownEnumValue: DepositMop.unknown) this.mop = DepositMop.unknown, this.fee, final  Map<String, dynamic>? additionalData, this.enabled, this.defaultCurrency, final  List<CurrencyAmountDetail>? currencyAmountDetails, final  List<CliqDetail>? cliq, final  List<PremierAccountMinimumAmountConfiguration>? premierAccountMinAmount}): _currencies = currencies,_tag = tag,_additionalData = additionalData,_currencyAmountDetails = currencyAmountDetails,_cliq = cliq,_premierAccountMinAmount = premierAccountMinAmount;
  factory _DepositPaymentMethod.fromJson(Map<String, dynamic> json) => _$DepositPaymentMethodFromJson(json);

@override final  String name;
 final  List<String>? _currencies;
@override List<String>? get currencies {
  final value = _currencies;
  if (value == null) return null;
  if (_currencies is EqualUnmodifiableListView) return _currencies;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<String>? _tag;
@override List<String>? get tag {
  final value = _tag;
  if (value == null) return null;
  if (_tag is EqualUnmodifiableListView) return _tag;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? time;
@override final  String? imageUrl;
@override@JsonKey(name: 'mop', unknownEnumValue: DepositMop.unknown) final  DepositMop mop;
@override final  String? fee;
 final  Map<String, dynamic>? _additionalData;
@override Map<String, dynamic>? get additionalData {
  final value = _additionalData;
  if (value == null) return null;
  if (_additionalData is EqualUnmodifiableMapView) return _additionalData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

@override final  bool? enabled;
@override final  String? defaultCurrency;
 final  List<CurrencyAmountDetail>? _currencyAmountDetails;
@override List<CurrencyAmountDetail>? get currencyAmountDetails {
  final value = _currencyAmountDetails;
  if (value == null) return null;
  if (_currencyAmountDetails is EqualUnmodifiableListView) return _currencyAmountDetails;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<CliqDetail>? _cliq;
@override List<CliqDetail>? get cliq {
  final value = _cliq;
  if (value == null) return null;
  if (_cliq is EqualUnmodifiableListView) return _cliq;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

 final  List<PremierAccountMinimumAmountConfiguration>? _premierAccountMinAmount;
@override List<PremierAccountMinimumAmountConfiguration>? get premierAccountMinAmount {
  final value = _premierAccountMinAmount;
  if (value == null) return null;
  if (_premierAccountMinAmount is EqualUnmodifiableListView) return _premierAccountMinAmount;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of DepositPaymentMethod
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DepositPaymentMethodCopyWith<_DepositPaymentMethod> get copyWith => __$DepositPaymentMethodCopyWithImpl<_DepositPaymentMethod>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DepositPaymentMethodToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DepositPaymentMethod&&(identical(other.name, name) || other.name == name)&&const DeepCollectionEquality().equals(other._currencies, _currencies)&&const DeepCollectionEquality().equals(other._tag, _tag)&&(identical(other.time, time) || other.time == time)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.mop, mop) || other.mop == mop)&&(identical(other.fee, fee) || other.fee == fee)&&const DeepCollectionEquality().equals(other._additionalData, _additionalData)&&(identical(other.enabled, enabled) || other.enabled == enabled)&&(identical(other.defaultCurrency, defaultCurrency) || other.defaultCurrency == defaultCurrency)&&const DeepCollectionEquality().equals(other._currencyAmountDetails, _currencyAmountDetails)&&const DeepCollectionEquality().equals(other._cliq, _cliq)&&const DeepCollectionEquality().equals(other._premierAccountMinAmount, _premierAccountMinAmount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,const DeepCollectionEquality().hash(_currencies),const DeepCollectionEquality().hash(_tag),time,imageUrl,mop,fee,const DeepCollectionEquality().hash(_additionalData),enabled,defaultCurrency,const DeepCollectionEquality().hash(_currencyAmountDetails),const DeepCollectionEquality().hash(_cliq),const DeepCollectionEquality().hash(_premierAccountMinAmount));

@override
String toString() {
  return 'DepositPaymentMethod(name: $name, currencies: $currencies, tag: $tag, time: $time, imageUrl: $imageUrl, mop: $mop, fee: $fee, additionalData: $additionalData, enabled: $enabled, defaultCurrency: $defaultCurrency, currencyAmountDetails: $currencyAmountDetails, cliq: $cliq, premierAccountMinAmount: $premierAccountMinAmount)';
}


}

/// @nodoc
abstract mixin class _$DepositPaymentMethodCopyWith<$Res> implements $DepositPaymentMethodCopyWith<$Res> {
  factory _$DepositPaymentMethodCopyWith(_DepositPaymentMethod value, $Res Function(_DepositPaymentMethod) _then) = __$DepositPaymentMethodCopyWithImpl;
@override @useResult
$Res call({
 String name, List<String>? currencies, List<String>? tag, String? time, String? imageUrl,@JsonKey(name: 'mop', unknownEnumValue: DepositMop.unknown) DepositMop mop, String? fee, Map<String, dynamic>? additionalData, bool? enabled, String? defaultCurrency, List<CurrencyAmountDetail>? currencyAmountDetails, List<CliqDetail>? cliq, List<PremierAccountMinimumAmountConfiguration>? premierAccountMinAmount
});




}
/// @nodoc
class __$DepositPaymentMethodCopyWithImpl<$Res>
    implements _$DepositPaymentMethodCopyWith<$Res> {
  __$DepositPaymentMethodCopyWithImpl(this._self, this._then);

  final _DepositPaymentMethod _self;
  final $Res Function(_DepositPaymentMethod) _then;

/// Create a copy of DepositPaymentMethod
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? currencies = freezed,Object? tag = freezed,Object? time = freezed,Object? imageUrl = freezed,Object? mop = null,Object? fee = freezed,Object? additionalData = freezed,Object? enabled = freezed,Object? defaultCurrency = freezed,Object? currencyAmountDetails = freezed,Object? cliq = freezed,Object? premierAccountMinAmount = freezed,}) {
  return _then(_DepositPaymentMethod(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,currencies: freezed == currencies ? _self._currencies : currencies // ignore: cast_nullable_to_non_nullable
as List<String>?,tag: freezed == tag ? _self._tag : tag // ignore: cast_nullable_to_non_nullable
as List<String>?,time: freezed == time ? _self.time : time // ignore: cast_nullable_to_non_nullable
as String?,imageUrl: freezed == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String?,mop: null == mop ? _self.mop : mop // ignore: cast_nullable_to_non_nullable
as DepositMop,fee: freezed == fee ? _self.fee : fee // ignore: cast_nullable_to_non_nullable
as String?,additionalData: freezed == additionalData ? _self._additionalData : additionalData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,enabled: freezed == enabled ? _self.enabled : enabled // ignore: cast_nullable_to_non_nullable
as bool?,defaultCurrency: freezed == defaultCurrency ? _self.defaultCurrency : defaultCurrency // ignore: cast_nullable_to_non_nullable
as String?,currencyAmountDetails: freezed == currencyAmountDetails ? _self._currencyAmountDetails : currencyAmountDetails // ignore: cast_nullable_to_non_nullable
as List<CurrencyAmountDetail>?,cliq: freezed == cliq ? _self._cliq : cliq // ignore: cast_nullable_to_non_nullable
as List<CliqDetail>?,premierAccountMinAmount: freezed == premierAccountMinAmount ? _self._premierAccountMinAmount : premierAccountMinAmount // ignore: cast_nullable_to_non_nullable
as List<PremierAccountMinimumAmountConfiguration>?,
  ));
}


}


/// @nodoc
mixin _$PremierAccountMinimumAmountConfiguration {

@JsonKey(unknownEnumValue: PlatformType.unknown) PlatformType get platform; String get currency; num get minAmount; String get minAmountMessage;
/// Create a copy of PremierAccountMinimumAmountConfiguration
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PremierAccountMinimumAmountConfigurationCopyWith<PremierAccountMinimumAmountConfiguration> get copyWith => _$PremierAccountMinimumAmountConfigurationCopyWithImpl<PremierAccountMinimumAmountConfiguration>(this as PremierAccountMinimumAmountConfiguration, _$identity);

  /// Serializes this PremierAccountMinimumAmountConfiguration to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PremierAccountMinimumAmountConfiguration&&(identical(other.platform, platform) || other.platform == platform)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.minAmount, minAmount) || other.minAmount == minAmount)&&(identical(other.minAmountMessage, minAmountMessage) || other.minAmountMessage == minAmountMessage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,platform,currency,minAmount,minAmountMessage);

@override
String toString() {
  return 'PremierAccountMinimumAmountConfiguration(platform: $platform, currency: $currency, minAmount: $minAmount, minAmountMessage: $minAmountMessage)';
}


}

/// @nodoc
abstract mixin class $PremierAccountMinimumAmountConfigurationCopyWith<$Res>  {
  factory $PremierAccountMinimumAmountConfigurationCopyWith(PremierAccountMinimumAmountConfiguration value, $Res Function(PremierAccountMinimumAmountConfiguration) _then) = _$PremierAccountMinimumAmountConfigurationCopyWithImpl;
@useResult
$Res call({
@JsonKey(unknownEnumValue: PlatformType.unknown) PlatformType platform, String currency, num minAmount, String minAmountMessage
});




}
/// @nodoc
class _$PremierAccountMinimumAmountConfigurationCopyWithImpl<$Res>
    implements $PremierAccountMinimumAmountConfigurationCopyWith<$Res> {
  _$PremierAccountMinimumAmountConfigurationCopyWithImpl(this._self, this._then);

  final PremierAccountMinimumAmountConfiguration _self;
  final $Res Function(PremierAccountMinimumAmountConfiguration) _then;

/// Create a copy of PremierAccountMinimumAmountConfiguration
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? platform = null,Object? currency = null,Object? minAmount = null,Object? minAmountMessage = null,}) {
  return _then(_self.copyWith(
platform: null == platform ? _self.platform : platform // ignore: cast_nullable_to_non_nullable
as PlatformType,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,minAmount: null == minAmount ? _self.minAmount : minAmount // ignore: cast_nullable_to_non_nullable
as num,minAmountMessage: null == minAmountMessage ? _self.minAmountMessage : minAmountMessage // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _PremierAccountMinimumAmountConfiguration implements PremierAccountMinimumAmountConfiguration {
  const _PremierAccountMinimumAmountConfiguration({@JsonKey(unknownEnumValue: PlatformType.unknown) required this.platform, required this.currency, required this.minAmount, required this.minAmountMessage});
  factory _PremierAccountMinimumAmountConfiguration.fromJson(Map<String, dynamic> json) => _$PremierAccountMinimumAmountConfigurationFromJson(json);

@override@JsonKey(unknownEnumValue: PlatformType.unknown) final  PlatformType platform;
@override final  String currency;
@override final  num minAmount;
@override final  String minAmountMessage;

/// Create a copy of PremierAccountMinimumAmountConfiguration
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PremierAccountMinimumAmountConfigurationCopyWith<_PremierAccountMinimumAmountConfiguration> get copyWith => __$PremierAccountMinimumAmountConfigurationCopyWithImpl<_PremierAccountMinimumAmountConfiguration>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PremierAccountMinimumAmountConfigurationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PremierAccountMinimumAmountConfiguration&&(identical(other.platform, platform) || other.platform == platform)&&(identical(other.currency, currency) || other.currency == currency)&&(identical(other.minAmount, minAmount) || other.minAmount == minAmount)&&(identical(other.minAmountMessage, minAmountMessage) || other.minAmountMessage == minAmountMessage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,platform,currency,minAmount,minAmountMessage);

@override
String toString() {
  return 'PremierAccountMinimumAmountConfiguration(platform: $platform, currency: $currency, minAmount: $minAmount, minAmountMessage: $minAmountMessage)';
}


}

/// @nodoc
abstract mixin class _$PremierAccountMinimumAmountConfigurationCopyWith<$Res> implements $PremierAccountMinimumAmountConfigurationCopyWith<$Res> {
  factory _$PremierAccountMinimumAmountConfigurationCopyWith(_PremierAccountMinimumAmountConfiguration value, $Res Function(_PremierAccountMinimumAmountConfiguration) _then) = __$PremierAccountMinimumAmountConfigurationCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(unknownEnumValue: PlatformType.unknown) PlatformType platform, String currency, num minAmount, String minAmountMessage
});




}
/// @nodoc
class __$PremierAccountMinimumAmountConfigurationCopyWithImpl<$Res>
    implements _$PremierAccountMinimumAmountConfigurationCopyWith<$Res> {
  __$PremierAccountMinimumAmountConfigurationCopyWithImpl(this._self, this._then);

  final _PremierAccountMinimumAmountConfiguration _self;
  final $Res Function(_PremierAccountMinimumAmountConfiguration) _then;

/// Create a copy of PremierAccountMinimumAmountConfiguration
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? platform = null,Object? currency = null,Object? minAmount = null,Object? minAmountMessage = null,}) {
  return _then(_PremierAccountMinimumAmountConfiguration(
platform: null == platform ? _self.platform : platform // ignore: cast_nullable_to_non_nullable
as PlatformType,currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,minAmount: null == minAmount ? _self.minAmount : minAmount // ignore: cast_nullable_to_non_nullable
as num,minAmountMessage: null == minAmountMessage ? _self.minAmountMessage : minAmountMessage // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
