//@GeneratedMicroModule;PaymentPackageModule;package:payment/src/di/di_initializer.module.dart
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i687;

import 'package:api_client/api_client.dart' as _i633;
import 'package:domain/domain.dart' as _i494;
import 'package:equiti_analytics/equiti_analytics.dart' as _i917;
import 'package:equiti_auth/equiti_auth.dart' as _i313;
import 'package:injectable/injectable.dart' as _i526;
import 'package:payment/src/analytics/deposit_analytics_event.dart' as _i62;
import 'package:payment/src/analytics/transfer_analytics_event.dart' as _i1005;
import 'package:payment/src/analytics/withdraw_analytics_event.dart' as _i558;
import 'package:payment/src/di/payment_module.dart' as _i158;
import 'package:payment/src/domain/repository/deposit_repository.dart' as _i596;
import 'package:payment/src/domain/repository/deposit_status_repository.dart'
    as _i382;
import 'package:payment/src/domain/repository/list_of_accounts_repository.dart'
    as _i870;
import 'package:payment/src/domain/repository/payment_options_repository.dart'
    as _i869;
import 'package:payment/src/domain/repository/transfer_repository.dart'
    as _i134;
import 'package:payment/src/domain/repository/withdraw_repository.dart'
    as _i874;
import 'package:payment/src/domain/usecase/check_withdrawal_allowed_usecase.dart'
    as _i18;
import 'package:payment/src/domain/usecase/conversion_rate_usecase.dart'
    as _i49;
import 'package:payment/src/domain/usecase/delete_bank_account_use_case.dart'
    as _i611;
import 'package:payment/src/domain/usecase/deposit_status_usecase.dart'
    as _i418;
import 'package:payment/src/domain/usecase/get_bank_accounts_use_case.dart'
    as _i652;
import 'package:payment/src/domain/usecase/get_deposit_details_usecase.dart'
    as _i49;
import 'package:payment/src/domain/usecase/get_selected_account_id_use_case.dart'
    as _i715;
import 'package:payment/src/domain/usecase/get_transfer_type_usecase.dart'
    as _i770;
import 'package:payment/src/domain/usecase/get_withdraw_card_usecase.dart'
    as _i673;
import 'package:payment/src/domain/usecase/get_withdrawal_fees_usecase.dart'
    as _i1009;
import 'package:payment/src/domain/usecase/list_of_account_use_case.dart'
    as _i33;
import 'package:payment/src/domain/usecase/payment_options_deposit_usecase.dart'
    as _i710;
import 'package:payment/src/domain/usecase/payment_options_withdraw_usecase.dart'
    as _i563;
import 'package:payment/src/domain/usecase/submit_transfer_use_case.dart'
    as _i988;
import 'package:payment/src/domain/usecase/upload_document_usecase.dart'
    as _i213;
import 'package:payment/src/domain/usecase/withdrawal_usecase.dart' as _i885;
import 'package:payment/src/navigation/payment_navigation.dart' as _i237;
import 'package:payment/src/presentation/deposit_accounts_and_amount/bloc/deposit_accounts_and_amount_bloc.dart'
    as _i113;
import 'package:payment/src/presentation/deposit_payment_methods/bloc/deposit_payment_options_bloc.dart'
    as _i201;
import 'package:payment/src/presentation/payment_status/bloc/deposit_status_bloc.dart'
    as _i866;
import 'package:payment/src/presentation/transfer_funds/bloc/transfer_funds_bloc.dart'
    as _i911;
import 'package:payment/src/presentation/transfer_funds/Transfer_funds_dest_selection/bloc/transfer_funds_dest_selection_bloc.dart'
    as _i852;
import 'package:payment/src/presentation/widgets/account_list_widget/bloc/account_list_bloc.dart'
    as _i427;
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/bloc/deposit_withdraw_amount_conversion_bloc.dart'
    as _i531;
import 'package:payment/src/presentation/widgets/amount_conversion_widget/transfer_amount_conversion_widget/bloc/transfer_amount_conversion_bloc.dart'
    as _i302;
import 'package:payment/src/presentation/widgets/withdraw_fees_display/bloc/withdraw_fees_bloc.dart'
    as _i319;
import 'package:payment/src/presentation/withdraw_accounts_and_amount/bloc/withdraw_accounts_and_amount_bloc.dart'
    as _i647;
import 'package:payment/src/presentation/withdraw_add_new_bank/bloc/withdraw_add_new_bank_bloc.dart'
    as _i366;
import 'package:payment/src/presentation/withdraw_bank_transfer/bloc/withdraw_bank_transfer_bloc.dart'
    as _i989;
import 'package:payment/src/presentation/withdraw_bank_transfer/transfer_type/bloc/transfer_type_bloc.dart'
    as _i37;
import 'package:payment/src/presentation/withdraw_new_bank_upload_doc/bloc/withdraw_new_bank_upload_doc_bloc.dart'
    as _i187;
import 'package:payment/src/presentation/withdraw_options/withdraw_card_page/bloc/withdraw_card_bloc.dart'
    as _i45;
import 'package:payment/src/presentation/withdraw_payment_methods/bloc/withdraw_payment_methods_bloc.dart'
    as _i1058;
import 'package:payment/src/presentation/withdraw_skrill_and_neteller/bloc/withdraw_skrill_and_neteller_bloc.dart'
    as _i487;
import 'package:payment/src/utils/payment/apple_pay_utils.dart' as _i683;
import 'package:payment/src/utils/payment/google_pay_utils.dart' as _i184;
import 'package:preferences/preferences.dart' as _i695;
import 'package:user_account/user_account.dart' as _i43;

class PaymentPackageModule extends _i526.MicroPackageModule {
  // initializes the registration of main-scope dependencies inside of GetIt
  @override
  _i687.FutureOr<void> init(_i526.GetItHelper gh) {
    final paymentModule = _$PaymentModule();
    gh.factory<_i184.GooglePayUtils>(() => paymentModule.googlePayUtils());
    gh.factory<_i683.ApplePayUtils>(() => paymentModule.applePayUtils());
    gh.factory<_i911.TransferFundsBloc>(
      () => paymentModule.transferFundsBloc(gh<_i237.PaymentNavigation>()),
    );
    gh.factory<_i869.PaymentOptionsRepository>(
      () => paymentModule.paymentOptionsRepository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
      ),
    );
    gh.factory<_i596.DepositRepository>(
      () => paymentModule.depositRepository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
      ),
    );
    gh.factory<_i382.DepositStatusRepository>(
      () => paymentModule.paymentStatusRepository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
      ),
    );
    gh.factory<_i134.TransferRepository>(
      () => paymentModule.transferFundsRepository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
      ),
    );
    gh.factory<_i870.ListOfAccountsRepository>(
      () => paymentModule.listOfAccountsRepository(gh<_i633.ApiClientBase>()),
    );
    gh.factory<_i49.GetDepositDetailsUsecase>(
      () =>
          paymentModule.getDepositDetailsUsecase(gh<_i596.DepositRepository>()),
    );
    gh.factory<_i715.GetSelectedAccountIdUseCase>(
      () => paymentModule.getSelectedAccountIdUseCase(
        gh<_i695.EquitiPreferences>(),
      ),
    );
    gh.factory<_i33.ListOfAccountUseCase>(
      () => paymentModule.listOfAccountUseCase(
        gh<_i870.ListOfAccountsRepository>(),
      ),
    );
    gh.lazySingleton<_i62.DepositAnalyticsEvent>(
      () => paymentModule.depositAnalyticsEvent(gh<_i917.AnalyticsService>()),
    );
    gh.lazySingleton<_i1005.TransferAnalyticsEvent>(
      () => paymentModule.transferAnalyticsEvent(gh<_i917.AnalyticsService>()),
    );
    gh.lazySingleton<_i558.WithdrawAnalyticsEvent>(
      () => paymentModule.withdrawAnalyticsEvent(gh<_i917.AnalyticsService>()),
    );
    gh.factory<_i427.AccountListBloc>(
      () => paymentModule.accountListBloc(
        gh<_i33.ListOfAccountUseCase>(),
        gh<_i237.PaymentNavigation>(),
        gh<_i715.GetSelectedAccountIdUseCase>(),
      ),
    );
    gh.factory<_i988.SubmitTransferUseCase>(
      () => paymentModule.submitTransferUseCase(gh<_i134.TransferRepository>()),
    );
    gh.factory<_i113.DepositAccountsAndAmountBloc>(
      () => paymentModule.paymentsAccountsAndAmountBloc(
        gh<_i49.GetDepositDetailsUsecase>(),
        gh<_i237.PaymentNavigation>(),
        gh<_i184.GooglePayUtils>(),
        gh<_i683.ApplePayUtils>(),
        gh<_i62.DepositAnalyticsEvent>(),
      ),
    );
    gh.factory<_i1009.GetWithdrawalFeesUsecase>(
      () => paymentModule.getWithdrawalFeesUsecase(
        gh<_i869.PaymentOptionsRepository>(),
        gh<_i43.ClientProfileUseCase>(),
        gh<_i43.GetBrokerIdUseCase>(),
      ),
    );
    gh.factory<_i710.PaymentOptionsDepositUsecase>(
      () => paymentModule.paymentOptionsUsecase(
        gh<_i869.PaymentOptionsRepository>(),
        gh<_i43.GetBrokerIdUseCase>(),
      ),
    );
    gh.factory<_i49.ConversionRateUsecase>(
      () => paymentModule.conversionRateUsecase(
        gh<_i869.PaymentOptionsRepository>(),
        gh<_i43.GetBrokerIdUseCase>(),
      ),
    );
    gh.factory<_i563.PaymentOptionsWithdrawUsecase>(
      () => paymentModule.paymentOptionsWithdrawUsecase(
        gh<_i869.PaymentOptionsRepository>(),
        gh<_i43.GetBrokerIdUseCase>(),
      ),
    );
    gh.factory<_i418.DepositStatusUsecase>(
      () => paymentModule.paymentStatusUsecase(
        gh<_i382.DepositStatusRepository>(),
      ),
    );
    gh.factory<_i874.WithdrawRepository>(
      () => paymentModule.withdrawRepository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
        gh<_i313.AuthService>(),
        gh<_i558.WithdrawAnalyticsEvent>(),
      ),
    );
    gh.factory<_i201.DepositPaymentOptionsBloc>(
      () => paymentModule.depositPaymentOptionsBloc(
        gh<_i710.PaymentOptionsDepositUsecase>(),
        gh<_i237.PaymentNavigation>(),
        gh<_i43.ClientProfileUseCase>(),
        gh<_i62.DepositAnalyticsEvent>(),
      ),
    );
    gh.factory<_i1058.WithdrawPaymentMethodsBloc>(
      () => paymentModule.withdrawPaymentMethodsBloc(
        gh<_i563.PaymentOptionsWithdrawUsecase>(),
        gh<_i237.PaymentNavigation>(),
        gh<_i43.ClientProfileUseCase>(),
      ),
    );
    gh.factory<_i319.WithdrawFeesBloc>(
      () => paymentModule.withdrawFeesBloc(
        gh<_i1009.GetWithdrawalFeesUsecase>(),
        gh<_i558.WithdrawAnalyticsEvent>(),
      ),
    );
    gh.factory<_i866.DepositStatusBloc>(
      () => paymentModule.paymentStatusBloc(gh<_i418.DepositStatusUsecase>()),
    );
    gh.factory<_i302.TransferAmountConversionBloc>(
      () => paymentModule.transferAmountConversionBloc(
        gh<_i49.ConversionRateUsecase>(),
      ),
    );
    gh.factory<_i673.GetWithdrawCardUseCase>(
      () =>
          paymentModule.getWithdrawCardUseCase(gh<_i874.WithdrawRepository>()),
    );
    gh.factory<_i770.GetTransferTypeUseCase>(
      () =>
          paymentModule.getTransferTypeUseCase(gh<_i874.WithdrawRepository>()),
    );
    gh.factory<_i652.GetBankAccountsUseCase>(
      () =>
          paymentModule.getBankAccountsUseCase(gh<_i874.WithdrawRepository>()),
    );
    gh.factory<_i611.DeleteBankAccountUseCase>(
      () => paymentModule.deleteBankAccountUseCase(
        gh<_i874.WithdrawRepository>(),
      ),
    );
    gh.factory<_i213.UploadDocumentUseCase>(
      () => paymentModule.uploadDocumentUseCase(gh<_i874.WithdrawRepository>()),
    );
    gh.factory<_i187.WithdrawNewBankUploadDocBloc>(
      () => paymentModule.withdrawNewBankUploadDocBloc(
        uploadDocumentUseCase: gh<_i213.UploadDocumentUseCase>(),
        withdrawAnalyticsEvent: gh<_i558.WithdrawAnalyticsEvent>(),
      ),
    );
    gh.factory<_i885.WithdrawalUseCase>(
      () => paymentModule.withdrawUseCase(
        gh<_i874.WithdrawRepository>(),
        gh<_i43.ClientProfileUseCase>(),
      ),
    );
    gh.factory<_i531.DepositWithdrawAmountConversionBloc>(
      () => paymentModule.depositWithdrawAmountConversionBloc(
        gh<_i49.ConversionRateUsecase>(),
        gh<_i62.DepositAnalyticsEvent>(),
        gh<_i558.WithdrawAnalyticsEvent>(),
      ),
    );
    gh.factory<_i366.WithdrawAddNewBankBloc>(
      () => paymentModule.withdrawAddNewBankBloc(
        gh<_i494.GetCountryUseCase>(),
        gh<_i770.GetTransferTypeUseCase>(),
        gh<_i237.PaymentNavigation>(),
        gh<_i885.WithdrawalUseCase>(),
        gh<_i558.WithdrawAnalyticsEvent>(),
      ),
    );
    gh.factory<_i45.WithdrawCardBloc>(
      () => paymentModule.withdrawCardBloc(
        gh<_i673.GetWithdrawCardUseCase>(),
        gh<_i237.PaymentNavigation>(),
        gh<_i558.WithdrawAnalyticsEvent>(),
      ),
    );
    gh.factory<_i852.TransferFundsDestSelectionBloc>(
      () => paymentModule.transferFundsDestSelectionBloc(
        gh<_i988.SubmitTransferUseCase>(),
        gh<_i1005.TransferAnalyticsEvent>(),
      ),
    );
    gh.factory<_i989.WithdrawBankTransferBloc>(
      () => paymentModule.withdrawBankTransferBloc(
        gh<_i652.GetBankAccountsUseCase>(),
        gh<_i237.PaymentNavigation>(),
        gh<_i611.DeleteBankAccountUseCase>(),
        gh<_i558.WithdrawAnalyticsEvent>(),
      ),
    );
    gh.factory<_i647.WithdrawAccountsAndAmountBloc>(
      () => paymentModule.withdrawAccountsAndAmountBloc(
        gh<_i237.PaymentNavigation>(),
        gh<_i885.WithdrawalUseCase>(),
        gh<_i1009.GetWithdrawalFeesUsecase>(),
      ),
    );
    gh.factory<_i37.TransferTypeBloc>(
      () => paymentModule.transferTypeBloc(
        gh<_i885.WithdrawalUseCase>(),
        gh<_i43.ClientProfileUseCase>(),
      ),
    );
    gh.factory<_i18.CheckWithdrawalAllowedUsecase>(
      () => paymentModule.checkWithdrawalAllowedUsecase(
        gh<_i874.WithdrawRepository>(),
      ),
    );
    gh.factory<_i487.WithdrawSkrillAndNetellerBloc>(
      () => paymentModule.withdrawSkrillAndNetellerBloc(
        gh<_i18.CheckWithdrawalAllowedUsecase>(),
        gh<_i558.WithdrawAnalyticsEvent>(),
      ),
    );
  }
}

class _$PaymentModule extends _i158.PaymentModule {}
