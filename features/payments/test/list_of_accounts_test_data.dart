// ignore_for_file: prefer-single-widget-per-file, prefer-match-file-name

import 'package:flutter/material.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/account_list_widget.dart';

/// Test data widgets for Padding(
/// padding: EdgeInsetsGeometry.all(16),
/// child:AccountListWidget golden tests
/// These widgets wrap Padding(
/// padding: EdgeInsetsGeometry.all(16),
/// child:AccountListWidget in proper Material theme context

class ListOfAccountsSuccessTestData extends StatelessWidget {
  const ListOfAccountsSuccessTestData({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Account List - Success')),
      body: Padding(
        padding: EdgeInsetsGeometry.all(16),
        child: AccountListWidget(
          args: (
            selectByHighestBalance: false,
            excludeAccountNumber: null,
            onEmptyStateChanged: null,
            isInputDisabled: false,
          ),
          onAccountSelected: (selectedAccount) {
            // Handle account selection in test
          },
        ),
      ),
    );
  }
}

class ListOfAccountsFailureTestData extends StatelessWidget {
  const ListOfAccountsFailureTestData({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Account List - Failure')),
      body: Padding(
        padding: EdgeInsetsGeometry.all(16),
        child: AccountListWidget(
          args: (
            selectByHighestBalance: false,
            excludeAccountNumber: null,
            onEmptyStateChanged: null,
            isInputDisabled: false,
          ),
          onAccountSelected: (selectedAccount) {
            // Handle account selection in test
          },
        ),
      ),
    );
  }
}

class ListOfAccountsLoadingTestData extends StatelessWidget {
  const ListOfAccountsLoadingTestData({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Account List - Loading')),
      body: Padding(
        padding: EdgeInsetsGeometry.all(16),
        child: AccountListWidget(
          args: (
            selectByHighestBalance: false,
            excludeAccountNumber: null,
            onEmptyStateChanged: ({
              required bool hasAccounts,
              required bool hasWallets,
              required bool isCurrentTabEmpty,
              required int currentTabIndex,
            }) {
              // Handle empty state changes in test
            },
            isInputDisabled: false,
          ),
          onAccountSelected: (selectedAccount) {
            // Handle account selection in test
          },
        ),
      ),
    );
  }
}

class ListOfAccountsEmptyTestData extends StatelessWidget {
  const ListOfAccountsEmptyTestData({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Account List - Empty')),
      body: Padding(
        padding: EdgeInsetsGeometry.all(16),
        child: AccountListWidget(
          args: (
            selectByHighestBalance: false,
            excludeAccountNumber: null,
            onEmptyStateChanged: ({
              required bool hasAccounts,
              required bool hasWallets,
              required bool isCurrentTabEmpty,
              required int currentTabIndex,
            }) {
              // Handle empty state changes in test
            },
            isInputDisabled: false,
          ),
          onAccountSelected: (selectedAccount) {
            // Handle account selection in test
          },
        ),
      ),
    );
  }
}

class ListOfAccountsWithSelectionTestData extends StatelessWidget {
  const ListOfAccountsWithSelectionTestData({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Account List - With Selection')),
      body: Padding(
        padding: EdgeInsetsGeometry.all(16),
        child: AccountListWidget(
          args: (
            selectByHighestBalance: true,
            excludeAccountNumber: null,
            onEmptyStateChanged: ({
              required bool hasAccounts,
              required bool hasWallets,
              required bool isCurrentTabEmpty,
              required int currentTabIndex,
            }) {
              // Handle empty state changes in test
            },
            isInputDisabled: false,
          ),
          onAccountSelected: (selectedAccount) {
            // Handle account selection in test
            debugPrint('Selected account: ${selectedAccount.name}');
          },
        ),
      ),
    );
  }
}
