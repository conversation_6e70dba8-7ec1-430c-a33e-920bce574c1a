// ignore_for_file: prefer-match-file-name, prefer-single-widget-per-file

import 'package:flutter/material.dart';
import 'package:payment/payments.dart';
import 'package:payment/src/domain/data/payment_type.dart';

// Global variables to store the last callback parameters for testing with proper type safety
DepositWithdrawAmountConversionCallback? lastDepositWithdrawCallback;
TransferAmountConversionCallback? lastTransferCallback;

// Helper class to store callback parameters in a type-safe way
class CallbackParams {
  // For DepositWithdraw callbacks
  String? selectedCurrencyAmount;
  String? accountCurrencyAmount;
  String? selectedCurrency;
  String? accountCurrency;
  RatesModel? conversionRateSelectedToAccountCurrency;
  String? conversionRateString;
  ConversionRateModel? conversionRateData;
  bool? isAmountValid;

  // For Transfer callbacks
  String? amountInSourceCurrency;
  String? amountInDestinationCurrency;
  RatesModel? conversionRateToDestinationCurrency;

  CallbackParams();

  // Factory for DepositWithdraw callbacks
  CallbackParams.fromDepositWithdraw({
    required this.selectedCurrencyAmount,
    required this.accountCurrencyAmount,
    this.selectedCurrency,
    required this.accountCurrency,
    required this.conversionRateSelectedToAccountCurrency,
    this.conversionRateString,
    required this.conversionRateData,
    required this.isAmountValid,
  });

  // Factory for Transfer callbacks
  CallbackParams.fromTransfer({
    required this.amountInSourceCurrency,
    required this.amountInDestinationCurrency,
    this.selectedCurrency,
    required this.conversionRateToDestinationCurrency,
    this.conversionRateString,
    required this.conversionRateData,
    required this.isAmountValid,
  }) : conversionRateSelectedToAccountCurrency =
           conversionRateToDestinationCurrency;
}

// Global variable to store the last callback parameters for testing
CallbackParams? lastCallbackParams;

/// Test data for DepositWithdrawAmountConversionWidget with deposit payment type
/// without currency conversion enabled - functional test version
class DepositWithoutConversionFunctionalTestData extends StatefulWidget {
  const DepositWithoutConversionFunctionalTestData({super.key});

  @override
  State<DepositWithoutConversionFunctionalTestData> createState() =>
      _DepositWithoutConversionFunctionalTestDataState();
}

class _DepositWithoutConversionFunctionalTestDataState
    extends State<DepositWithoutConversionFunctionalTestData> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: DepositWithdrawAmountConversionWidget(
          args: (
            account: null,
            accountCurrency: 'USD',
            currencyMinMaxSuggestedAmountList: [
              CurrencyAmountDetail(
                currency: 'USD',
                suggestedAmounts: [50, 100, 150],
                minAmount: 1,
                maxAmount: 2000,
              ),
            ],
            currencies: ['USD'],
            showSuggestedAmounts: true,
            isStartWithConversionRate: false,
            selectedCurrency: null,
            externalErrorMessage: null,
            paymentType: PaymentType.deposit,
            premierAccountMinAmountForDeposit: null,
            isInputDisabled: false,
          ),
          callback: ({
            required String selectedCurrencyAmount,
            required String accountCurrencyAmount,
            String? selectedCurrency,
            required RatesModel? conversionRateSelectedToAccountCurrency,
            String? conversionRateString,
            required ConversionRateModel? conversionRateData,
            required bool isAmountValid,
          }) {
            // Store in global variable for testing with proper type safety
            lastCallbackParams = CallbackParams.fromDepositWithdraw(
              selectedCurrencyAmount: selectedCurrencyAmount,
              accountCurrencyAmount: accountCurrencyAmount,
              selectedCurrency: selectedCurrency,
              conversionRateSelectedToAccountCurrency:
                  conversionRateSelectedToAccountCurrency,
              conversionRateString: conversionRateString,
              accountCurrency: 'USD',
              conversionRateData: conversionRateData,
              isAmountValid: isAmountValid,
            );
          },
        ),
      ),
    );
  }
}

/// Test data for DepositWithdrawAmountConversionWidget with deposit payment type
/// with currency conversion enabled - functional test version
class DepositWithConversionFunctionalTestData extends StatefulWidget {
  const DepositWithConversionFunctionalTestData({super.key});

  @override
  State<DepositWithConversionFunctionalTestData> createState() =>
      _DepositWithConversionFunctionalTestDataState();
}

class _DepositWithConversionFunctionalTestDataState
    extends State<DepositWithConversionFunctionalTestData> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: DepositWithdrawAmountConversionWidget(
          args: (
            account: null,
            accountCurrency: 'USD',
            currencyMinMaxSuggestedAmountList: [
              CurrencyAmountDetail(
                currency: 'AED',
                suggestedAmounts: [500, 1000, 1600],
                minAmount: 1,
                maxAmount: 1000,
              ),
              CurrencyAmountDetail(
                currency: 'USD',
                suggestedAmounts: [50, 100, 150],
                minAmount: 1,
                maxAmount: 2000,
              ),
            ],
            currencies: ['USD', 'AED'],
            showSuggestedAmounts: true,
            isStartWithConversionRate: true,
            selectedCurrency: 'AED',
            externalErrorMessage: null,
            paymentType: PaymentType.deposit,
            premierAccountMinAmountForDeposit: null,
            isInputDisabled: false,
          ),
          callback: ({
            required String selectedCurrencyAmount,
            required String accountCurrencyAmount,
            String? selectedCurrency,
            required RatesModel? conversionRateSelectedToAccountCurrency,
            String? conversionRateString,
            required ConversionRateModel? conversionRateData,
            required bool isAmountValid,
          }) {
            // Store in global variable for testing with proper type safety
            lastCallbackParams = CallbackParams.fromDepositWithdraw(
              selectedCurrencyAmount: selectedCurrencyAmount,
              accountCurrencyAmount: accountCurrencyAmount,
              selectedCurrency: selectedCurrency,
              conversionRateSelectedToAccountCurrency:
                  conversionRateSelectedToAccountCurrency,
              conversionRateString: conversionRateString,
              conversionRateData: conversionRateData,
              accountCurrency: 'USD',
              isAmountValid: isAmountValid,
            );
          },
        ),
      ),
    );
  }
}

/// Test data for DepositWithdrawAmountConversionWidget with withdrawal payment type
/// without currency conversion enabled - functional test version
class WithdrawalWithoutConversionFunctionalTestData extends StatefulWidget {
  const WithdrawalWithoutConversionFunctionalTestData({super.key});

  @override
  State<WithdrawalWithoutConversionFunctionalTestData> createState() =>
      _WithdrawalWithoutConversionFunctionalTestDataState();
}

class _WithdrawalWithoutConversionFunctionalTestDataState
    extends State<WithdrawalWithoutConversionFunctionalTestData> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: DepositWithdrawAmountConversionWidget(
          args: (
            account: null,
            accountCurrency: 'USD',
            currencyMinMaxSuggestedAmountList: [
              CurrencyAmountDetail(
                currency: 'USD',
                suggestedAmounts: [50, 100, 150],
                minAmount: 1,
                maxAmount: 2000,
              ),
            ],
            currencies: ['USD'],
            showSuggestedAmounts: true,
            isStartWithConversionRate: false,
            selectedCurrency: null,
            externalErrorMessage: null,
            paymentType: PaymentType.withdrawal,
            premierAccountMinAmountForDeposit: null,
            isInputDisabled: false,
          ),
          callback: ({
            required String selectedCurrencyAmount,
            required String accountCurrencyAmount,
            String? selectedCurrency,
            required RatesModel? conversionRateSelectedToAccountCurrency,
            String? conversionRateString,
            required ConversionRateModel? conversionRateData,
            required bool isAmountValid,
          }) {
            // Store in global variable for testing with proper type safety
            lastCallbackParams = CallbackParams.fromDepositWithdraw(
              selectedCurrencyAmount: selectedCurrencyAmount,
              accountCurrencyAmount: accountCurrencyAmount,
              selectedCurrency: selectedCurrency,
              conversionRateSelectedToAccountCurrency:
                  conversionRateSelectedToAccountCurrency,
              conversionRateString: conversionRateString,
              conversionRateData: conversionRateData,
              accountCurrency: 'USD',
              isAmountValid: isAmountValid,
            );
          },
        ),
      ),
    );
  }
}

/// Test data for DepositWithdrawAmountConversionWidget with withdrawal payment type
/// with currency conversion enabled - functional test version
class WithdrawalWithConversionFunctionalTestData extends StatefulWidget {
  const WithdrawalWithConversionFunctionalTestData({super.key});

  @override
  State<WithdrawalWithConversionFunctionalTestData> createState() =>
      _WithdrawalWithConversionFunctionalTestDataState();
}

class _WithdrawalWithConversionFunctionalTestDataState
    extends State<WithdrawalWithConversionFunctionalTestData> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: DepositWithdrawAmountConversionWidget(
          args: (
            account: null,
            accountCurrency: 'USD',
            currencyMinMaxSuggestedAmountList: [
              CurrencyAmountDetail(
                currency: 'AED',
                suggestedAmounts: [500, 1000, 1600],
                minAmount: 1,
                maxAmount: 1000,
              ),
              CurrencyAmountDetail(
                currency: 'USD',
                suggestedAmounts: [50, 100, 150],
                minAmount: 1,
                maxAmount: 2000,
              ),
            ],
            currencies: ['USD', 'AED'],
            showSuggestedAmounts: true,
            isStartWithConversionRate: true,
            selectedCurrency: 'AED',
            externalErrorMessage: null,
            paymentType: PaymentType.withdrawal,
            premierAccountMinAmountForDeposit: null,
            isInputDisabled: false,
          ),
          callback: ({
            required String selectedCurrencyAmount,
            required String accountCurrencyAmount,
            String? selectedCurrency,
            required RatesModel? conversionRateSelectedToAccountCurrency,
            String? conversionRateString,
            required ConversionRateModel? conversionRateData,
            required bool isAmountValid,
          }) {
            // Store in global variable for testing with proper type safety
            lastCallbackParams = CallbackParams.fromDepositWithdraw(
              selectedCurrencyAmount: selectedCurrencyAmount,
              accountCurrencyAmount: accountCurrencyAmount,
              selectedCurrency: selectedCurrency,
              conversionRateSelectedToAccountCurrency:
                  conversionRateSelectedToAccountCurrency,
              conversionRateString: conversionRateString,
              accountCurrency: 'USD',
              conversionRateData: conversionRateData,
              isAmountValid: isAmountValid,
            );
          },
        ),
      ),
    );
  }
}

/// Test data for TransferAmountConversionWidget with transfer payment type
/// without currency conversion enabled - functional test version
class TransferWithoutConversionFunctionalTestData extends StatefulWidget {
  const TransferWithoutConversionFunctionalTestData({super.key});

  @override
  State<TransferWithoutConversionFunctionalTestData> createState() =>
      _TransferWithoutConversionFunctionalTestDataState();
}

class _TransferWithoutConversionFunctionalTestDataState
    extends State<TransferWithoutConversionFunctionalTestData> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: TransferAmountConversionWidget(
          args: (
            account: null,
            sourceCurrency: 'USD',
            destinationCurrency: null,
            currencyMinMaxSuggestedAmountList: [
              CurrencyAmountDetail(
                currency: 'USD',
                suggestedAmounts: [50, 100, 150],
                minAmount: 1,
                maxAmount: 2000,
              ),
            ],
            currencies: ['USD'],
            showSuggestedAmounts: true,
            isStartWithConversionRate: false,
            externalErrorMessage: null,
            paymentType: PaymentType.transfer,
            isInputDisabled: false,
          ),
          callback: ({
            required String amountInSourceCurrency,
            required String amountInDestinationCurrency,
            String? selectedCurrency,
            required RatesModel? conversionRateToDestinationCurrency,
            String? conversionRateString,
            required ConversionRateModel? conversionRateData,
            required bool isAmountValid,
          }) {
            // Store in global variable for testing with proper type safety
            lastCallbackParams = CallbackParams.fromTransfer(
              amountInSourceCurrency: amountInSourceCurrency,
              amountInDestinationCurrency: amountInDestinationCurrency,
              selectedCurrency: selectedCurrency,
              conversionRateToDestinationCurrency:
                  conversionRateToDestinationCurrency,
              conversionRateString: conversionRateString,
              conversionRateData: conversionRateData,
              isAmountValid: isAmountValid,
            );
          },
        ),
      ),
    );
  }
}

/// Test data for TransferAmountConversionWidget with transfer payment type
/// with currency conversion enabled - functional test version
class TransferWithConversionFunctionalTestData extends StatefulWidget {
  const TransferWithConversionFunctionalTestData({super.key});

  @override
  State<TransferWithConversionFunctionalTestData> createState() =>
      _TransferWithConversionFunctionalTestDataState();
}

class _TransferWithConversionFunctionalTestDataState
    extends State<TransferWithConversionFunctionalTestData> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: TransferAmountConversionWidget(
          args: (
            account: null,
            sourceCurrency: 'USD',
            destinationCurrency: 'AED',
            currencyMinMaxSuggestedAmountList: [
              CurrencyAmountDetail(
                currency: 'AED',
                suggestedAmounts: [500, 1000, 1600],
                minAmount: 1,
                maxAmount: 1000,
              ),
              CurrencyAmountDetail(
                currency: 'USD',
                suggestedAmounts: [50, 100, 150],
                minAmount: 1,
                maxAmount: 2000,
              ),
            ],
            currencies: ['USD', 'AED'],
            showSuggestedAmounts: true,
            isStartWithConversionRate: true,
            externalErrorMessage: null,
            paymentType: PaymentType.transfer,
            isInputDisabled: false,
          ),
          callback: ({
            required String amountInSourceCurrency,
            required String amountInDestinationCurrency,
            String? selectedCurrency,
            required RatesModel? conversionRateToDestinationCurrency,
            String? conversionRateString,
            required ConversionRateModel? conversionRateData,
            required bool isAmountValid,
          }) {
            // Store in global variable for testing with proper type safety
            lastCallbackParams = CallbackParams.fromTransfer(
              amountInSourceCurrency: amountInSourceCurrency,
              amountInDestinationCurrency: amountInDestinationCurrency,
              selectedCurrency: selectedCurrency,
              conversionRateToDestinationCurrency:
                  conversionRateToDestinationCurrency,
              conversionRateString: conversionRateString,
              conversionRateData: conversionRateData,
              isAmountValid: isAmountValid,
            );
          },
        ),
      ),
    );
  }
}
