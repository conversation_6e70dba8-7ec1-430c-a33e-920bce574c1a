import 'package:flutter/material.dart';
import 'package:payment/payments.dart';
import 'package:payment/src/domain/data/payment_type.dart';

class DepositAmountTestData extends StatefulWidget {
  const DepositAmountTestData({super.key});

  @override
  State<DepositAmountTestData> createState() => _DepositAmountTestDataState();
}

class _DepositAmountTestDataState extends State<DepositAmountTestData> {
  final TextEditingController transferAmountController =
      TextEditingController();
  final TextEditingController convertedAmountController =
      TextEditingController();

  @override
  void dispose() {
    transferAmountController.dispose();
    convertedAmountController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: DepositWithdrawAmountConversionWidget(
          args: (
            account: null,
            accountCurrency: 'USD',
            currencyMinMaxSuggestedAmountList: [
              CurrencyAmountDetail(
                currency: 'AED',
                suggestedAmounts: [500, 1000, 1600],
                minAmount: 1,
                maxAmount: 1000,
              ),
              CurrencyAmountDetail(
                currency: 'USD',
                suggestedAmounts: [50, 100, 150],
                minAmount: 1,
                maxAmount: 2000,
              ),
            ],
            currencies: ['USD', 'AED'],
            showSuggestedAmounts: true,
            isStartWithConversionRate: false,
            selectedCurrency: null,
            externalErrorMessage: null,
            paymentType: PaymentType.deposit,
            premierAccountMinAmountForDeposit: null,
            isInputDisabled: false,
          ),
          callback:
              ({
                required String accountCurrencyAmount,
                required String selectedCurrencyAmount,
                String? selectedCurrency,
                required bool isAmountValid,
                required RatesModel? conversionRateSelectedToAccountCurrency,
                String? conversionRateString,
                required ConversionRateModel? conversionRateData,
              }) {},
        ),
      ),
    );
  }
}
